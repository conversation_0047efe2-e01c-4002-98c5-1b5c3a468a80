// ============================================================================
// CONTRIBUTION GRAPH UTILITIES
// ============================================================================
// Utilities for processing Codeforces submissions into contribution graph data

import type { CodeforcesSubmission } from "@/lib/codeforces";

export interface ContributionDay {
  date: string; // YYYY-MM-DD format
  count: number; // Number of problems solved
  maxRating: number; // Highest rated problem solved that day
  level: number; // Intensity level (0-4) for color coding
}

export interface ContributionData {
  days: ContributionDay[];
  totalSolved: number;
  maxStreak: number;
  currentStreak: number;
}

/**
 * Get color based on problem rating using exact Codeforces rating colors
 */
export const getRatingBasedColor = (rating: number, level: number): string => {
  if (level === 0) return "rgb(22, 27, 34)"; // No activity - dark background

  // Use exact Codeforces rating colors with intensity-based opacity
  let baseColor: string;

  if (rating >= 3000) {
    baseColor = "#FF0000"; // Legendary Grandmaster (red)
  } else if (rating >= 2600) {
    baseColor = "#FF8C00"; // International Grandmaster (orange)
  } else if (rating >= 2400) {
    baseColor = "#FF0000"; // Grandmaster (red)
  } else if (rating >= 2300) {
    baseColor = "#FF8C00"; // International Master (orange)
  } else if (rating >= 2100) {
    baseColor = "#FFCC00"; // Master (yellow)
  } else if (rating >= 1900) {
    baseColor = "#FF88FF"; // Candidate Master (violet)
  } else if (rating >= 1600) {
    baseColor = "#0077FF"; // Expert (blue)
  } else if (rating >= 1400) {
    baseColor = "#00AAAA"; // Specialist (cyan)
  } else if (rating >= 1200) {
    baseColor = "#008800"; // Pupil (green)
  } else {
    baseColor = "#808080"; // Newbie (gray)
  }

  // Apply intensity-based opacity for better visibility
  const intensity = Math.min(level / 4, 1);
  const opacity = 0.6 + intensity * 0.4; // Range from 0.6 to 1.0 for better visibility

  // Convert hex to rgba
  const hex = baseColor.replace("#", "");
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Get intensity level (0-4) based on number of problems solved
 */
export const getIntensityLevel = (count: number): number => {
  if (count === 0) return 0;
  if (count === 1) return 1;
  if (count <= 3) return 2;
  if (count <= 6) return 3;
  return 4; // 7+ problems
};

/**
 * Process Codeforces submissions into contribution graph data
 */
export const processSubmissionsToContributions = (
  submissions: CodeforcesSubmission[]
): ContributionData => {
  // Filter only solved submissions (verdict === "OK")
  const solvedSubmissions = submissions.filter(
    (submission) => submission.verdict === "OK"
  );

  // Group submissions by date
  const dailyData = new Map<
    string,
    { count: number; maxRating: number; problems: Set<string> }
  >();

  solvedSubmissions.forEach((submission) => {
    const date = new Date(submission.creationTimeSeconds * 1000)
      .toISOString()
      .split("T")[0]; // YYYY-MM-DD format

    // Create unique problem identifier
    const problemId = `${submission.problem.contestId}-${submission.problem.index}`;

    if (!dailyData.has(date)) {
      dailyData.set(date, { count: 0, maxRating: 0, problems: new Set() });
    }

    const dayData = dailyData.get(date)!;

    // Only count unique problems per day
    if (!dayData.problems.has(problemId)) {
      dayData.problems.add(problemId);
      dayData.count++;

      // Update max rating for the day
      const problemRating = submission.problem.rating || 0;
      if (problemRating > dayData.maxRating) {
        dayData.maxRating = problemRating;
      }
    }
  });

  // Generate last 365 days
  const days: ContributionDay[] = [];
  const today = new Date();
  const oneYearAgo = new Date(today);
  oneYearAgo.setFullYear(today.getFullYear() - 1);

  for (let d = new Date(oneYearAgo); d <= today; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split("T")[0];
    const dayData = dailyData.get(dateStr);

    const contributionDay: ContributionDay = {
      date: dateStr,
      count: dayData?.count || 0,
      maxRating: dayData?.maxRating || 0,
      level: getIntensityLevel(dayData?.count || 0),
    };

    days.push(contributionDay);
  }

  // Calculate streaks
  let currentStreak = 0;
  let maxStreak = 0;
  let tempStreak = 0;

  // Calculate current streak (from today backwards)
  for (let i = days.length - 1; i >= 0; i--) {
    if (days[i].count > 0) {
      currentStreak++;
    } else {
      break;
    }
  }

  // Calculate max streak
  for (const day of days) {
    if (day.count > 0) {
      tempStreak++;
      maxStreak = Math.max(maxStreak, tempStreak);
    } else {
      tempStreak = 0;
    }
  }

  return {
    days,
    totalSolved: Array.from(
      new Set(
        solvedSubmissions.map(
          (s) => `${s.problem.contestId}-${s.problem.index}`
        )
      )
    ).length,
    maxStreak,
    currentStreak,
  };
};

/**
 * Process Codeforces submissions into contribution graph data for a specific year
 */
export const processSubmissionsToContributionsForYear = (
  submissions: CodeforcesSubmission[],
  year: number
): ContributionData => {
  // Filter only solved submissions (verdict === "OK")
  const solvedSubmissions = submissions.filter(
    (submission) => submission.verdict === "OK"
  );

  // Group submissions by date
  const dailyData = new Map<
    string,
    { count: number; maxRating: number; problems: Set<string> }
  >();

  solvedSubmissions.forEach((submission) => {
    const date = new Date(submission.creationTimeSeconds * 1000)
      .toISOString()
      .split("T")[0]; // YYYY-MM-DD format

    // Create unique problem identifier
    const problemId = `${submission.problem.contestId}-${submission.problem.index}`;

    if (!dailyData.has(date)) {
      dailyData.set(date, { count: 0, maxRating: 0, problems: new Set() });
    }

    const dayData = dailyData.get(date)!;

    // Only count unique problems per day
    if (!dayData.problems.has(problemId)) {
      dayData.problems.add(problemId);
      dayData.count++;

      // Update max rating for the day
      const problemRating = submission.problem.rating || 0;
      if (problemRating > dayData.maxRating) {
        dayData.maxRating = problemRating;
      }
    }
  });

  // Generate all days for the specified year
  const days: ContributionDay[] = [];
  const startOfYear = new Date(year, 0, 1); // January 1st of the year
  const endOfYear = new Date(year, 11, 31); // December 31st of the year

  for (
    let d = new Date(startOfYear);
    d <= endOfYear;
    d.setDate(d.getDate() + 1)
  ) {
    const dateStr = d.toISOString().split("T")[0];
    const dayData = dailyData.get(dateStr);

    const contributionDay: ContributionDay = {
      date: dateStr,
      count: dayData?.count || 0,
      maxRating: dayData?.maxRating || 0,
      level: getIntensityLevel(dayData?.count || 0),
    };

    days.push(contributionDay);
  }

  // Calculate streaks for the year
  let currentStreak = 0;
  let maxStreak = 0;
  let tempStreak = 0;

  // Calculate current streak (from end of year backwards)
  for (let i = days.length - 1; i >= 0; i--) {
    if (days[i].count > 0) {
      currentStreak++;
    } else {
      break;
    }
  }

  // Calculate max streak for the year
  for (const day of days) {
    if (day.count > 0) {
      tempStreak++;
      maxStreak = Math.max(maxStreak, tempStreak);
    } else {
      tempStreak = 0;
    }
  }

  return {
    days,
    totalSolved: Array.from(
      new Set(
        solvedSubmissions
          .filter((s) => {
            const submissionDate = new Date(s.creationTimeSeconds * 1000);
            return submissionDate.getFullYear() === year;
          })
          .map((s) => `${s.problem.contestId}-${s.problem.index}`)
      )
    ).length,
    maxStreak,
    currentStreak,
  };
};

/**
 * Get week start dates for the contribution graph
 */
export const getWeekStartDates = (days: ContributionDay[]): Date[] => {
  const weeks: Date[] = [];
  const firstDay = new Date(days[0].date);

  // Find the first Sunday (or Monday, depending on preference)
  const firstSunday = new Date(firstDay);
  firstSunday.setDate(firstDay.getDate() - firstDay.getDay());

  for (let i = 0; i < Math.ceil(days.length / 7); i++) {
    const weekStart = new Date(firstSunday);
    weekStart.setDate(firstSunday.getDate() + i * 7);
    weeks.push(weekStart);
  }

  return weeks;
};

/**
 * Get month labels for the contribution graph with proper positioning
 */
export const getMonthLabels = (
  days: ContributionDay[]
): Array<{ month: string; weekIndex: number; width: number }> => {
  const months: Array<{ month: string; weekIndex: number; width: number }> = [];
  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  if (days.length === 0) return months;

  let currentMonth = -1;
  let monthStartWeek = 0;
  let weekIndex = 0;

  // Process each week to find month boundaries
  for (let i = 0; i < days.length; i += 7) {
    const weekDays = days.slice(i, i + 7);
    // Find the first day of the week that has a valid date
    const firstValidDay = weekDays.find((day) => day.date !== "");

    if (firstValidDay) {
      const date = new Date(firstValidDay.date);
      const month = date.getMonth();

      if (month !== currentMonth) {
        // If we have a previous month, calculate its width
        if (currentMonth !== -1 && months.length > 0) {
          months[months.length - 1].width = weekIndex - monthStartWeek;
        }

        currentMonth = month;
        monthStartWeek = weekIndex;
        months.push({
          month: monthNames[month],
          weekIndex: weekIndex,
          width: 1, // Will be updated when we find the next month
        });
      }
    }
    weekIndex++;
  }

  // Set width for the last month
  if (months.length > 0) {
    months[months.length - 1].width = weekIndex - monthStartWeek;
  }

  return months;
};

/**
 * Create a GitHub-style contribution grid with proper week alignment
 */
export const createContributionGrid = (
  days: ContributionDay[]
): ContributionDay[][] => {
  if (days.length === 0) return [];

  // Find the first day and determine what day of the week it is
  const firstDate = new Date(days[0].date);
  const firstDayOfWeek = firstDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

  // Create empty days to fill the first week if needed
  const grid: ContributionDay[][] = [];
  let currentWeek: ContributionDay[] = [];

  // Fill empty days at the beginning of the first week
  for (let i = 0; i < firstDayOfWeek; i++) {
    currentWeek.push({
      date: "",
      count: 0,
      maxRating: 0,
      level: 0,
    });
  }

  // Add all the actual days
  days.forEach((day, index) => {
    currentWeek.push(day);

    // If we've filled a week (7 days), start a new week
    if (currentWeek.length === 7) {
      grid.push(currentWeek);
      currentWeek = [];
    }
  });

  // Fill the last week if it's not complete
  while (currentWeek.length > 0 && currentWeek.length < 7) {
    currentWeek.push({
      date: "",
      count: 0,
      maxRating: 0,
      level: 0,
    });
  }

  if (currentWeek.length > 0) {
    grid.push(currentWeek);
  }

  return grid;
};

/**
 * Get available years from submissions data
 */
export const getAvailableYears = (
  submissions: CodeforcesSubmission[]
): number[] => {
  const years = new Set<number>();

  submissions.forEach((submission) => {
    if (submission.verdict === "OK") {
      const year = new Date(
        submission.creationTimeSeconds * 1000
      ).getFullYear();
      years.add(year);
    }
  });

  return Array.from(years).sort((a, b) => b - a); // Sort descending (newest first)
};
