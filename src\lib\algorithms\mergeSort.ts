import {
  ArrayElement,
  MergeTreeGenerator,
  MergeTreeNode,
  MergeTreeState,
} from "../types";

// Utility function to generate unique IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Utility function to create array elements with unique IDs
const createArrayElements = (values: number[]): ArrayElement[] => {
  return values.map((value, index) => ({
    value,
    id: `${value}-${index}-${generateId()}`,
  }));
};

// Utility to create a deep copy of array elements with new IDs
const createSubArrayElements = (elements: ArrayElement[]): ArrayElement[] => {
  return elements.map((el) => ({
    ...el,
    id: `${el.value}-${generateId()}`, // Keep value, new unique ID
    isComparing: false,
    isSorted: false,
  }));
};

// Calculate positions for tree nodes with optimal spacing
const calculateNodePositions = (nodes: Map<string, MergeTreeNode>) => {
  nodes.forEach((node) => {
    const { depth, position } = node;
    // The number of potential nodes at this depth level
    const numNodesAtLevel = Math.pow(2, depth);
    // The horizontal space allocated for each node slot
    const spacing = 100 / numNodesAtLevel;
    // Center the node within its allocated slot
    node.x = spacing * (position + 0.5);
    // Position vertically with consistent spacing
    node.y = depth * 140 + 80;
  });
};

export function* mergeSort(array: ArrayElement[]): MergeTreeGenerator {
  const nodes = new Map<string, MergeTreeNode>();
  let maxDepth = 0;
  let currentStep = 0;

  // Helper function to yield current state
  const yieldState = (): MergeTreeState => {
    calculateNodePositions(nodes);
    return {
      nodes: new Map(nodes),
      currentStep: currentStep++,
      maxDepth,
      isComplete: false,
    };
  };

  // Create root node
  const rootId = generateId();
  const rootNode: MergeTreeNode = {
    id: rootId,
    array: createSubArrayElements(array),
    depth: 0,
    position: 0,
    state: "dividing",
    isActive: true,
  };
  nodes.set(rootId, rootNode);

  // Calculate maximum depth
  const calculateMaxDepth = (length: number): number => {
    return Math.ceil(Math.log2(length));
  };

  maxDepth = calculateMaxDepth(array.length);

  // Initial state
  yield yieldState();

  // Recursive function to build the tree structure
  function* buildTree(
    nodeId: string,
    arr: ArrayElement[],
    depth: number,
    position: number
  ): Generator<MergeTreeState, ArrayElement[], void> {
    const currentNode = nodes.get(nodeId)!;

    if (arr.length <= 1) {
      // Base case: single element or empty array
      currentNode.state = "complete";
      currentNode.isActive = false;
      yield yieldState();
      return arr;
    }

    // Mark current node as dividing
    currentNode.state = "dividing";
    currentNode.isActive = true;
    yield yieldState();

    // Divide the array
    const mid = Math.floor(arr.length / 2);
    const leftArray = arr.slice(0, mid);
    const rightArray = arr.slice(mid);

    // Create child nodes with deep-copied elements
    const leftChildId = generateId();
    const rightChildId = generateId();

    const leftChild: MergeTreeNode = {
      id: leftChildId,
      array: createSubArrayElements(leftArray),
      depth: depth + 1,
      position: position * 2,
      parentId: nodeId,
      state: "dividing",
    };

    const rightChild: MergeTreeNode = {
      id: rightChildId,
      array: createSubArrayElements(rightArray),
      depth: depth + 1,
      position: position * 2 + 1,
      parentId: nodeId,
      state: "dividing",
    };

    nodes.set(leftChildId, leftChild);
    nodes.set(rightChildId, rightChild);

    // Update parent node with child references
    currentNode.leftChildId = leftChildId;
    currentNode.rightChildId = rightChildId;
    currentNode.state = "divided";
    currentNode.isActive = false;

    yield yieldState();

    // Recursively sort left and right halves
    const sortedLeft = yield* buildTree(
      leftChildId,
      leftChild.array,
      depth + 1,
      position * 2
    );
    const sortedRight = yield* buildTree(
      rightChildId,
      rightChild.array,
      depth + 1,
      position * 2 + 1
    );

    // Merge the sorted halves
    currentNode.state = "merging";
    currentNode.isActive = true;
    yield yieldState();

    const merged = yield* merge(sortedLeft, sortedRight, nodeId);

    // Update current node with merged result
    currentNode.array = merged;
    currentNode.state = "merged";
    currentNode.isActive = false;
    yield yieldState();

    return merged;
  }

  // Merge function with enhanced visualization
  function* merge(
    left: ArrayElement[],
    right: ArrayElement[],
    parentNodeId: string
  ): Generator<MergeTreeState, ArrayElement[], void> {
    const result: ArrayElement[] = [];
    let leftIndex = 0;
    let rightIndex = 0;
    const parentNode = nodes.get(parentNodeId)!;
    const leftChild = nodes.get(parentNode.leftChildId!)!;
    const rightChild = nodes.get(parentNode.rightChildId!)!;

    // Mark child nodes as being compared during the entire merge process
    leftChild.isBeingCompared = true;
    rightChild.isBeingCompared = true;
    yield yieldState();

    // Step-by-step merge with highlighting
    while (leftIndex < left.length && rightIndex < right.length) {
      // Clear previous element highlighting
      leftChild.array.forEach((el) => (el.isComparing = false));
      rightChild.array.forEach((el) => (el.isComparing = false));

      // Highlight elements being compared
      if (leftChild.array[leftIndex]) {
        leftChild.array[leftIndex].isComparing = true;
      }
      if (rightChild.array[rightIndex]) {
        rightChild.array[rightIndex].isComparing = true;
      }
      parentNode.comparisonText = `${left[leftIndex].value} <= ${right[rightIndex].value}`;

      // Show the comparison with highlighting
      yield yieldState();

      // Now proceed with the merge decision
      if (left[leftIndex].value <= right[rightIndex].value) {
        result.push({ ...left[leftIndex], isComparing: false, isSorted: true });
        leftIndex++;
      } else {
        result.push({
          ...right[rightIndex],
          isComparing: false,
          isSorted: true,
        });
        rightIndex++;
      }

      // Update parent with merged part
      parentNode.array = createSubArrayElements(result);

      // Clear comparison highlights and text after updating
      leftChild.array.forEach((el) => (el.isComparing = false));
      rightChild.array.forEach((el) => (el.isComparing = false));
      parentNode.comparisonText = undefined;
      yield yieldState();
    }

    // Add remaining elements from left or right
    while (leftIndex < left.length) {
      result.push({ ...left[leftIndex], isSorted: true });
      leftIndex++;
      parentNode.array = createSubArrayElements(result);
      yield yieldState();
    }
    while (rightIndex < right.length) {
      result.push({ ...right[rightIndex], isSorted: true });
      rightIndex++;
      parentNode.array = createSubArrayElements(result);
      yield yieldState();
    }

    // Final update to parent and children state
    leftChild.state = "merged";
    rightChild.state = "merged";
    leftChild.isBeingCompared = false;
    rightChild.isBeingCompared = false;
    parentNode.comparisonText = undefined;
    yield yieldState();

    return result;
  }

  // Start the process
  yield* buildTree(rootId, rootNode.array, 0, 0);

  // Final step: mark root as complete
  const finalRoot = nodes.get(rootId)!;
  finalRoot.state = "complete";
  finalRoot.isActive = false;

  // Mark all elements in the root node as sorted for final display
  finalRoot.array.forEach((el) => (el.isSorted = true));

  yield {
    nodes: new Map(nodes),
    currentStep,
    maxDepth,
    isComplete: true,
  };
}
