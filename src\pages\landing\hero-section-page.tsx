"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const HeroSection = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center px-4 md:px-6 animate-in fade-in duration-1000">
      {/* Main Heading with Codeforces-inspired Gradient */}
      <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-wide mb-4 leading-tight mt-4">
        <span className="text-white bg-clip-text">MyCPTrainer</span>
      </h1>

      {/* Subheading */}
      <h2 className="text-lg sm:text-xl md:text-2xl font-light tracking-wider text-slate-300 mb-12 opacity-90">
        Precision Over Randomness
      </h2>

      {/* Call to Action */}
      <div className="group">
        <Link href="/sheetscope">
          <Button
            variant="outline"
            className="py-3 px-8 rounded-full border-2 border-blue-400/50 text-white hover:bg-blue-500/20 hover:border-blue-300 hover:scale-105 transition-all duration-300 font-medium backdrop-blur-sm"
          >
            Get Started
            <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">
              →
            </span>
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default HeroSection;
