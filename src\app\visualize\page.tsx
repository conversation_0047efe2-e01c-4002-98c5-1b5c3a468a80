"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Grid } from "@/components/magicui/bento-grid";
import {
  BinaryIcon,
  DatabaseIcon,
  MapIcon,
  NetworkIcon,
  TreePineIcon,
  TrendingUpIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";

const visualizers = [
  {
    name: "Binary Trees",
    description:
      "Visualize binary search trees, AVL trees, and heap structures with interactive node manipulation",
    href: "/visualize/tree",
    cta: "Explore Trees",
    Icon: TreePineIcon,
    className: "col-span-3 lg:col-span-2",
    background: (
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-green-600/20">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_70%)]" />
        <div className="absolute top-4 left-4 w-3 h-3 bg-emerald-400 rounded-full animate-pulse" />
        <div className="absolute top-8 left-12 w-2 h-2 bg-emerald-300 rounded-full animate-pulse delay-1000" />
        <div className="absolute top-8 left-20 w-2 h-2 bg-emerald-300 rounded-full animate-pulse delay-500" />
        <div className="absolute top-12 left-8 w-1.5 h-1.5 bg-emerald-200 rounded-full animate-pulse delay-1500" />
        <div className="absolute top-12 left-16 w-1.5 h-1.5 bg-emerald-200 rounded-full animate-pulse delay-2000" />
        <div className="absolute top-12 left-24 w-1.5 h-1.5 bg-emerald-200 rounded-full animate-pulse delay-750" />
      </div>
    ),
  },
  {
    name: "Graph Networks",
    description:
      "Interactive graph visualization with shortest path algorithms and network analysis",
    href: "/visualize/graph",
    cta: "Explore Graphs",
    Icon: NetworkIcon,
    className: "col-span-3 lg:col-span-1",
    background: (
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-transparent to-purple-600/20">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(59,130,246,0.1),transparent_50%)]" />
        <svg
          className="absolute inset-0 w-full h-full opacity-30"
          viewBox="0 0 100 100"
        >
          <circle
            cx="20"
            cy="20"
            r="2"
            fill="rgb(59,130,246)"
            className="animate-pulse"
          />
          <circle
            cx="80"
            cy="30"
            r="2"
            fill="rgb(147,51,234)"
            className="animate-pulse delay-500"
          />
          <circle
            cx="60"
            cy="70"
            r="2"
            fill="rgb(59,130,246)"
            className="animate-pulse delay-1000"
          />
          <circle
            cx="30"
            cy="80"
            r="2"
            fill="rgb(147,51,234)"
            className="animate-pulse delay-1500"
          />
          <line
            x1="20"
            y1="20"
            x2="80"
            y2="30"
            stroke="rgb(59,130,246)"
            strokeWidth="0.5"
            opacity="0.6"
          />
          <line
            x1="80"
            y1="30"
            x2="60"
            y2="70"
            stroke="rgb(147,51,234)"
            strokeWidth="0.5"
            opacity="0.6"
          />
          <line
            x1="60"
            y1="70"
            x2="30"
            y2="80"
            stroke="rgb(59,130,246)"
            strokeWidth="0.5"
            opacity="0.6"
          />
          <line
            x1="30"
            y1="80"
            x2="20"
            y2="20"
            stroke="rgb(147,51,234)"
            strokeWidth="0.5"
            opacity="0.6"
          />
        </svg>
      </div>
    ),
  },
  {
    name: "Binary Search",
    description: "Visualize binary search execution flow",
    href: "/visualize/binarysearch",
    cta: "Explore Binary Search",
    Icon: TrendingUpIcon,
    className: "col-span-3 lg:col-span-1",
    background: (
      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-transparent to-indigo-600/20">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_60%_40%,rgba(139,69,244,0.1),transparent_50%)]" />
        <div className="absolute inset-4 flex flex-col gap-2">
          {[0, 1, 2, 3, 4].map((level) => (
            <div
              key={level}
              className="flex gap-1"
              style={{ marginLeft: `${level * 8}px` }}
            >
              <div
                className="w-4 h-2 bg-gradient-to-r from-purple-400 to-indigo-400 rounded animate-pulse"
                style={{
                  animationDelay: `${level * 300}ms`,
                }}
              />
              <div
                className="w-2 h-2 bg-purple-300 rounded-full animate-pulse"
                style={{
                  animationDelay: `${level * 300 + 150}ms`,
                }}
              />
            </div>
          ))}
        </div>
      </div>
    ),
  },
  {
    name: "Sorting Algorithms",
    description:
      "Watch sorting algorithms in action with customizable speed and array sizes",
    href: "/visualize/sorting",
    cta: "Explore Sorting",
    Icon: TrendingUpIcon,
    className: "col-span-3 lg:col-span-2",
    background: (
      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/20 to-amber-600/30">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(249,115,22,0.2),transparent_60%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_30%,rgba(255,165,0,0.1)_50%,transparent_70%)]" />

        {/* Sorting visualization */}
        <div className="absolute bottom-6 left-6 flex gap-1 items-end">
          {[3, 7, 2, 8, 1, 9, 4, 6, 5].map((height, i) => (
            <div
              key={i}
              className="bg-gradient-to-t from-orange-500 via-orange-400 to-yellow-300 w-4 animate-pulse rounded-t shadow-lg border border-orange-300/30"
              style={{
                height: `${height * 4}px`,
                animationDelay: `${i * 200}ms`,
                boxShadow: `0 0 8px rgba(249, 115, 22, 0.4)`,
              }}
            />
          ))}
        </div>

        {/* Floating elements for visual interest */}
        <div className="absolute top-8 right-8 w-2 h-2 bg-orange-400 rounded-full animate-bounce opacity-60" />
        <div className="absolute top-16 right-16 w-1.5 h-1.5 bg-amber-300 rounded-full animate-bounce delay-500 opacity-60" />
        <div className="absolute top-12 right-24 w-1 h-1 bg-red-400 rounded-full animate-bounce delay-1000 opacity-60" />
      </div>
    ),
  },
];

const comingSoonVisualizers = [
  {
    name: "Dynamic Programming",
    description: "Step-by-step visualization of DP problems and memoization",
    Icon: DatabaseIcon,
  },
  {
    name: "Algorithm Complexity",
    description:
      "Interactive Big O notation visualizer with time/space analysis",
    Icon: BinaryIcon,
  },
  {
    name: "Pathfinding",
    description: "A*, Dijkstra, and BFS pathfinding algorithm visualization",
    Icon: MapIcon,
  },
];

export default function VisualizePage() {
  const router = useRouter();

  return (
    <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      {/* Beautiful gradient background matching home page style */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[600px] w-[600px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-30 blur-3xl rounded-full -z-10"></div>
      <div className="absolute top-1/3 right-1/4 h-[400px] w-[400px] bg-gradient-to-br from-purple-800/40 to-cyan-800/40 opacity-20 blur-3xl rounded-full -z-10"></div>
      <div className="absolute bottom-1/4 left-1/4 h-[500px] w-[500px] bg-gradient-to-br from-emerald-800/30 to-blue-800/30 opacity-15 blur-3xl rounded-full -z-10"></div>

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Subtle geometric lines */}
      <div className="absolute inset-0">
        {/* Horizontal lines */}
        <div className="absolute top-1/4 left-0 w-1/3 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent animate-pulse" />
        <div
          className="absolute top-3/4 right-0 w-1/4 h-px bg-gradient-to-l from-transparent via-blue-400/15 to-transparent animate-pulse"
          style={{ animationDelay: "2s" }}
        />

        {/* Vertical lines */}
        <div
          className="absolute left-1/4 top-0 w-px h-1/2 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute right-1/3 top-1/3 w-px h-1/3 bg-gradient-to-b from-transparent via-blue-400/15 to-transparent animate-pulse"
          style={{ animationDelay: "3s" }}
        />
      </div>

      {/* Main Visualizers */}
      <div className="relative px-6 py-16 z-10 mt-16">
        <div className="mx-auto max-w-7xl">
          <div className="mb-16 text-center">
            <h2 className="text-3xl font-bold text-white sm:text-4xl">
              Available Visualizers
            </h2>
            <p className="mt-4 text-lg text-gray-400">
              Click on any visualizer to start exploring
            </p>
          </div>

          <BentoGrid className="mx-auto max-w-6xl hover:cursor-pointer">
            {visualizers.map((visualizer) => (
              <BentoCard
                key={visualizer.name}
                {...visualizer}
                onClick={() => {
                  router.push(visualizer.href);
                }}
              />
            ))}
          </BentoGrid>
        </div>
      </div>

      {/* Coming Soon Section */}
      <div className="relative px-6 py-16 border-t border-gray-800/50 z-10">
        <div className="mx-auto max-w-7xl">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold text-white sm:text-4xl">
              Coming Soon
            </h2>
            <p className="mt-4 text-lg text-gray-400">
              Advanced Algorithms Visualizers are in development
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
