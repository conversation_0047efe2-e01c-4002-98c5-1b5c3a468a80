"use client";

interface StatusDisplayProps {
  isSorting: boolean;
  isComplete: boolean;
}

const StatusDisplay = ({ isSorting, isComplete }: StatusDisplayProps) => {
  return (
    <>
      {isSorting && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-blue-900/50 border border-blue-700/50 rounded-lg">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400 mr-2"></div>
            <span className="text-blue-300">Sorting in progress...</span>
          </div>
        </div>
      )}

      {isComplete && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-green-900/50 border border-green-700/50 rounded-lg">
            <svg
              className="w-4 h-4 text-green-400 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-green-300">Sorting completed!</span>
          </div>
        </div>
      )}
    </>
  );
};

export default StatusDisplay;
