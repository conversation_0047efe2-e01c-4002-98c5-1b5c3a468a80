"use client";

import React, { useEffect, useState } from "react";

interface Particle {
  id: number;
  x: number;
  delay: number;
  duration: number;
}

//git reset --hard

const ProfessionalHeroBackground = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    setParticles(
      Array.from({ length: 25 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        delay: Math.random() * 20,
        duration: 15 + Math.random() * 10,
      }))
    );
  }, []);

  return (
    <div className="relative min-h-screen w-full bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/60 via-transparent to-slate-950/70" />

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      {/* Subtle geometric lines */}
      <div className="absolute inset-0">
        {/* Horizontal lines */}
        <div className="absolute top-1/4 left-0 w-1/3 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent animate-pulse" />
        <div
          className="absolute top-3/4 right-0 w-1/4 h-px bg-gradient-to-l from-transparent via-blue-400/15 to-transparent animate-pulse"
          style={{ animationDelay: "2s" }}
        />

        {/* Vertical lines */}
        <div
          className="absolute left-1/4 top-0 w-px h-1/2 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute right-1/3 top-1/3 w-px h-1/3 bg-gradient-to-b from-transparent via-blue-400/15 to-transparent animate-pulse"
          style={{ animationDelay: "3s" }}
        />
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0">
        {isMounted &&
          particles.map((particle) => (
            <div
              key={particle.id}
              className="absolute w-0.5 h-0.5 bg-blue-500/50 rounded-full animate-particle-float-hero"
              style={{
                left: `${particle.x}%`,
                animationDelay: `${particle.delay}s`,
                animationDuration: `${particle.duration}s`,
              }}
            />
          ))}
      </div>

      {/* Subtle corner accents */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-slate-500/5 rounded-full blur-3xl" />

      {/* Content area */}
      <div className="relative z-10">{children}</div>

      {/* Bottom fade effect */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/50 to-transparent" />
    </div>
  );
};

export default ProfessionalHeroBackground;
