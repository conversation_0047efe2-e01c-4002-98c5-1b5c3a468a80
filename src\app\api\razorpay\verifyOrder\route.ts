import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import crypto from "crypto";
import { NextRequest, NextResponse } from "next/server";

//Verifying if the payment was successful or not and legit. Here we are using crypto to generate a signature using our secret key and comparing it with the signature sent by razorpay. If they match, then the payment was successful and legit.

const generatedSignature = (
  razorpayOrderId: string,
  razorpayPaymentId: string
) => {
  const keySecret = process.env.RAZORPAY_KEY_SECRET as string;

  const sig = crypto
    .createHmac("sha256", keySecret)
    .update(razorpayOrderId + "|" + razorpayPaymentId)
    .digest("hex");
  return sig;
};

export async function POST(request: NextRequest) {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }
  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  const { orderId, razorpayPaymentId, razorpaySignature } =
    await request.json();

  if (!orderId || !razorpayPaymentId || !razorpaySignature) {
    return NextResponse.json(
      { message: "Missing payment details", isOk: false },
      { status: 400 }
    );
  }

  const signature = generatedSignature(orderId, razorpayPaymentId);
  if (signature !== razorpaySignature) {
    return NextResponse.json(
      { message: "payment verification failed", isOk: false },
      { status: 400 }
    );
  }

  //find the user id
  const userId = await prisma.users.findFirst({
    where: {
      email: email,
    },
    select: {
      id: true,
    },
  });

  if (!userId) {
    return new Response("User not found", { status: 404 });
  }

  //create an order
  const order = await prisma.order.create({
    data: {
      orderId: orderId,
      razorpayPaymentId: razorpayPaymentId,
      userId: userId.id,
      email: email,
    },
  });

  const updateSheetSlot = await prisma.users.update({
    where: {
      email: email,
    },
    data: {
      maxSheetSlots: { increment: 1 },
    },
  });

  // console.log(updateSheetSlot);

  return NextResponse.json(
    { message: "payment verified successfully", isOk: true },
    { status: 200 }
  );
}
