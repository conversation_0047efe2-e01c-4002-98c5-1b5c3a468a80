import type { CodeforcesSubmission } from "./codeforces";

// ============================================================================
// CODING SHEET LOCAL STORAGE UTILITIES
// ============================================================================
// Utilities for managing coding sheet data in localStorage
// Handles storing, retrieving, and managing problem completion status

// ============================================================================
// CONSTANTS
// ============================================================================

// Maximum number of problems allowed in a coding sheet for optimal performance
// This limit ensures good rendering performance and prevents UI lag
export const MAX_CODING_SHEET_PROBLEMS = 400;

// Interface for coding sheet data stored in localStorage
export interface CodingSheetData {
  problems: CodeforcesSubmission[]; // Array of problems in the sheet
  createdAt: string; // ISO timestamp when sheet was created
  totalProblems: number; // Total number of problems in the sheet
  completedProblems: Set<string>; // Set of completed problem IDs (contestId + index)
  lastModified: string; // ISO timestamp when sheet was last modified
  sheetName?: string; // Optional custom name for the sheet
}

// Interface for problem completion tracking
export interface ProblemCompletion {
  problemId: string; // Unique identifier (contestId + index)
  completedAt: string; // ISO timestamp when marked as completed
  notes?: string; // Optional notes about the solution
}

// Local storage key for the coding sheet
const CODING_SHEET_KEY = "tracestack_coding_sheet";
const COMPLETION_DATA_KEY = "tracestack_completion_data";

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Generate unique problem ID from submission
export const generateProblemId = (submission: CodeforcesSubmission): string => {
  const { problem } = submission;
  return problem.contestId
    ? `${problem.contestId}${problem.index}`
    : `${problem.problemsetName || "unknown"}${problem.index}`;
};

// Validate and limit problems array to maximum allowed size
export const validateAndLimitProblems = (
  problems: CodeforcesSubmission[]
): {
  validatedProblems: CodeforcesSubmission[];
  wasLimited: boolean;
  originalCount: number;
} => {
  const originalCount = problems.length;
  const wasLimited = originalCount > MAX_CODING_SHEET_PROBLEMS;
  const validatedProblems = wasLimited
    ? problems.slice(0, MAX_CODING_SHEET_PROBLEMS)
    : problems;

  return {
    validatedProblems,
    wasLimited,
    originalCount,
  };
};

// Store coding sheet data in localStorage
export const storeCodingSheet = (
  problems: CodeforcesSubmission[],
  sheetName?: string
): boolean => {
  try {
    // Validate and limit problems to maximum allowed
    const { validatedProblems } = validateAndLimitProblems(problems);

    const sheetData: CodingSheetData = {
      problems: validatedProblems,
      createdAt: new Date().toISOString(),
      totalProblems: validatedProblems.length,
      completedProblems: new Set<string>(), // Start with no completed problems
      lastModified: new Date().toISOString(),
      sheetName:
        sheetName || `Coding Sheet - ${new Date().toLocaleDateString()}`,
    };

    // Convert Set to Array for JSON serialization
    const serializedData = {
      ...sheetData,
      completedProblems: Array.from(sheetData.completedProblems),
    };

    localStorage.setItem(CODING_SHEET_KEY, JSON.stringify(serializedData));
    return true;
  } catch (error) {
    console.error("Error storing coding sheet:", error);
    return false;
  }
};

// Retrieve coding sheet data from localStorage
export const getCodingSheet = (): CodingSheetData | null => {
  try {
    const storedData = localStorage.getItem(CODING_SHEET_KEY);
    if (!storedData) return null;

    const parsedData = JSON.parse(storedData);

    // Convert Array back to Set for completedProblems
    return {
      ...parsedData,
      completedProblems: new Set(parsedData.completedProblems || []),
    };
  } catch (error) {
    console.error("Error retrieving coding sheet:", error);
    return null;
  }
};

// Check if a coding sheet exists in localStorage
export const hasCodingSheet = (): boolean => {
  try {
    return localStorage.getItem(CODING_SHEET_KEY) !== null;
  } catch (error) {
    console.error("Error checking for coding sheet:", error);
    return false;
  }
};

// Clear coding sheet from localStorage
export const clearCodingSheet = (): boolean => {
  try {
    localStorage.removeItem(CODING_SHEET_KEY);
    localStorage.removeItem(COMPLETION_DATA_KEY);
    return true;
  } catch (error) {
    console.error("Error clearing coding sheet:", error);
    return false;
  }
};

// Mark a problem as completed
export const markProblemCompleted = (
  problemId: string,
  notes?: string
): boolean => {
  try {
    const sheetData = getCodingSheet();
    if (!sheetData) return false;

    // Add to completed problems set
    sheetData.completedProblems.add(problemId);
    sheetData.lastModified = new Date().toISOString();

    // Store completion details separately for additional metadata
    const completionData: ProblemCompletion = {
      problemId,
      completedAt: new Date().toISOString(),
      notes,
    };

    // Get existing completion data
    const existingCompletions = getCompletionData();
    existingCompletions[problemId] = completionData;

    // Save both updated sheet and completion data
    const serializedSheetData = {
      ...sheetData,
      completedProblems: Array.from(sheetData.completedProblems),
    };

    localStorage.setItem(CODING_SHEET_KEY, JSON.stringify(serializedSheetData));
    localStorage.setItem(
      COMPLETION_DATA_KEY,
      JSON.stringify(existingCompletions)
    );

    return true;
  } catch (error) {
    console.error("Error marking problem as completed:", error);
    return false;
  }
};

// Mark a problem as not completed
export const markProblemIncomplete = (problemId: string): boolean => {
  try {
    const sheetData = getCodingSheet();
    if (!sheetData) return false;

    // Remove from completed problems set
    sheetData.completedProblems.delete(problemId);
    sheetData.lastModified = new Date().toISOString();

    // Remove from completion data
    const existingCompletions = getCompletionData();
    delete existingCompletions[problemId];

    // Save both updated sheet and completion data
    const serializedSheetData = {
      ...sheetData,
      completedProblems: Array.from(sheetData.completedProblems),
    };

    localStorage.setItem(CODING_SHEET_KEY, JSON.stringify(serializedSheetData));
    localStorage.setItem(
      COMPLETION_DATA_KEY,
      JSON.stringify(existingCompletions)
    );

    return true;
  } catch (error) {
    console.error("Error marking problem as incomplete:", error);
    return false;
  }
};

// Get completion data for all problems
export const getCompletionData = (): Record<string, ProblemCompletion> => {
  try {
    const storedData = localStorage.getItem(COMPLETION_DATA_KEY);
    return storedData ? JSON.parse(storedData) : {};
  } catch (error) {
    console.error("Error retrieving completion data:", error);
    return {};
  }
};

// Check if a specific problem is completed
export const isProblemCompleted = (problemId: string): boolean => {
  try {
    const sheetData = getCodingSheet();
    return sheetData ? sheetData.completedProblems.has(problemId) : false;
  } catch (error) {
    console.error("Error checking problem completion:", error);
    return false;
  }
};

// Get completion statistics
export const getCompletionStats = (): {
  total: number;
  completed: number;
  remaining: number;
  completionPercentage: number;
} => {
  try {
    const sheetData = getCodingSheet();
    if (!sheetData) {
      return { total: 0, completed: 0, remaining: 0, completionPercentage: 0 };
    }

    const total = sheetData.totalProblems;
    const completed = sheetData.completedProblems.size;
    const remaining = total - completed;
    const completionPercentage =
      total > 0 ? Math.round((completed / total) * 100) : 0;

    return { total, completed, remaining, completionPercentage };
  } catch (error) {
    console.error("Error calculating completion stats:", error);
    return { total: 0, completed: 0, remaining: 0, completionPercentage: 0 };
  }
};

// Export coding sheet data as JSON for backup/sharing
export const exportCodingSheet = (): string | null => {
  try {
    const sheetData = getCodingSheet();
    const completionData = getCompletionData();

    if (!sheetData) return null;

    const exportData = {
      sheet: sheetData,
      completions: completionData,
      exportedAt: new Date().toISOString(),
      version: "1.0",
    };

    return JSON.stringify(exportData, null, 2);
  } catch (error) {
    console.error("Error exporting coding sheet:", error);
    return null;
  }
};

// Import coding sheet data from JSON
export const importCodingSheet = (jsonData: string): boolean => {
  try {
    const importData = JSON.parse(jsonData);

    if (!importData.sheet || !importData.completions) {
      throw new Error("Invalid import data format");
    }

    // Restore the sheet data
    const serializedSheetData = {
      ...importData.sheet,
      completedProblems: Array.from(importData.sheet.completedProblems || []),
    };

    localStorage.setItem(CODING_SHEET_KEY, JSON.stringify(serializedSheetData));
    localStorage.setItem(
      COMPLETION_DATA_KEY,
      JSON.stringify(importData.completions)
    );

    return true;
  } catch (error) {
    console.error("Error importing coding sheet:", error);
    return false;
  }
};
