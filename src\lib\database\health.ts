import { prisma } from '../prisma';
import { handlePrismaError, withRetry } from './errors';

/**
 * Database health check result interface
 */
export interface DatabaseHealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Date;
  responseTime: number;
  details: {
    connection: boolean;
    queryExecution: boolean;
    connectionPool?: {
      active?: number;
      idle?: number;
      total?: number;
    };
  };
  error?: string;
}

/**
 * Connection pool status interface
 */
export interface ConnectionPoolStatus {
  active: number;
  idle: number;
  total: number;
  maxConnections: number;
  utilizationPercentage: number;
}

/**
 * Performs a basic database connectivity check
 */
export const checkDatabaseConnection = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection check failed:', error);
    return false;
  }
};

/**
 * Performs a comprehensive database health check
 */
export const performHealthCheck = async (): Promise<DatabaseHealthCheck> => {
  const startTime = Date.now();
  const timestamp = new Date();
  
  let connectionStatus = false;
  let queryStatus = false;
  let error: string | undefined;

  try {
    // Test basic connection
    connectionStatus = await withRetry(async () => {
      await prisma.$connect();
      return true;
    }, 2, 500);

    // Test query execution
    if (connectionStatus) {
      queryStatus = await withRetry(async () => {
        await prisma.$queryRaw`SELECT 1 as health_check`;
        return true;
      }, 2, 500);
    }
  } catch (err) {
    const dbError = handlePrismaError(err);
    error = dbError.message;
    console.error('Health check failed:', dbError);
  }

  const responseTime = Date.now() - startTime;

  // Determine overall status
  let status: 'healthy' | 'unhealthy' | 'degraded';
  if (connectionStatus && queryStatus) {
    status = responseTime > 5000 ? 'degraded' : 'healthy';
  } else if (connectionStatus) {
    status = 'degraded';
  } else {
    status = 'unhealthy';
  }

  return {
    status,
    timestamp,
    responseTime,
    details: {
      connection: connectionStatus,
      queryExecution: queryStatus,
    },
    error,
  };
};

/**
 * Monitors connection pool metrics (if available)
 * Note: This is a placeholder for future implementation when Prisma exposes pool metrics
 */
export const getConnectionPoolStatus = async (): Promise<ConnectionPoolStatus | null> => {
  try {
    // This is a conceptual implementation
    // Prisma doesn't currently expose detailed pool metrics via the client
    // In a real implementation, you might query system tables or use monitoring tools
    
    return {
      active: 0, // Would be populated from actual metrics
      idle: 0,   // Would be populated from actual metrics
      total: 0,  // Would be populated from actual metrics
      maxConnections: 1, // Based on connection_limit setting
      utilizationPercentage: 0,
    };
  } catch (error) {
    console.error('Failed to get connection pool status:', error);
    return null;
  }
};

/**
 * Validates database schema integrity
 */
export const validateSchemaIntegrity = async (): Promise<boolean> => {
  try {
    // Check if essential tables exist and are accessible
    const userCount = await prisma.users.count();
    const sheetCount = await prisma.sheet.count();
    
    // If we can count records, schema is likely intact
    return typeof userCount === 'number' && typeof sheetCount === 'number';
  } catch (error) {
    console.error('Schema integrity check failed:', error);
    return false;
  }
};

/**
 * Comprehensive database diagnostics
 */
export const runDatabaseDiagnostics = async () => {
  console.log('🔍 Running database diagnostics...');
  
  const healthCheck = await performHealthCheck();
  const poolStatus = await getConnectionPoolStatus();
  const schemaIntegrity = await validateSchemaIntegrity();
  
  const diagnostics = {
    health: healthCheck,
    connectionPool: poolStatus,
    schemaIntegrity,
    recommendations: [] as string[],
  };

  // Generate recommendations based on results
  if (healthCheck.status === 'unhealthy') {
    diagnostics.recommendations.push('Database connection is failing. Check DATABASE_URL and network connectivity.');
  }
  
  if (healthCheck.responseTime > 3000) {
    diagnostics.recommendations.push('Database response time is slow. Consider optimizing queries or checking connection pooling.');
  }
  
  if (!schemaIntegrity) {
    diagnostics.recommendations.push('Schema integrity check failed. Run database migrations or check for schema drift.');
  }
  
  if (poolStatus && poolStatus.utilizationPercentage > 80) {
    diagnostics.recommendations.push('Connection pool utilization is high. Consider increasing connection limits or optimizing query patterns.');
  }

  return diagnostics;
};

/**
 * Database warmup function for serverless environments
 * Establishes connection and primes the connection pool
 */
export const warmupDatabase = async (): Promise<boolean> => {
  try {
    console.log('🔥 Warming up database connection...');
    
    // Establish connection
    await prisma.$connect();
    
    // Execute a lightweight query to prime the connection
    await prisma.$queryRaw`SELECT 1`;
    
    console.log('✅ Database warmed up successfully');
    return true;
  } catch (error) {
    console.error('❌ Database warmup failed:', error);
    return false;
  }
};

/**
 * Graceful database shutdown
 * Properly closes connections and cleans up resources
 */
export const shutdownDatabase = async (): Promise<void> => {
  try {
    console.log('🔄 Shutting down database connections...');
    await prisma.$disconnect();
    console.log('✅ Database connections closed gracefully');
  } catch (error) {
    console.error('❌ Error during database shutdown:', error);
  }
};

/**
 * Database maintenance utilities
 */
export const maintenanceUtils = {
  /**
   * Analyze query performance (placeholder for future implementation)
   */
  analyzeQueryPerformance: async () => {
    // This would integrate with query logging and performance monitoring
    console.log('Query performance analysis not yet implemented');
  },

  /**
   * Check for long-running queries (placeholder)
   */
  checkLongRunningQueries: async () => {
    // This would query system tables to find long-running queries
    console.log('Long-running query check not yet implemented');
  },

  /**
   * Optimize database statistics (placeholder)
   */
  updateStatistics: async () => {
    // This would run database-specific optimization commands
    console.log('Statistics update not yet implemented');
  },
};
