import CodeEditor from "@/components/features/code-editor";
import { HowItWorks } from "@/components/features/how-it-works";
import ProfessionalHeroBackground from "@/components/layout/ProfessionalHeroBackground";

const page = () => {
  return (
    <ProfessionalHeroBackground>
      <div className="pt-26 pb-8 mt-22">
        <div className="mx-auto max-w-4xl px-4 text-center">
          <h1 className="text-5xl font-bold text-white md:text-6xl animate-breathing">
            Trace and analyze code to spot bugs instantly.
          </h1>
          <p className="mx-auto mt-4 max-w-3xl text-lg text-gray-300">
            Our intelligent code editor helps you visualize errors, understand
            complex logic, and write better code.
          </p>
        </div>
        <div className="mt-12 w-[90vw] h-[90vw] max-w-[1200px] max-h-[90vh] mx-auto">
          <CodeEditor />
        </div>

        <HowItWorks />
      </div>
    </ProfessionalHeroBackground>
  );
};

export default page;
