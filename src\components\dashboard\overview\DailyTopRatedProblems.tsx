"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { useAllSolvedQuestionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import {
  createContributionGrid,
  getAvailableYears,
  getMonthLabels,
  getRatingBasedColor,
  processSubmissionsToContributionsForYear,
} from "@/lib/contribution-utils";
import {
  Activity,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Target,
  TrendingUp,
} from "lucide-react";
import React from "react";

interface DailyTopRatedProblemsProps {
  handle: string;
}

const DailyTopRatedProblems: React.FC<DailyTopRatedProblemsProps> = React.memo(
  ({ handle }) => {
    // State for year selection
    const [selectedYear, setSelectedYear] = React.useState<number>(
      new Date().getFullYear()
    );

    // Memoize the trimmed handle to ensure stable query key
    const stableHandle = React.useMemo(() => handle?.trim() || "", [handle]);

    // Fetch all solved submissions for the user with optimized configuration
    const {
      data: submissionsData,
      isLoading,
      error,
    } = useAllSolvedQuestionsClient({
      handle: stableHandle,
      enabled: !!stableHandle,
    });

    // Get available years from submissions
    const availableYears = React.useMemo(() => {
      if (!submissionsData?.result?.submissions) return [];
      return getAvailableYears(submissionsData.result.submissions);
    }, [submissionsData?.result?.submissions]);

    // Update selected year to the latest available year when data loads
    React.useEffect(() => {
      if (availableYears.length > 0) {
        const currentYear = new Date().getFullYear();
        // If current year is available and it's not January, use current year
        // Otherwise, use the previous year for more complete data
        const defaultYear =
          availableYears.includes(currentYear) && new Date().getMonth() > 0
            ? currentYear
            : availableYears.find((year) => year < currentYear) ||
              availableYears[0];

        if (!availableYears.includes(selectedYear)) {
          setSelectedYear(defaultYear);
        }
      }
    }, [availableYears, selectedYear]);

    // Process contribution data for the selected year
    const contributionData = React.useMemo(() => {
      if (!submissionsData?.result?.submissions) return null;
      return processSubmissionsToContributionsForYear(
        submissionsData.result.submissions,
        selectedYear
      );
    }, [submissionsData?.result?.submissions, selectedYear]);

    // Create GitHub-style grid layout
    const contributionGrid = React.useMemo(() => {
      if (!contributionData) return [];
      return createContributionGrid(contributionData.days);
    }, [contributionData]);

    // Get month labels for the grid
    const monthLabels = React.useMemo(() => {
      if (!contributionData) return [];
      return getMonthLabels(contributionData.days);
    }, [contributionData]);

    // Navigation functions - memoize to prevent unnecessary re-renders
    const goToPreviousYear = React.useCallback(() => {
      const currentIndex = availableYears.indexOf(selectedYear);
      if (currentIndex < availableYears.length - 1) {
        setSelectedYear(availableYears[currentIndex + 1]);
      }
    }, [availableYears, selectedYear]);

    const goToNextYear = React.useCallback(() => {
      const currentIndex = availableYears.indexOf(selectedYear);
      if (currentIndex > 0) {
        setSelectedYear(availableYears[currentIndex - 1]);
      }
    }, [availableYears, selectedYear]);

    if (isLoading) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Daily Top-Rated Problems Solved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
              <span className="ml-3 text-gray-400">
                Loading contribution data...
              </span>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (error || !submissionsData?.result?.submissions) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Daily Top-Rated Problems Solved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-400 py-8">
              <p>Unable to load contribution data</p>
              <p className="text-sm mt-2">Please check the Codeforces handle</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (!contributionData) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Daily Top-Rated Problems Solved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-400 py-8">
              <p>No contribution data available for {selectedYear}</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <div className="flex items-start justify-between mb-4">
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Daily Top-Rated Problems Solved
            </CardTitle>

            {/* Year Navigation */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPreviousYear}
                disabled={
                  availableYears.indexOf(selectedYear) >=
                  availableYears.length - 1
                }
                className="h-8 w-8 p-0 bg-slate-800 border-slate-600 hover:bg-slate-700"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <span className="text-white font-medium min-w-[60px] text-center">
                {selectedYear}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={goToNextYear}
                disabled={availableYears.indexOf(selectedYear) <= 0}
                className="h-8 w-8 p-0 bg-slate-800 border-slate-600 hover:bg-slate-700"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <div className="flex items-center gap-2 mb-2">
                <Target className="w-5 h-5 text-blue-400" />
                <span className="text-sm text-gray-300 font-medium">
                  Total Problems
                </span>
              </div>
              <div className="text-2xl font-bold text-blue-400">
                {contributionData.totalSolved}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                Solved in {selectedYear}
              </div>
            </div>

            <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="w-5 h-5 text-green-400" />
                <span className="text-sm text-gray-300 font-medium">
                  Current Streak
                </span>
              </div>
              <div className="text-2xl font-bold text-green-400">
                {contributionData.currentStreak}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {contributionData.currentStreak === 1 ? "day" : "days"} active
              </div>
            </div>

            <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-600/30">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="w-5 h-5 text-yellow-400" />
                <span className="text-sm text-gray-300 font-medium">
                  Best Streak
                </span>
              </div>
              <div className="text-2xl font-bold text-yellow-400">
                {contributionData.maxStreak}
              </div>
              <div className="text-xs text-gray-400 mt-1">
                {contributionData.maxStreak === 1 ? "day" : "days"} record
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pb-6">
          {/* Two-column layout to utilize full width */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* Left column - Contribution Grid */}
            <div className="xl:col-span-2">
              <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/20">
                {/* Month labels */}
                <div className="relative mb-3 ml-14">
                  <div className="flex justify-start">
                    {monthLabels.map((label, index) => (
                      <div
                        key={index}
                        className="text-xs text-gray-400 text-left font-medium"
                        style={{
                          width: `${label.width * 18}px`,
                          minWidth: "32px",
                        }}
                      >
                        {label.month}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Day labels and Contribution grid container */}
                <div className="flex items-start gap-3">
                  {/* Day labels */}
                  <div className="flex flex-col text-xs text-gray-400 font-medium justify-between h-[126px] pt-1">
                    <span>Sun</span>
                    <span>Mon</span>
                    <span>Tue</span>
                    <span>Wed</span>
                    <span>Thu</span>
                    <span>Fri</span>
                    <span>Sat</span>
                  </div>

                  {/* Contribution grid */}
                  <div className="flex-1 overflow-x-auto">
                    <div
                      className="flex gap-1 min-w-fit pb-2"
                      style={{ minWidth: "600px" }}
                    >
                      {contributionGrid.map((week, weekIndex) => (
                        <div key={weekIndex} className="flex flex-col gap-1">
                          {week.map((day, dayIndex) => (
                            <div
                              key={day.date || `empty-${weekIndex}-${dayIndex}`}
                              className="w-[14px] h-[14px] rounded-sm border border-gray-700/40 transition-all duration-200 hover:border-gray-500 hover:scale-110 cursor-pointer"
                              style={{
                                backgroundColor: day.date
                                  ? getRatingBasedColor(
                                      day.maxRating,
                                      day.level
                                    )
                                  : "rgb(22, 27, 34)",
                                boxShadow:
                                  day.level > 0 && day.date
                                    ? `0 0 6px ${getRatingBasedColor(
                                        day.maxRating,
                                        day.level
                                      )}80`
                                    : "none",
                              }}
                              title={
                                day.date
                                  ? `${day.date}: ${day.count} problem${
                                      day.count !== 1 ? "s" : ""
                                    } solved${
                                      day.maxRating > 0
                                        ? ` (max rating: ${day.maxRating})`
                                        : ""
                                    }`
                                  : ""
                              }
                            />
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Legend */}
                <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-600/30">
                  <div className="text-sm text-gray-400 font-medium">
                    Colors represent highest rated problem solved each day
                  </div>
                </div>
              </div>
            </div>

            {/* Right column - Additional insights and summary */}
            <div className="xl:col-span-1 space-y-4">
              {/* Activity Summary */}
              <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/20">
                <h4 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <Activity className="w-4 h-4 text-green-400" />
                  Activity Summary
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">
                      Most Active Day
                    </span>
                    <span className="text-xs text-white font-medium">
                      {contributionData.days
                        .reduce(
                          (max, day) => (day.count > max.count ? day : max),
                          contributionData.days[0] || { count: 0, date: "N/A" }
                        )
                        .date?.split("-")
                        .slice(1)
                        .join("/") || "N/A"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">Active Days</span>
                    <span className="text-xs text-white font-medium">
                      {
                        contributionData.days.filter((day) => day.count > 0)
                          .length
                      }
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">Average/Day</span>
                    <span className="text-xs text-white font-medium">
                      {(
                        contributionData.totalSolved /
                        Math.max(
                          contributionData.days.filter((day) => day.count > 0)
                            .length,
                          1
                        )
                      ).toFixed(1)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Rating Distribution */}
              <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/20">
                <h4 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <Target className="w-4 h-4 text-blue-400" />
                  Rating Levels
                </h4>
                <div className="space-y-2">
                  {[
                    {
                      range: "2000+",
                      color: "#ff0000",
                      count: contributionData.days.filter(
                        (d) => d.maxRating >= 2000
                      ).length,
                    },
                    {
                      range: "1600-1999",
                      color: "#ff8c00",
                      count: contributionData.days.filter(
                        (d) => d.maxRating >= 1600 && d.maxRating < 2000
                      ).length,
                    },
                    {
                      range: "1200-1599",
                      color: "#0000ff",
                      count: contributionData.days.filter(
                        (d) => d.maxRating >= 1200 && d.maxRating < 1600
                      ).length,
                    },
                    {
                      range: "800-1199",
                      color: "#008000",
                      count: contributionData.days.filter(
                        (d) => d.maxRating >= 800 && d.maxRating < 1200
                      ).length,
                    },
                    {
                      range: "<800",
                      color: "#808080",
                      count: contributionData.days.filter(
                        (d) => d.maxRating > 0 && d.maxRating < 800
                      ).length,
                    },
                  ].map((level) => (
                    <div
                      key={level.range}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-sm"
                          style={{ backgroundColor: level.color }}
                        />
                        <span className="text-xs text-gray-400">
                          {level.range}
                        </span>
                      </div>
                      <span className="text-xs text-white font-medium">
                        {level.count}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-slate-800/30 rounded-lg p-4 border border-slate-600/20">
                <h4 className="text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-yellow-400" />
                  Quick Stats
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">
                      Completion Rate
                    </span>
                    <span className="text-xs text-white font-medium">
                      {(
                        (contributionData.days.filter((d) => d.count > 0)
                          .length /
                          contributionData.days.length) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-400">Best Month</span>
                    <span className="text-xs text-white font-medium">
                      {
                        monthLabels.reduce(
                          (max, month, index) => {
                            const monthDays = contributionData.days.filter(
                              (day) =>
                                day.date &&
                                new Date(day.date).getMonth() === index
                            );
                            const monthTotal = monthDays.reduce(
                              (sum, day) => sum + day.count,
                              0
                            );
                            return monthTotal > max.total
                              ? { month: month.month, total: monthTotal }
                              : max;
                          },
                          { month: "N/A", total: 0 }
                        ).month
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// Add display name for debugging
DailyTopRatedProblems.displayName = "DailyTopRatedProblems";

export default DailyTopRatedProblems;
