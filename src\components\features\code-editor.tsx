"use client";

import {
  <PERSON><PERSON>boardPaste,
  <PERSON>2,
  <PERSON><PERSON>,
  Link,
  RotateCw,
  Send,
  Smartphone,
} from "lucide-react";
import dynamic from "next/dynamic";
import "prismjs/themes/prism-okaidia.css";
import { useState } from "react";

const MonacoEditor = dynamic(() => import("@monaco-editor/react"), {
  ssr: false,
});

const Page = () => {
  const [activeTab, setActiveTab] = useState<"cpp" | "java" | "python">("cpp");
  const [problemLink, setProblemLink] = useState("");
  const [linkError, setLinkError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  const validateAndSetLink = (link: string) => {
    setProblemLink(link);

    if (!link) {
      setLinkError(null);
      return;
    }

    if (link.length > 512) {
      setLinkError("URL cannot be longer than 512 characters.");
      return;
    }

    const urlPattern = new RegExp(
      "^(https?:\\/\\/)?" + // protocol
        "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
        "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
        "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port and path
        "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
        "(\\#[-a-z\\d_]*)?$", // fragment locator
      "i"
    );

    if (urlPattern.test(link)) {
      setLinkError(null);
    } else {
      setLinkError("Please enter a valid URL.");
    }
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      validateAndSetLink(text);
    } catch (err) {
      console.error("Failed to read clipboard contents: ", err);
    }
  };

  const handleSubmit = async () => {
    if (linkError || !problemLink.trim()) {
      if (!problemLink.trim()) {
        setLinkError("Problem link cannot be empty.");
      }
      console.error("Submission failed: Invalid or empty link.");
      return;
    }

    setIsLoading(true);
    setAnalysisResult(null);
    setAnalysisError(null);
    const promptToGetSolution = `
    You are a strict code generator.
    
    Your task:
    - Given a problem context link and a programming language, generate only the complete, clean code implementation.
    - If the input format is defined or implied, include input format description as inline comments at the top of the code (e.g., "The first line contains...").
    - Do not include any explanation, markdown, or headings.
    - After the implementation, provide exactly 5 sample test cases showing only:
      - The input (matching the described input format)
      - The expected output
    - No extra commentary or formatting.
    
    Constraints:
    - Language: ${activeTab}
    - Problem: ${problemLink}
    - Output must be strictly in the syntax of the selected programming language.
    - Only code + 5 plain sample test cases.
    
    Begin now.
    `;

    try {
      const response = await fetch("/api/gemini", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: promptToGetSolution,
          userCode: activeSnippet.code,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch analysis.");
      }

      const result = await response.json();
      const feedback = result.failedTestCases;

      if (feedback) {
        setAnalysisResult(feedback);
      } else {
        throw new Error("Invalid response structure from analysis API.");
      }
    } catch (error: any) {
      setAnalysisError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const [cppCode, setCppCode] = useState(`#include <iostream>

int main() {
    std::cout << "Hello, C++!" << std::endl;
    return 0;
}`);
  const [javaCode, setJavaCode] = useState(`public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, Java!");
    }
}`);
  const [pythonCode, setPythonCode] = useState(`def hello():
    print("Hello, Python!")

hello()`);

  const codeSnippets = {
    cpp: {
      code: cppCode,
      setter: setCppCode,
      language: "cpp",
      icon: <Code2 className="w-4 h-4" />,
      name: "C++",
    },
    java: {
      code: javaCode,
      setter: setJavaCode,
      language: "java",
      icon: <Smartphone className="w-4 h-4" />,
      name: "Java",
    },
    python: {
      code: pythonCode,
      setter: setPythonCode,
      language: "python",
      icon: <Cpu className="w-4 h-4" />,
      name: "Python",
    },
  };

  const activeSnippet = codeSnippets[activeTab];

  const submitButton = (
    <div className="bg-[#21252b] p-4 flex items-center justify-center">
      <button
        onClick={handleSubmit}
        disabled={!!linkError || !problemLink.trim() || isLoading}
        className="flex items-center justify-center gap-2 w-full max-w-sm bg-white text-black font-bold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#21252b] focus:ring-gray-500 border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? (
          <>
            <div className="w-5 h-5 border-2 border-dashed rounded-full animate-spin border-black"></div>
            <span>Analyzing...</span>
          </>
        ) : analysisResult || analysisError ? (
          <>
            <RotateCw className="w-5 h-5" />
            <span>Try Again</span>
          </>
        ) : (
          <>
            <Send className="w-5 h-5" />
            <span>Submit & Analyze</span>
          </>
        )}
      </button>
    </div>
  );

  return (
    <div className="flex items-center justify-center p-4 h-full">
      <div className="w-full h-full flex flex-col rounded-lg scrollable-content shadow-2xl bg-[#282c34]">
        {/* Header */}
        <div className="bg-[#21252b] p-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-[#ff5f57]"></div>
            <div className="w-3 h-3 rounded-full bg-[#febb2e]"></div>
            <div className="w-3 h-3 rounded-full bg-[#28c840]"></div>
          </div>
          <div className="text-gray-400 text-sm"></div>
          <div className="w-12" />
        </div>

        {/* Problem Link Input */}
        <div className="bg-[#21252b] px-3 pb-3">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Link
                className={`h-5 w-5 transition-colors duration-300 ${
                  linkError ? "text-red-400" : "text-gray-400"
                }`}
              />
            </div>
            <input
              type="text"
              name="problem-link"
              id="problem-link"
              className={`block w-full bg-[#282c34] border rounded-md py-2.5 pl-10 pr-12 text-base placeholder-gray-400 text-white focus:outline-none focus:ring-1 transition-colors duration-300 ${
                linkError
                  ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                  : "border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              }`}
              placeholder="Add the link of the problem"
              value={problemLink}
              onChange={(e) => validateAndSetLink(e.target.value)}
              maxLength={512}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <button
                type="button"
                onClick={handlePaste}
                className="text-gray-400 hover:text-white"
                aria-label="Paste problem link"
              >
                <ClipboardPaste className="h-5 w-5" />
              </button>
            </div>
          </div>
          {linkError && (
            <p className="mt-2 text-xs text-red-400">{linkError}</p>
          )}
        </div>

        {/* Tabs */}
        <div className="bg-[#21252b] flex">
          {Object.entries(codeSnippets).map(([key, { name, icon }]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as "cpp" | "java" | "python")}
              className={`flex items-center gap-2 px-4 py-2 text-sm border-t-2 ${
                activeTab === key
                  ? "bg-[#282c34] text-white border-blue-500"
                  : "text-gray-400 border-transparent hover:bg-[#2c313a]"
              }`}
            >
              {icon}
              {name}
            </button>
          ))}
        </div>

        {/* Monaco Editor */}
        <div className="flex-grow p-4 bg-[#1e1e1e]">
          <MonacoEditor
            language={activeSnippet.language}
            value={activeSnippet.code}
            theme="vs-dark"
            onChange={(code) => activeSnippet.setter(code || "")}
            options={{
              fontSize: 14,
              minimap: { enabled: false },
              wordWrap: "on",
              automaticLayout: true,
              scrollBeyondLastLine: false,
              suggestOnTriggerCharacters: true,
              tabSize: 2,
              scrollbar: {
                alwaysConsumeMouseWheel: false,
              },
            }}
          />
        </div>

        {!analysisResult && !analysisError && submitButton}

        {analysisResult && (
          <div className="bg-[#21252b] p-4">
            <h3 className="text-2xl font-bold mb-4 text-white px-2">
              Analysis Result
            </h3>
            <pre className="bg-[#1e1e1e] p-4 rounded-md scrollable-content">
              <code className="text-white font-mono">{analysisResult}</code>
            </pre>
          </div>
        )}

        {analysisError && (
          <div className="bg-[#21252b] p-4">
            <div className="p-4 bg-red-900/50 border border-red-500 rounded-lg shadow-lg text-white">
              <h3 className="text-xl font-bold mb-2">Error</h3>
              <p>{analysisError}</p>
            </div>
          </div>
        )}

        {(analysisResult || analysisError) && submitButton}
      </div>
    </div>
  );
};

export default Page;
