"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useAllSolvedQuestionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import { Target } from "lucide-react";
import { useMemo } from "react";

// Types for difficulty analysis data
interface DifficultyCategory {
  name: string;
  count: number;
  color: string;
  percentage: number;
}

interface DifficultyAnalysisProps {
  handle: string;
}

const DifficultyAnalysis = ({ handle }: DifficultyAnalysisProps) => {
  // Fetch all solved problems
  const {
    data: solvedData,
    isLoading,
    error,
  } = useAllSolvedQuestionsClient({
    handle,
    enabled: !!handle.trim(),
  });

  // Process solved problems into difficulty categories
  const { categories, totalProblems, maxCount, problemsInBand } =
    useMemo(() => {
      if (!solvedData?.result?.submissions) {
        return {
          categories: [],
          totalProblems: 0,
          maxCount: 0,
          problemsInBand: 0,
        };
      }

      // Filter unique solved problems
      const uniqueProblems = new Map();
      solvedData.result.submissions.forEach((submission) => {
        const problemId = `${submission.problem.contestId || "unknown"}-${
          submission.problem.index
        }`;
        if (submission.verdict === "OK" && submission.problem.rating) {
          if (
            !uniqueProblems.has(problemId) ||
            uniqueProblems.get(problemId).rating < submission.problem.rating
          ) {
            uniqueProblems.set(problemId, submission.problem);
          }
        }
      });

      const problems = Array.from(uniqueProblems.values());
      const total = problems.length;

      // Define difficulty categories based on rating ranges
      const difficultyRanges = [
        {
          name: "Very Easy",
          min: 800,
          max: 1199,
          color: "#22c55e", // Green
        },
        {
          name: "Easy",
          min: 1200,
          max: 1399,
          color: "#3b82f6", // Blue
        },
        {
          name: "Moderate",
          min: 1400,
          max: 1699,
          color: "#f59e0b", // Orange/Yellow
        },
        {
          name: "Hard",
          min: 1700,
          max: 2199,
          color: "#ef4444", // Red
        },
        {
          name: "Very Hard",
          min: 2200,
          max: Infinity,
          color: "#8b5cf6", // Purple
        },
      ];

      // Count problems in each difficulty category
      const categories: DifficultyCategory[] = difficultyRanges.map((range) => {
        const count = problems.filter(
          (p) => p.rating >= range.min && p.rating <= range.max
        ).length;

        return {
          name: range.name,
          count,
          color: range.color,
          percentage: total > 0 ? Math.round((count / total) * 100) : 0,
        };
      });

      const maxCount = Math.max(...categories.map((cat) => cat.count), 1);

      // Calculate how many problems fall within our defined difficulty bands
      const problemsInBand = categories.reduce(
        (sum, cat) => sum + cat.count,
        0
      );

      return { categories, totalProblems: total, maxCount, problemsInBand };
    }, [solvedData]);

  if (isLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-green-400" />
            Difficulty Category Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || totalProblems === 0) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Target className="w-5 h-5 text-green-400" />
            Difficulty Category Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-400">No solved problems data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
          <Target className="w-5 h-5 text-green-400" />
          Difficulty Category Analysis
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left side - Bar Chart */}
          <div className="space-y-4">
            <div className="space-y-3">
              {categories.map((category) => (
                <div key={category.name} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">
                      {category.name}
                    </span>
                    <span className="text-sm font-medium text-white">
                      {category.count}
                    </span>
                  </div>
                  <div className="h-8 bg-slate-800/50 rounded-lg overflow-hidden">
                    <div
                      className="h-full rounded-lg transition-all duration-500 ease-out"
                      style={{
                        width: `${(category.count / maxCount) * 100}%`,
                        backgroundColor: category.color,
                        opacity: category.count > 0 ? 0.8 : 0.3,
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right side - Category Cards */}
          <div className="space-y-3">
            {categories.map((category) => (
              <div
                key={category.name}
                className="bg-slate-800/30 border border-slate-600/20 rounded-lg p-4 flex justify-between items-center"
                style={{
                  borderLeftColor: category.color,
                  borderLeftWidth: "4px",
                }}
              >
                <div>
                  <div
                    className="text-2xl font-bold"
                    style={{ color: category.color }}
                  >
                    {category.count}
                  </div>
                  <div className="text-sm text-gray-300">{category.name}</div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-semibold text-gray-400">
                    {category.percentage}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DifficultyAnalysis;
