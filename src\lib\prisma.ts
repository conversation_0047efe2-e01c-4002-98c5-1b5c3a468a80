import { Prisma, PrismaClient } from "../generated/prisma";

// Enhanced global type declaration for better type safety
declare global {
  var __prisma: PrismaClient | undefined;
}

/**
 * Database configuration utility
 * Provides environment-specific database connection settings
 */
const getDatabaseConfig = () => {
  const baseUrl = process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error("DATABASE_URL environment variable is required");
  }

  // For production, ensure we're using connection pooling parameters
  // These settings are optimized for serverless environments
  if (process.env.NODE_ENV === "production") {
    // Add connection pooling parameters if not already present
    const url = new URL(baseUrl);
    if (!url.searchParams.has("connection_limit")) {
      url.searchParams.set("connection_limit", "1");
    }
    if (!url.searchParams.has("pool_timeout")) {
      url.searchParams.set("pool_timeout", "20");
    }
    if (!url.searchParams.has("connect_timeout")) {
      url.searchParams.set("connect_timeout", "15");
    }
    return url.toString();
  }

  return baseUrl;
};

/**
 * Logging configuration based on environment
 * Development: Comprehensive logging for debugging
 * Production: Error-only logging for performance
 */
const getLogConfig = (): Prisma.LogLevel[] => {
  if (process.env.NODE_ENV === "development") {
    return ["query", "info", "warn", "error"];
  }
  return ["error"];
};

/**
 * Production-ready PrismaClient configuration
 * Follows official Prisma best practices for Next.js and serverless environments
 */
const createPrismaClient = () => {
  return new PrismaClient({
    log: getLogConfig(),
    datasources: {
      db: {
        url: getDatabaseConfig(),
      },
    },
    // Additional configuration for production optimization
    errorFormat: process.env.NODE_ENV === "development" ? "pretty" : "minimal",
  });
};

/**
 * Global PrismaClient instance with hot-reload protection
 * This pattern prevents multiple database connections during development
 * while ensuring optimal performance in production
 */
const globalForPrisma = globalThis as unknown as { __prisma: PrismaClient };

export const prisma = globalForPrisma.__prisma || createPrismaClient();

// In development, store the client globally to prevent hot-reload issues
if (process.env.NODE_ENV === "development") {
  globalForPrisma.__prisma = prisma;
}

export default prisma;
