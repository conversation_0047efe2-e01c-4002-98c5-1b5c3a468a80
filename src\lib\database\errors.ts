import { 
  PrismaClientKnownRequestError, 
  PrismaClientUnknownRequestError, 
  PrismaClientValidationError,
  PrismaClientInitializationError,
  PrismaClientRustPanicError
} from '@prisma/client/runtime/library';

/**
 * Enhanced error types for better error handling
 */
export interface DatabaseError {
  code: string;
  message: string;
  details?: any;
  isRetryable: boolean;
  userMessage: string;
}

/**
 * Common Prisma error codes and their meanings
 */
export const PRISMA_ERROR_CODES = {
  // Connection errors
  P1000: 'Authentication failed',
  P1001: 'Cannot reach database server',
  P1002: 'Database server timeout',
  P1003: 'Database does not exist',
  P1008: 'Operations timed out',
  P1009: 'Database already exists',
  P1010: 'User denied access',
  P1011: 'Error opening TLS connection',
  P1012: 'Schema validation error',
  P1013: 'Invalid database string',
  P1014: 'Underlying kind for model does not exist',
  P1015: 'Unsupported features',
  P1016: 'Incorrect number of parameters',
  P1017: 'Server closed connection',

  // Query errors
  P2000: 'Value too long for column',
  P2001: 'Record not found',
  P2002: 'Unique constraint violation',
  P2003: 'Foreign key constraint violation',
  P2004: 'Constraint violation',
  P2005: 'Invalid value for field',
  P2006: 'Invalid value provided',
  P2007: 'Data validation error',
  P2008: 'Failed to parse query',
  P2009: 'Failed to validate query',
  P2010: 'Raw query failed',
  P2011: 'Null constraint violation',
  P2012: 'Missing required value',
  P2013: 'Missing required argument',
  P2014: 'Required relation violation',
  P2015: 'Related record not found',
  P2016: 'Query interpretation error',
  P2017: 'Records not connected',
  P2018: 'Required connected records not found',
  P2019: 'Input error',
  P2020: 'Value out of range',
  P2021: 'Table does not exist',
  P2022: 'Column does not exist',
  P2023: 'Inconsistent column data',
  P2024: 'Connection pool timeout',
  P2025: 'Record not found in where condition',
  P2026: 'Unsupported feature',
  P2027: 'Multiple errors occurred',
  P2028: 'Transaction API error',
  P2030: 'Cannot find fulltext index',
  P2031: 'MongoDB replica set required',
  P2033: 'Number out of range',
  P2034: 'Transaction conflict',
} as const;

/**
 * Determines if an error is retryable based on its type and code
 */
export const isRetryableError = (error: any): boolean => {
  if (error instanceof PrismaClientKnownRequestError) {
    const retryableCodes = ['P1001', 'P1002', 'P1008', 'P1017', 'P2024', 'P2034'];
    return retryableCodes.includes(error.code);
  }
  
  if (error instanceof PrismaClientInitializationError) {
    return true; // Connection issues are often retryable
  }
  
  return false;
};

/**
 * Converts Prisma errors to user-friendly messages
 */
export const getUserFriendlyMessage = (error: any): string => {
  if (error instanceof PrismaClientKnownRequestError) {
    switch (error.code) {
      case 'P1001':
        return 'Unable to connect to the database. Please try again later.';
      case 'P1002':
      case 'P1008':
        return 'Database operation timed out. Please try again.';
      case 'P2001':
      case 'P2025':
        return 'The requested record was not found.';
      case 'P2002':
        return 'This record already exists. Please use different values.';
      case 'P2003':
        return 'Cannot perform this operation due to related data constraints.';
      case 'P2024':
        return 'Database is temporarily busy. Please try again in a moment.';
      default:
        return 'A database error occurred. Please try again or contact support.';
    }
  }
  
  if (error instanceof PrismaClientValidationError) {
    return 'Invalid data provided. Please check your input and try again.';
  }
  
  if (error instanceof PrismaClientInitializationError) {
    return 'Database connection failed. Please try again later.';
  }
  
  return 'An unexpected error occurred. Please try again or contact support.';
};

/**
 * Enhanced error handler for Prisma operations
 */
export const handlePrismaError = (error: any): DatabaseError => {
  const isRetryable = isRetryableError(error);
  const userMessage = getUserFriendlyMessage(error);
  
  if (error instanceof PrismaClientKnownRequestError) {
    return {
      code: error.code,
      message: error.message,
      details: error.meta,
      isRetryable,
      userMessage,
    };
  }
  
  if (error instanceof PrismaClientValidationError) {
    return {
      code: 'VALIDATION_ERROR',
      message: error.message,
      isRetryable: false,
      userMessage,
    };
  }
  
  if (error instanceof PrismaClientInitializationError) {
    return {
      code: 'INITIALIZATION_ERROR',
      message: error.message,
      isRetryable: true,
      userMessage,
    };
  }
  
  if (error instanceof PrismaClientUnknownRequestError) {
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message,
      isRetryable: false,
      userMessage,
    };
  }
  
  if (error instanceof PrismaClientRustPanicError) {
    return {
      code: 'RUST_PANIC',
      message: error.message,
      isRetryable: false,
      userMessage: 'A critical database error occurred. Please contact support.',
    };
  }
  
  // Handle unknown errors
  return {
    code: 'UNKNOWN',
    message: error?.message || 'Unknown error',
    isRetryable: false,
    userMessage,
  };
};

/**
 * Retry wrapper for database operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const dbError = handlePrismaError(error);
      
      // Don't retry if error is not retryable or this is the last attempt
      if (!dbError.isRetryable || attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      console.warn(`Database operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, dbError.message);
    }
  }
  
  throw lastError;
};

/**
 * Safe database operation wrapper with comprehensive error handling
 */
export const safeDbOperation = async <T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ success: true; data: T } | { success: false; error: DatabaseError }> => {
  try {
    const data = await withRetry(operation);
    return { success: true, data };
  } catch (error) {
    const dbError = handlePrismaError(error);
    
    // Log error for monitoring (in production, this should go to your logging service)
    console.error(`Database operation failed${context ? ` in ${context}` : ''}:`, {
      code: dbError.code,
      message: dbError.message,
      details: dbError.details,
      isRetryable: dbError.isRetryable,
    });
    
    return { success: false, error: dbError };
  }
};
