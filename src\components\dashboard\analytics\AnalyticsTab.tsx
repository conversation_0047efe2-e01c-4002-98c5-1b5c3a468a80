"use client";

import ProductivityIndexScore from "./ProductivityIndexScore";
import WeeklyCodingConsistency from "./WeeklyCodingConsistency";

const AnalyticsTab = () => {
  return (
    <div className="space-y-6">
      {/* Analytics Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent mb-2">
          Performance Analytics
        </h2>
        <p className="text-gray-400">
          Deep insights into your coding journey and progress patterns
        </p>
      </div>

      {/* Productivity Index Score - Full Width Horizontal Card */}
      <ProductivityIndexScore />

      {/* Weekly Coding Consistency */}
      <WeeklyCodingConsistency />
    </div>
  );
};

export default AnalyticsTab;
