import { Button } from "@/components/ui/button";
import { FileQuestion, Home, Search } from "lucide-react";
import Link from "next/link";

export default function NotFound() {
  return (
    <div className="h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      {/* Beautiful gradient background matching home page style */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-30 blur-3xl rounded-full -z-10"></div>
      <div className="absolute top-1/3 right-1/4 h-[300px] w-[300px] bg-gradient-to-br from-purple-800/40 to-cyan-800/40 opacity-20 blur-3xl rounded-full -z-10"></div>

      {/* Floating Code Elements for Personality */}
      <div className="absolute top-20 left-10 text-blue-400/20 text-6xl font-mono animate-pulse">
        {"{"}
      </div>
      <div className="absolute top-32 right-16 text-cyan-400/20 text-4xl font-mono animate-bounce">
        ;
      </div>
      <div className="absolute bottom-20 left-20 text-purple-400/20 text-5xl font-mono animate-pulse">
        {"}"}
      </div>
      <div className="absolute bottom-32 right-12 text-green-400/20 text-3xl font-mono animate-bounce">
        //
      </div>
      <div className="absolute top-1/2 left-8 text-yellow-400/20 text-4xl font-mono animate-pulse">
        []
      </div>
      <div className="absolute top-1/4 right-8 text-red-400/20 text-3xl font-mono animate-bounce">
        ()
      </div>

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Main content */}
      <div className="relative z-10 h-full flex items-center justify-center px-4">
        <div className="max-w-lg w-full text-center space-y-6">
          {/* 404 Icon and Number */}
          <div className="space-y-3">
            <div className="flex justify-center items-center gap-4">
              <div className="relative">
                <FileQuestion className="w-16 h-16 text-blue-400/80 animate-pulse" />
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500/30 border-2 border-red-400/60 rounded-full flex items-center justify-center animate-bounce">
                  <span className="text-red-400 text-xs font-bold">!</span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h1 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent animate-pulse">
                404
              </h1>
              <div className="h-1 w-24 mx-auto bg-gradient-to-r from-blue-500 via-cyan-400 to-blue-600 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Error Message */}
          <div className="space-y-3">
            <h2 className="text-2xl md:text-3xl font-bold text-white">
              Oops! This Page Got Lost in the Code 🔍
            </h2>
            <div className="space-y-2">
              <p className="text-gray-300 max-w-md mx-auto">
                Looks like this page took a wrong turn in our algorithm!
              </p>
              <p className="text-blue-400/80 text-sm font-mono bg-gray-900/30 px-3 py-1 rounded-md mx-auto inline-block">
                Error: Page not found in competitive_programming_universe.exe
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center pt-4">
            <Link href="/" prefetch={false}>
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 hover:scale-105 text-white font-medium px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-blue-500/25"
              >
                <Home className="w-4 h-4" />
                🏠 Take Me Home
              </Button>
            </Link>

            <Link href="/sheetscope" prefetch={false}>
              <Button
                variant="outline"
                size="lg"
                className="border-gray-600 text-gray-300 hover:text-white hover:bg-gray-800/50 hover:scale-105 font-medium px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2 hover:border-cyan-400/50"
              >
                <Search className="w-4 h-4" />
                🔍 Explore SheetScope
              </Button>
            </Link>
          </div>

          {/* Fun Programming Quote */}
          <div className="pt-4">
            <p className="text-cyan-400/70 text-sm italic font-mono bg-gray-900/20 px-4 py-2 rounded-lg border border-gray-800/50">
              "There are only 10 types of people: those who understand binary
              and those who don't!" xD
            </p>
          </div>

          {/* Branding */}
          <div className="pt-4">
            <p className="text-gray-500 text-sm">
              <span className="font-semibold text-white">MyCPTrainer</span> -
              Your Personal CP Trainer ⚡
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
