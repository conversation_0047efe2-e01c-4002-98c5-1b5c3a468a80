"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/lib/auth-context";
import { cn } from "@/lib/utils";
import axios from "axios";
import { ArrowRight, Clock, ExternalLink, User } from "lucide-react";
import * as React from "react";

// ============================================================================
// TWO-PART MODAL COMPONENT INTERFACES
// ============================================================================

interface TwoPartModalProps {
  /** Whether the modal is open */
  isOpen: boolean;
  /** Function to handle modal close */
  onClose: () => void;
  /** Modal title */
  title: string;
  /** Modal description (optional) */
  description?: string;
  /** Function called when both parts are completed */
  onComplete: (handle: string) => void;
  /** Codeforces problem URL to display in part 2 */
  problemUrl?: string;
  /** Function called when verification is successful */
  onVerificationSuccess?: (handle: string) => void;
  /** Function called when verification fails */
  onVerificationFailed?: (handle: string, reason: string) => void;
  /** Custom className for the modal content */
  className?: string;
}

// ============================================================================
// TIMER COMPONENT
// ============================================================================
// Displays a countdown timer with visual feedback

interface TimerProps {
  /** Duration in seconds */
  duration: number;
  /** Function called when timer expires */
  onExpire?: () => void;
  /** Whether the timer is active */
  isActive: boolean;
  /** Custom className */
  className?: string;
  /** End time timestamp for persistent timer */
  endTime?: number;
}

const Timer: React.FC<TimerProps> = ({
  duration,
  onExpire,
  isActive,
  className,
  endTime,
}) => {
  const [timeLeft, setTimeLeft] = React.useState(duration);
  const [hasExpired, setHasExpired] = React.useState(false);

  // Calculate time left based on end time if provided
  React.useEffect(() => {
    if (isActive && endTime) {
      const now = Date.now();
      const remaining = Math.max(0, Math.ceil((endTime - now) / 1000));
      setTimeLeft(remaining);
      setHasExpired(remaining === 0);
    } else if (isActive && !endTime) {
      setTimeLeft(duration);
      setHasExpired(false);
    }
  }, [duration, isActive, endTime]);

  // Timer countdown logic
  React.useEffect(() => {
    if (!isActive || timeLeft <= 0) return;

    const timer = setInterval(() => {
      if (endTime) {
        // Use end time for persistent timer
        const now = Date.now();
        const remaining = Math.max(0, Math.ceil((endTime - now) / 1000));
        setTimeLeft(remaining);
        if (remaining === 0) {
          setHasExpired(true);
        }
      } else {
        // Use countdown for regular timer
        setTimeLeft((prev) => {
          if (prev <= 1) {
            setHasExpired(true);
            return 0;
          }
          return prev - 1;
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, isActive, endTime]);

  // Handle expiration separately to avoid setState during render
  React.useEffect(() => {
    if (hasExpired && timeLeft === 0) {
      onExpire?.();
    }
  }, [hasExpired, timeLeft, onExpire]);

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  };

  // Calculate progress percentage for visual indicator
  const progressPercentage = ((duration - timeLeft) / duration) * 100;

  // Determine color based on time remaining
  const getTimerColor = () => {
    if (timeLeft <= 30) return "text-red-500";
    if (timeLeft <= 60) return "text-yellow-500";
    return "text-green-500";
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <div className="flex items-center gap-2">
        <span className={cn("text-base font-mono font-bold", getTimerColor())}>
          {formatTime(timeLeft)}
        </span>
        {/* Compact progress bar */}
        <div className="w-16 h-1.5 bg-muted rounded-full overflow-hidden">
          <div
            className={cn(
              "h-full transition-all duration-1000 ease-linear",
              timeLeft <= 30
                ? "bg-red-500"
                : timeLeft <= 60
                ? "bg-yellow-500"
                : "bg-green-500"
            )}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>
    </div>
  );
};

// ============================================================================
// TWO-PART MODAL COMPONENT
// ============================================================================
// Part 1: Codeforces handle input (no timer)
// Part 2: 2-minute timer display

export const TwoPartModal: React.FC<TwoPartModalProps> = ({
  isOpen,
  onClose,
  title,
  description,
  onComplete,
  problemUrl: _problemUrl,
  onVerificationSuccess,
  onVerificationFailed,
  className,
}) => {
  // Persistent state management - survives modal close/open
  const [currentPart, setCurrentPart] = React.useState<1 | 2>(1);
  const [codeforcesHandle, setCodeforcesHandle] = React.useState("");
  const [isTimerActive, setIsTimerActive] = React.useState(false);
  const [modalStartTime, setModalStartTime] = React.useState<number>(0);
  const [timerEndTime, setTimerEndTime] = React.useState<number>(0);
  const [userRating, setUserRating] = React.useState<number | null>(null);
  const [userRank, setUserRank] = React.useState<string | null>(null);

  // Non-persistent state - resets on modal open
  const [isVerifying, setIsVerifying] = React.useState(false);
  const [verificationResult, setVerificationResult] = React.useState<{
    success: boolean;
    message: string;
  } | null>(null);
  const [isValidatingHandle, setIsValidatingHandle] = React.useState(false);
  const [handleValidationError, setHandleValidationError] =
    React.useState<string>("");
  const [isCheckingHandleExists, setIsCheckingHandleExists] =
    React.useState(false);
  const [handleExistsError, setHandleExistsError] = React.useState<string>("");
  const [verificationAttempts, setVerificationAttempts] = React.useState<
    number[]
  >([]);
  const [isRateLimited, setIsRateLimited] = React.useState(false);
  const [rateLimitTimeLeft, setRateLimitTimeLeft] = React.useState(0);
  // Note: Confirmation dialog removed - handle transfers are no longer allowed
  const { user } = useAuth();

  // Handle modal state when opening
  React.useEffect(() => {
    if (isOpen) {
      // Reset non-persistent state
      setIsVerifying(false);
      setVerificationResult(null);
      setIsValidatingHandle(false);
      setHandleValidationError("");
      setIsCheckingHandleExists(false);
      setHandleExistsError("");
      setIsRateLimited(false);
      setRateLimitTimeLeft(0);

      // Check if timer has expired while modal was closed
      if (currentPart === 2 && isTimerActive && timerEndTime > 0) {
        const now = Date.now();
        if (now >= timerEndTime) {
          // Timer expired - reset to part 1
          setCurrentPart(1);
          setCodeforcesHandle("");
          setIsTimerActive(false);
          setModalStartTime(0);
          setTimerEndTime(0);
          setUserRating(null);
          setUserRank(null);
        }
        // If timer hasn't expired, keep current state and continue timer
      }
    }
  }, [isOpen, currentPart, isTimerActive, timerEndTime]);

  // Function to validate Codeforces handle and contest participation
  const validateCodeforcesHandle = async (handle: string): Promise<boolean> => {
    if (!handle.trim()) {
      setHandleValidationError("Please enter a Codeforces handle");
      return false;
    }

    setIsValidatingHandle(true);
    setHandleValidationError("");

    try {
      // Step 1: Check if user exists and get basic info
      const userInfoResponse = await fetch(
        `https://codeforces.com/api/user.info?handles=${handle}`
      );
      const userInfoData = await userInfoResponse.json();

      if (
        userInfoData.status !== "OK" ||
        !userInfoData.result ||
        userInfoData.result.length === 0
      ) {
        setHandleValidationError(
          "User not found on Codeforces. Please enter a valid handle."
        );
        setIsValidatingHandle(false);
        return false;
      }

      const userInfo = userInfoData.result[0];
      setUserRating(userInfo.rating || null);
      setUserRank(userInfo.rank || null);

      // Step 2: Check contest participation (minimum 5 contests)
      const contestResponse = await fetch(
        `https://codeforces.com/api/user.rating?handle=${handle}`
      );
      const contestData = await contestResponse.json();

      if (contestData.status !== "OK") {
        setHandleValidationError(
          "Unable to verify contest participation. Please try again."
        );
        setIsValidatingHandle(false);
        return false;
      }

      const contestHistory = contestData.result || [];
      const contestCount = contestHistory.length;

      if (contestCount < 5) {
        setHandleValidationError(
          `Insufficient contest participation. This account has participated in ${contestCount} contests. At least 5 contest participations are required for verification.`
        );
        setIsValidatingHandle(false);
        return false;
      }

      // All validations passed
      setIsValidatingHandle(false);
      return true;
    } catch (error) {
      console.error("Error validating Codeforces handle:", error);
      setHandleValidationError("Error validating handle. Please try again.");
      setIsValidatingHandle(false);
      return false;
    }
  };

  // Function to check if handle already exists in database and meets requirements
  const checkIfHandleExists = async (handle: string): Promise<boolean> => {
    if (!handle.trim()) return false;

    setIsCheckingHandleExists(true);
    setHandleExistsError("");

    try {
      const response = await fetch(
        `/api/codeforces/verifyIfHandleExists?handle=${encodeURIComponent(
          handle
        )}`
      );
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to check handle existence");
      }

      // Check if we can proceed with verification
      if (!data.canProceed) {
        setHandleExistsError(data.message);
        setIsCheckingHandleExists(false);
        return true; // Block verification
      }

      // Show success message if handle is available and meets requirements
      if (data.contestCount) {
        setHandleExistsError(
          `✅ Handle is available for verification (${data.contestCount} contests participated)`
        );
      }

      setIsCheckingHandleExists(false);
      return false; // Can proceed with verification
    } catch (error) {
      console.error("Error checking handle existence:", error);
      setHandleExistsError(
        "Error checking handle availability. Please try again."
      );
      setIsCheckingHandleExists(false);
      return true; // Treat error as blocking to be safe
    }
  };

  // Handle part 1 submission (Codeforces handle)
  const handlePart1Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (codeforcesHandle.trim()) {
      // First validate the handle exists on Codeforces
      const isValid = await validateCodeforcesHandle(codeforcesHandle.trim());
      if (!isValid) return;

      // Then check if handle already exists in our database
      const handleExists = await checkIfHandleExists(codeforcesHandle.trim());
      if (handleExists) return;

      // If both checks pass, proceed to part 2
      const now = Date.now();
      const endTime = now + 120 * 1000; // 2 minutes from now
      setCurrentPart(2);
      setIsTimerActive(true);
      setModalStartTime(now);
      setTimerEndTime(endTime);
    }
  };

  // Handle timer expiration
  const handleTimerExpire = () => {
    setIsTimerActive(false);
    setCurrentPart(1);
    setCodeforcesHandle("");
    setModalStartTime(0);
    setTimerEndTime(0);
    setUserRating(null);
    setUserRank(null);
    setHandleValidationError("");
    setHandleExistsError("");
    setVerificationAttempts([]);
    setIsRateLimited(false);
    setRateLimitTimeLeft(0);
    onComplete(codeforcesHandle);
    onClose();
  };

  // Handle manual completion during timer
  const handleCompleteEarly = () => {
    setIsTimerActive(false);
    setCurrentPart(1);
    setCodeforcesHandle("");
    setModalStartTime(0);
    setTimerEndTime(0);
    setUserRating(null);
    setUserRank(null);
    setHandleValidationError("");
    setHandleExistsError("");
    setVerificationAttempts([]);
    setIsRateLimited(false);
    setRateLimitTimeLeft(0);
    onComplete(codeforcesHandle);
    onClose();
  };

  // Handle going back to part 1
  const handleGoBack = () => {
    setCurrentPart(1);
    setCodeforcesHandle("");
    setIsTimerActive(false);
    setModalStartTime(0);
    setTimerEndTime(0);
    setUserRating(null);
    setUserRank(null);
    setVerificationResult(null);
    setHandleValidationError("");
    setHandleExistsError("");
    setVerificationAttempts([]);
    setIsRateLimited(false);
    setRateLimitTimeLeft(0);
    // Note: Confirmation dialog state cleanup removed
  };

  // Rate limiting logic
  const checkRateLimit = (): boolean => {
    const now = Date.now();
    const oneMinute = 60 * 1000;
    const fiveMinutes = 5 * 60 * 1000;

    // Clean up old attempts (older than 5 minutes)
    const recentAttempts = verificationAttempts.filter(
      (timestamp) => now - timestamp < fiveMinutes
    );
    setVerificationAttempts(recentAttempts);

    // Check if more than 3 attempts in the last minute
    const attemptsInLastMinute = recentAttempts.filter(
      (timestamp) => now - timestamp < oneMinute
    );

    if (attemptsInLastMinute.length >= 3) {
      const oldestAttempt = Math.min(...attemptsInLastMinute);
      const timeUntilReset = oneMinute - (now - oldestAttempt);
      setRateLimitTimeLeft(Math.ceil(timeUntilReset / 1000));
      setIsRateLimited(true);

      // Start countdown timer
      const countdown = setInterval(() => {
        setRateLimitTimeLeft((prev) => {
          if (prev <= 1) {
            setIsRateLimited(false);
            clearInterval(countdown);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return false;
    }

    return true;
  };

  const addVerificationAttempt = () => {
    const now = Date.now();
    setVerificationAttempts((prev) => [...prev, now]);
  };

  // Function to handle verification API call with validation error handling
  const handleVerificationAPI = async () => {
    console.log("handleVerificationAPI called");
    try {
      const response = await axios.post("/api/codeforces/verifyHandle", {
        handle: codeforcesHandle,
        email: user?.email,
        rating: userRating,
        rank: userRank,
      });

      console.log("API response:", response.data);

      // Handle successful verification
      if (response.data.success) {
        const successMessage = response.data.message;

        setVerificationResult({
          success: true,
          message: `✅ ${successMessage}`,
        });

        return true;
      }
    } catch (error: any) {
      console.log("API error:", error.response?.status, error.response?.data);

      // Handle validation errors (400, 409 status codes)
      if (error.response?.status === 409 || error.response?.status === 400) {
        const errorData = error.response.data;
        let errorMessage =
          errorData.message || errorData.error || "Verification failed";

        // Add specific details for contest participation errors
        if (
          errorData.contestCount !== undefined &&
          errorData.requiredContests
        ) {
          errorMessage += ` (Current: ${errorData.contestCount}, Required: ${errorData.requiredContests})`;
        }

        setVerificationResult({
          success: false,
          message: `❌ ${errorMessage}`,
        });
        return false;
      }

      // Handle server errors
      if (error.response?.status >= 500) {
        setVerificationResult({
          success: false,
          message: `❌ Server error: ${
            error.response.data?.message || "Please try again later"
          }`,
        });
        return false;
      }

      // Handle other errors
      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Verification failed";
      setVerificationResult({
        success: false,
        message: `❌ ${errorMessage}`,
      });
      return false;
    }
  };

  // Note: Handle transfer functionality removed - no longer needed

  // Handle verification
  const handleVerify = async () => {
    if (!codeforcesHandle.trim()) return;

    // Check rate limit before proceeding
    if (!checkRateLimit()) {
      return;
    }

    addVerificationAttempt();
    setIsVerifying(true);
    setVerificationResult(null);

    try {
      // Make API call to Codeforces
      const response = await fetch(
        `https://codeforces.com/api/user.status?handle=${codeforcesHandle}&from=1&count=3`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.status !== "OK") {
        throw new Error(data.comment || "API request failed");
      }

      // Check the submissions
      const submissions = data.result || [];
      const currentTime = Date.now();
      const twoMinutesInMs = 2 * 60 * 1000;

      // Find a submission that matches our criteria
      const validSubmission = submissions.find((submission: any) => {
        // Check if submission is within 2 minutes of modal start
        const submissionTime = submission.creationTimeSeconds * 1000;
        const timeDiff = currentTime - submissionTime;
        const isWithinTimeLimit =
          timeDiff <= twoMinutesInMs && submissionTime >= modalStartTime;

        // Check problem details
        const isProblemMatch =
          submission.problem?.name === "Problem with Queries" &&
          submission.problem?.contestId === 2043 &&
          submission.problem?.index === "G";

        // Check verdict
        const isCompilationError = submission.verdict === "COMPILATION_ERROR";

        return isWithinTimeLimit && isProblemMatch && isCompilationError;
      });

      if (validSubmission) {
        setVerificationResult({
          success: true,
          message:
            "✅ Verification successful! Compilation error found for the correct problem.",
        });

        // Call the verification API
        await handleVerificationAPI();

        onVerificationSuccess?.(codeforcesHandle);

        // Auto-close modal after successful verification
        setTimeout(() => {
          setIsTimerActive(false);
          setCurrentPart(1);
          setCodeforcesHandle("");
          setModalStartTime(0);
          setTimerEndTime(0);
          setUserRating(null);
          setUserRank(null);
          setHandleValidationError("");
          setHandleExistsError("");
          setVerificationAttempts([]);
          setIsRateLimited(false);
          setRateLimitTimeLeft(0);
          // Note: Confirmation dialog state cleanup removed
          onComplete(codeforcesHandle);
          onClose();
        }, 2000); // 2 second delay to show success message
      } else {
        // Provide detailed feedback about what went wrong
        let reason = "❌ Verification failed. ";

        const hasAnySubmission = submissions.length > 0;
        if (!hasAnySubmission) {
          reason += "No recent submissions found.";
        } else {
          const reasons = [];

          // Check each condition
          const hasCorrectProblem = submissions.some(
            (s: any) =>
              s.problem?.name === "Problem with Queries" &&
              s.problem?.contestId === 2043 &&
              s.problem?.index === "G"
          );

          const hasCompilationError = submissions.some(
            (s: any) => s.verdict === "COMPILATION_ERROR"
          );

          const hasRecentSubmission = submissions.some((s: any) => {
            const submissionTime = s.creationTimeSeconds * 1000;
            const timeDiff = currentTime - submissionTime;
            return (
              timeDiff <= twoMinutesInMs && submissionTime >= modalStartTime
            );
          });

          if (!hasCorrectProblem) {
            reasons.push("Problem 'Problem with Queries' (2043/G) not found");
          }
          if (!hasCompilationError) {
            reasons.push("No compilation error found");
          }
          if (!hasRecentSubmission) {
            reasons.push("No submission within the last 2 minutes");
          }

          reason += reasons.join(", ") + ".";
        }

        setVerificationResult({
          success: false,
          message: reason,
        });
        onVerificationFailed?.(codeforcesHandle, reason);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      setVerificationResult({
        success: false,
        message: `❌ Error during verification: ${errorMessage}`,
      });
      onVerificationFailed?.(codeforcesHandle, errorMessage);
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle input key down for Enter submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && codeforcesHandle.trim()) {
      e.preventDefault();
      handlePart1Submit(e);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          currentPart === 1
            ? "sm:max-w-xl lg:max-w-2xl"
            : "sm:max-w-4xl lg:max-w-5xl max-h-[85vh] overflow-y-auto",
          className
        )}
      >
        <DialogHeader className="text-center space-y-3">
          <DialogTitle className="flex items-center justify-center gap-3 text-xl">
            {currentPart === 1 ? (
              <>
                <User className="h-6 w-6 text-blue-500" />
                {title}
              </>
            ) : (
              <>
                <Clock className="h-6 w-6 text-green-500" />
                {title}
              </>
            )}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-base">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>

        {/* Part 1: Codeforces Handle Input - Enhanced */}
        {currentPart === 1 && (
          <form onSubmit={handlePart1Submit} className="space-y-6 py-6">
            {/* Welcome Section */}
            <div className="text-center space-y-3">
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground max-w-sm mx-auto">
                  Verify your Codeforces profile by completing a simple task.
                  This ensures you have access to your account.
                </p>
              </div>
            </div>

            {/* Input Section - Enhanced */}
            <div className="space-y-5">
              <div className="space-y-3">
                <div className="space-y-2">
                  <label
                    htmlFor="codeforces-handle"
                    className="block text-sm font-medium text-center"
                  >
                    Codeforces Handle
                  </label>
                  <div className="relative">
                    <Input
                      id="codeforces-handle"
                      placeholder="enter your codeforces handle..."
                      value={codeforcesHandle}
                      onChange={(e) => {
                        setCodeforcesHandle(e.target.value);
                        // Clear validation errors when user starts typing
                        if (handleValidationError) {
                          setHandleValidationError("");
                        }
                        // Clear handle exists errors when user starts typing
                        if (handleExistsError) {
                          setHandleExistsError("");
                        }
                        // Clear previous validation results
                        if (userRating !== null || userRank) {
                          setUserRating(null);
                          setUserRank(null);
                        }
                      }}
                      onKeyDown={handleKeyDown}
                      autoFocus
                      className="text-center font-mono text-lg py-3 h-12 border-2 focus:border-blue-500 transition-all duration-200"
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                      <User className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </div>
                </div>

                {/* Validation Error Display */}
                {handleValidationError && (
                  <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <span className="text-red-600 dark:text-red-400">❌</span>
                      <p className="text-sm text-red-700 dark:text-red-300">
                        {handleValidationError}
                      </p>
                    </div>
                  </div>
                )}

                {/* Handle Exists Error Display */}
                {handleExistsError && (
                  <div className="bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <span className="text-red-600 dark:text-red-400">❌</span>
                      <p className="text-sm text-red-700 dark:text-red-300">
                        {handleExistsError}
                      </p>
                    </div>
                  </div>
                )}

                <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <span className="text-amber-600 dark:text-amber-400">
                      ⚠️
                    </span>
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-amber-800 dark:text-amber-200">
                        Important: Case Sensitive
                      </p>
                      <p className="text-xs text-amber-700 dark:text-amber-300">
                        Enter your handle exactly as it appears on Codeforces.
                        Capital letters matter!
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Process Preview - Compact */}
              <div className="bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
                <div className="text-center mb-4">
                  <h4 className="text-base font-semibold text-slate-800 dark:text-slate-200 mb-1">
                    🚀 What Happens Next?
                  </h4>
                  <p className="text-xs text-slate-600 dark:text-slate-400">
                    Simple 3-step verification process
                  </p>
                </div>

                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center space-y-2">
                    <div className="mx-auto w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <Clock className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <h5 className="font-medium text-xs text-slate-800 dark:text-slate-200">
                        Timer
                      </h5>
                      <p className="text-xs text-slate-600 dark:text-slate-400">
                        2 minutes
                      </p>
                    </div>
                  </div>

                  <div className="text-center space-y-2">
                    <div className="mx-auto w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">⚡</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-xs text-slate-800 dark:text-slate-200">
                        Submit
                      </h5>
                      <p className="text-xs text-slate-600 dark:text-slate-400">
                        Error code
                      </p>
                    </div>
                  </div>

                  <div className="text-center space-y-2">
                    <div className="mx-auto w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">✓</span>
                    </div>
                    <div>
                      <h5 className="font-medium text-xs text-slate-800 dark:text-slate-200">
                        Verify
                      </h5>
                      <p className="text-xs text-slate-600 dark:text-slate-400">
                        Automatic
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter className="pt-4 gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                className="transition-all duration-200 hover:scale-105 active:scale-95 hover:cursor-pointer px-6 py-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  !codeforcesHandle.trim() ||
                  isValidatingHandle ||
                  isCheckingHandleExists
                }
                className="transition-all duration-200 hover:scale-105 active:scale-95 hover:cursor-pointer px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                variant="default"
              >
                {isValidatingHandle ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Validating & Checking Contests...
                  </>
                ) : isCheckingHandleExists ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Checking Availability...
                  </>
                ) : (
                  <>
                    Start Verification
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}

        {/* Part 2: Timer Display - Compact Layout */}
        {currentPart === 2 && (
          <div className="space-y-4">
            {/* Header Section with Handle and Timer */}
            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground">Handle:</span>
                <span className="font-mono font-bold text-lg text-blue-500">
                  {codeforcesHandle}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <Timer
                  duration={120} // 2 minutes
                  onExpire={handleTimerExpire}
                  isActive={isTimerActive}
                  endTime={timerEndTime}
                />
              </div>
            </div>

            {/* Two-Column Layout - Balanced */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Instructions Column */}
              <div className="space-y-4">
                <div className="text-left">
                  <h3 className="text-base font-semibold mb-2">
                    Verification Steps
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Follow these steps to verify your Codeforces profile
                  </p>
                </div>

                <div className="p-5 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl border-2 border-blue-200 dark:border-blue-800">
                  <ol className="space-y-4 text-sm">
                    <li className="flex items-start gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                        1
                      </span>
                      <div className="space-y-2 flex-1">
                        <div className="font-medium">
                          Navigate to problem and open Submit tab
                        </div>
                        <div>
                          <a
                            href="https://codeforces.com/contest/2043/problem/G"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 underline font-medium text-sm"
                          >
                            Problem with Queries (2043/G)
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </div>
                      </div>
                    </li>

                    <li className="flex items-start gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                        2
                      </span>
                      <div className="space-y-2 flex-1">
                        <div className="font-medium">
                          Paste invalid code that will fail to compile
                        </div>
                        <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg text-xs font-mono">
                          <div className="text-green-600 dark:text-green-400">
                            // verify: {codeforcesHandle}
                          </div>
                          <div className="text-red-600 dark:text-red-400">
                            error_here;
                          </div>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Use any programming language (C++, Java, Python, etc.)
                        </div>
                      </div>
                    </li>

                    <li className="flex items-start gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                        3
                      </span>
                      <div className="flex-1">
                        <div className="font-medium">
                          Submit and wait for verdict
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Should show{" "}
                          <span className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded font-mono">
                            Compilation Error
                          </span>{" "}
                          within seconds
                        </div>
                      </div>
                    </li>

                    <li className="flex items-start gap-3">
                      <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">
                        4
                      </span>
                      <div className="flex-1">
                        <div className="font-medium">
                          Click Verify to complete
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          Our system will check your recent submissions
                          automatically
                        </div>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>

              {/* Verification Column */}
              <div className="space-y-4">
                <div className="text-left">
                  <h3 className="text-base font-semibold mb-2">Verification</h3>
                  <p className="text-sm text-muted-foreground">
                    Complete the verification process
                  </p>
                </div>

                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-xl border-2 border-green-200 dark:border-green-800">
                  <Button
                    onClick={handleVerify}
                    disabled={isVerifying || isRateLimited}
                    className="w-full h-12 text-base font-semibold transition-all duration-200 hover:scale-105 active:scale-95 hover:cursor-pointer bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    variant="default"
                  >
                    {isVerifying ? (
                      <>
                        <span className="animate-spin mr-3 text-lg">⏳</span>
                        Verifying Submission...
                      </>
                    ) : isRateLimited ? (
                      <>
                        <span className="mr-3 text-lg">⏱️</span>
                        Wait {rateLimitTimeLeft}s before retrying
                      </>
                    ) : (
                      <>
                        <span className="mr-3 text-lg">🔍</span>
                        Verify My Submission
                      </>
                    )}
                  </Button>

                  {/* Rate Limit Warning */}
                  <div className="mt-3 p-2 bg-orange-50 dark:bg-orange-950/20 rounded border border-orange-200 dark:border-orange-800">
                    <p className="text-xs text-orange-700 dark:text-orange-300 flex items-center gap-1">
                      <span className="text-orange-600 dark:text-orange-400">
                        ⚠️
                      </span>
                      <span className="font-medium">Rate limited:</span> 3
                      attempts/min. Excessive requests may cause IP blocking.
                    </p>
                  </div>

                  {/* Verification Result */}
                  {verificationResult && (
                    <div
                      className={cn(
                        "mt-4 p-4 rounded-lg border-2 text-sm font-medium",
                        verificationResult.success
                          ? "bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-700 text-green-800 dark:text-green-200"
                          : "bg-red-50 dark:bg-red-900/20 border-red-300 dark:border-red-700 text-red-800 dark:text-red-200"
                      )}
                    >
                      {verificationResult.message}
                    </div>
                  )}

                  {/* Rate Limit Status */}
                  {isRateLimited && (
                    <div className="mt-2 p-2 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800">
                      <p className="text-xs text-red-700 dark:text-red-300 flex items-center gap-1">
                        <span className="text-red-600 dark:text-red-400">
                          🚫
                        </span>
                        <span className="font-medium">
                          Rate limit exceeded.
                        </span>{" "}
                        Wait {rateLimitTimeLeft}s to retry.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Footer - Auto-closes on verification success or timer expiration */}
            <DialogFooter className="pt-4 gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleGoBack}
                className="transition-all duration-200 hover:scale-105 active:scale-95 hover:cursor-pointer"
              >
                ← Back to Handle
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleCompleteEarly}
                className="transition-all duration-200 hover:scale-105 active:scale-95 hover:cursor-pointer"
              >
                Cancel
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>

      {/* Note: Confirmation dialog removed - handle transfers no longer allowed */}
    </Dialog>
  );
};

// ============================================================================
// USAGE EXAMPLE COMPONENT
// ============================================================================

export const TwoPartModalExample: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [lastResult, setLastResult] = React.useState<string>("");

  const handleComplete = (handle: string) => {
    setLastResult(
      `Completed with handle: ${handle} at ${new Date().toLocaleTimeString()}`
    );
    // console.log("Modal completed with handle:", handle);
  };

  const handleVerificationSuccess = (handle: string) => {
    setLastResult(
      `✅ Verification successful for: ${handle} at ${new Date().toLocaleTimeString()}`
    );
  };

  const handleVerificationFailed = (handle: string, reason: string) => {
    setLastResult(`❌ Verification failed for: ${handle} - ${reason}`);
  };

  return (
    <div className="p-4 space-y-4">
      <Button
        onClick={() => setIsModalOpen(true)}
        className="hover:cursor-pointer"
      >
        Open Two-Part Modal
      </Button>

      {lastResult && (
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-sm font-mono">{lastResult}</p>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setLastResult("")}
            className="mt-2 hover:cursor-pointer"
          >
            Clear
          </Button>
        </div>
      )}

      <TwoPartModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Codeforces Challenge"
        description="First enter your handle, then complete the verification challenge."
        onComplete={handleComplete}
        onVerificationSuccess={handleVerificationSuccess}
        onVerificationFailed={handleVerificationFailed}
      />
    </div>
  );
};
