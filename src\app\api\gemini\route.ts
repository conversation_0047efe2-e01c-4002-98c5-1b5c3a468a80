import { GoogleGenAI } from "@google/genai";
import { NextRequest, NextResponse } from "next/server";

const ai = new GoogleGenAI({
  apiKey: process.env.GEMINI_API_KEY!,
});

export async function POST(request: NextRequest) {
  try {
    const { prompt, userCode } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required" },
        { status: 400 }
      );
    }

    const response = await ai.models.generateContent({
      model: "gemini-2.0-flash",
      contents: [{ role: "user", parts: [{ text: prompt }] }],
    });
    const originalCorrectCode =
      response.candidates?.[0]?.content?.parts?.[0]?.text;

    const PromptToGetFailedTestCases = `
      You are a precise test case analyzer.
      
      Your job:
      - Compare the correct code and the user's incorrect code.
      - Identify and return only the *minimal set* of failed test cases.
      - For each failed case, include:
        - Input
        - Expected Output
        - Why it failed (briefly)
        - Step-by-step dry run showing how the user's code went wrong
      
      Constraints:
      - Plain text only
      - No headings, no explanations, no markdown
      - Keep output concise
      
      Correct Code:
      ${originalCorrectCode}
      
      User Code:
      ${userCode}
      
      Format strictly like this:
      
      Input: ...
      Expected Output: ...
      Why it failed: ...
      Dry Run: ...
      `;

    const responseToGetFailedTestCases = await ai.models.generateContent({
      model: "gemini-2.5-pro",
      contents: [
        { role: "user", parts: [{ text: PromptToGetFailedTestCases }] },
      ],
    });

    const failedTestCases =
      responseToGetFailedTestCases.candidates?.[0]?.content?.parts?.[0]?.text;
    return NextResponse.json({ failedTestCases });
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
