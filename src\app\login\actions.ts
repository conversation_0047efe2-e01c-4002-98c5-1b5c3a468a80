"use server";

import { createClient } from "@/lib/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export async function signInWithGoogle() {
  const origin = (await headers()).get("origin");
  const supabase = await createClient();

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || origin;
  // console.log("siteUrl", origin);
  if (!siteUrl) {
    const errorMessage =
      "Cannot determine site URL. Please set NEXT_PUBLIC_SITE_URL environment variable.";
    return redirect(`/error?message=${encodeURIComponent(errorMessage)}`);
  }

  const redirectTo = `${siteUrl}/auth/callback`;

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo,
    },
  });

  if (error) {
    redirect(`/error?message=${encodeURIComponent(error.message)}`);
  }

  if (data.url) {
    redirect(data.url);
  }
}
