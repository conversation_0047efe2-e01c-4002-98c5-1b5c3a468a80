import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  // TEMPORARY: Return mock data for development
  if (process.env.NODE_ENV === "development") {
    const mockUserData = {
      id: 1,
      handle: "UdayRajVadeghar",
      rating: 1450,
      rank: "Specialist",
      streak: 5,
      training_streak: 12,
      max_training_streak: 25,
      maxSheetSlots: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      email: "<EMAIL>",
      totalSheets: 1,
      availableSheetSlots: 2,
      sheets: [
        {
          id: 1,
          name: "Practice Sheet 1",
          createdAt: new Date().toISOString(),
        },
      ],
      supabaseUser: {
        id: "dev-user-123",
        email: "<EMAIL>",
        created_at: new Date().toISOString(),
        user_metadata: {},
      },
    };

    return new Response(JSON.stringify(mockUserData), {
      headers: { "Content-Type": "application/json" },
    });
  }

  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  try {
    const dbUser = await prisma.users.findFirst({
      where: {
        email: email,
      },
      select: {
        id: true,
        handle: true,
        rating: true,
        rank: true,
        streak: true,
        training_streak: true,
        max_training_streak: true,
        maxSheetSlots: true,
        createdAt: true,
        updatedAt: true,
        email: true,
        sheets: {
          select: {
            id: true,
            name: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!dbUser) {
      return new Response("User not found", { status: 404 });
    }

    // Calculate derived fields
    const totalSheets = dbUser.sheets?.length || 0;
    const maxSheetSlots = dbUser.maxSheetSlots || 1; // Default to 1 if not set
    const availableSheetSlots = Math.max(0, maxSheetSlots - totalSheets);

    // Add the supabase user data and calculated fields
    const responseData = {
      ...dbUser,
      totalSheets,
      availableSheetSlots,
      supabaseUser: {
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        user_metadata: user.user_metadata,
      },
    };

    return new Response(JSON.stringify(responseData), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Database error: Failed to fetch user", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
