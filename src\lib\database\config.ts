/**
 * Database configuration utilities
 * Provides environment-specific database settings and validation
 */

export interface DatabaseEnvironmentConfig {
  url: string;
  connectionLimit: number;
  poolTimeout: number;
  connectTimeout: number;
  ssl: boolean;
  logLevel: 'query' | 'info' | 'warn' | 'error';
  enableMetrics: boolean;
}

/**
 * Environment-specific database configurations
 */
export const DATABASE_CONFIGS = {
  development: {
    connectionLimit: 5,
    poolTimeout: 10,
    connectTimeout: 10,
    ssl: false,
    logLevel: 'query' as const,
    enableMetrics: true,
  },
  test: {
    connectionLimit: 2,
    poolTimeout: 5,
    connectTimeout: 5,
    ssl: false,
    logLevel: 'error' as const,
    enableMetrics: false,
  },
  production: {
    connectionLimit: 1, // Optimal for serverless
    poolTimeout: 20,
    connectTimeout: 15,
    ssl: true,
    logLevel: 'error' as const,
    enableMetrics: true,
  },
} as const;

/**
 * Validates DATABASE_URL format and structure
 */
export const validateDatabaseUrl = (url: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!url) {
    errors.push('DATABASE_URL is required');
    return { isValid: false, errors };
  }

  try {
    const parsedUrl = new URL(url);
    
    // Check protocol
    if (!['postgresql:', 'postgres:', 'mysql:', 'sqlserver:'].includes(parsedUrl.protocol)) {
      errors.push(`Unsupported database protocol: ${parsedUrl.protocol}`);
    }
    
    // Check hostname
    if (!parsedUrl.hostname) {
      errors.push('Database hostname is required');
    }
    
    // Check for Supabase-specific optimizations
    if (parsedUrl.hostname.includes('supabase.com') || parsedUrl.hostname.includes('supabase.co')) {
      // Check if using pooled connection for production
      if (process.env.NODE_ENV === 'production' && !parsedUrl.hostname.includes('pooler')) {
        errors.push('Production should use Supabase pooled connection (hostname should contain "pooler")');
      }
      
      // Check for required Supabase parameters
      if (process.env.NODE_ENV === 'production') {
        if (!parsedUrl.searchParams.has('pgbouncer')) {
          errors.push('Supabase production connections should include pgbouncer=true parameter');
        }
      }
    }
    
    // Check for connection pooling parameters
    const connectionLimit = parsedUrl.searchParams.get('connection_limit');
    if (process.env.NODE_ENV === 'production' && !connectionLimit) {
      errors.push('Production connections should specify connection_limit parameter');
    }
    
  } catch (error) {
    errors.push(`Invalid DATABASE_URL format: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
  
  return { isValid: errors.length === 0, errors };
};

/**
 * Generates optimized DATABASE_URL for different environments
 */
export const generateOptimizedDatabaseUrl = (
  baseUrl: string,
  environment: keyof typeof DATABASE_CONFIGS = 'production'
): string => {
  try {
    const url = new URL(baseUrl);
    const config = DATABASE_CONFIGS[environment];
    
    // Add connection pooling parameters
    url.searchParams.set('connection_limit', config.connectionLimit.toString());
    url.searchParams.set('pool_timeout', config.poolTimeout.toString());
    url.searchParams.set('connect_timeout', config.connectTimeout.toString());
    
    // Add SSL configuration for production
    if (environment === 'production' && config.ssl) {
      if (url.protocol === 'postgresql:' || url.protocol === 'postgres:') {
        url.searchParams.set('sslmode', 'require');
      }
    }
    
    // Add Supabase-specific optimizations
    if (url.hostname.includes('supabase')) {
      if (environment === 'production') {
        url.searchParams.set('pgbouncer', 'true');
        
        // Ensure using pooled endpoint for production
        if (!url.hostname.includes('pooler')) {
          url.hostname = url.hostname.replace('.supabase.co', '.pooler.supabase.com');
          url.port = '6543'; // Transaction pooler port
        }
      }
    }
    
    return url.toString();
  } catch (error) {
    console.error('Failed to optimize DATABASE_URL:', error);
    return baseUrl;
  }
};

/**
 * Gets the current environment configuration
 */
export const getCurrentEnvironmentConfig = (): DatabaseEnvironmentConfig => {
  const environment = (process.env.NODE_ENV as keyof typeof DATABASE_CONFIGS) || 'development';
  const config = DATABASE_CONFIGS[environment];
  const baseUrl = process.env.DATABASE_URL || '';
  
  return {
    url: generateOptimizedDatabaseUrl(baseUrl, environment),
    ...config,
  };
};

/**
 * Validates environment variables required for database operation
 */
export const validateEnvironmentVariables = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check required environment variables
  if (!process.env.DATABASE_URL) {
    errors.push('DATABASE_URL environment variable is required');
  }
  
  // Validate DATABASE_URL format
  if (process.env.DATABASE_URL) {
    const urlValidation = validateDatabaseUrl(process.env.DATABASE_URL);
    if (!urlValidation.isValid) {
      errors.push(...urlValidation.errors);
    }
  }
  
  // Check NODE_ENV
  const validEnvironments = ['development', 'test', 'production'];
  if (process.env.NODE_ENV && !validEnvironments.includes(process.env.NODE_ENV)) {
    errors.push(`Invalid NODE_ENV: ${process.env.NODE_ENV}. Must be one of: ${validEnvironments.join(', ')}`);
  }
  
  return { isValid: errors.length === 0, errors };
};

/**
 * Supabase-specific configuration helpers
 */
export const supabaseHelpers = {
  /**
   * Converts direct Supabase URL to pooled URL
   */
  convertToPooledUrl: (directUrl: string): string => {
    try {
      const url = new URL(directUrl);
      
      if (url.hostname.includes('supabase.co')) {
        // Convert to pooled endpoint
        url.hostname = url.hostname.replace('.supabase.co', '.pooler.supabase.com');
        url.port = '6543';
        url.searchParams.set('pgbouncer', 'true');
        url.searchParams.set('connection_limit', '1');
      }
      
      return url.toString();
    } catch (error) {
      console.error('Failed to convert to pooled URL:', error);
      return directUrl;
    }
  },

  /**
   * Extracts Supabase project details from URL
   */
  getProjectDetails: (url: string): { projectId?: string; region?: string; isPooled: boolean } => {
    try {
      const parsedUrl = new URL(url);
      const hostname = parsedUrl.hostname;
      
      if (hostname.includes('supabase')) {
        const parts = hostname.split('.');
        const projectId = parts[0];
        const isPooled = hostname.includes('pooler');
        
        // Extract region if present
        const regionMatch = hostname.match(/([a-z]+-[a-z]+-\d+)/);
        const region = regionMatch ? regionMatch[1] : undefined;
        
        return { projectId, region, isPooled };
      }
      
      return { isPooled: false };
    } catch (error) {
      return { isPooled: false };
    }
  },

  /**
   * Generates recommended Supabase connection strings
   */
  generateRecommendedUrls: (baseUrl: string) => {
    const development = baseUrl; // Direct connection for development
    const production = supabaseHelpers.convertToPooledUrl(baseUrl);
    
    return {
      development,
      production,
      recommendations: [
        'Use direct connection for development (faster for single connections)',
        'Use pooled connection for production (better for serverless)',
        'Set connection_limit=1 for serverless environments',
        'Enable pgbouncer for production workloads',
      ],
    };
  },
};

/**
 * Database configuration diagnostics
 */
export const runConfigurationDiagnostics = () => {
  console.log('🔍 Running database configuration diagnostics...');
  
  const envValidation = validateEnvironmentVariables();
  const currentConfig = getCurrentEnvironmentConfig();
  
  const diagnostics = {
    environment: process.env.NODE_ENV || 'development',
    validation: envValidation,
    currentConfig,
    recommendations: [] as string[],
  };
  
  // Generate recommendations
  if (!envValidation.isValid) {
    diagnostics.recommendations.push('Fix environment variable issues before proceeding');
  }
  
  if (process.env.NODE_ENV === 'production') {
    if (currentConfig.connectionLimit > 1) {
      diagnostics.recommendations.push('Consider setting connection_limit=1 for serverless environments');
    }
    
    if (!currentConfig.ssl) {
      diagnostics.recommendations.push('Enable SSL for production database connections');
    }
  }
  
  if (process.env.DATABASE_URL?.includes('supabase')) {
    const projectDetails = supabaseHelpers.getProjectDetails(process.env.DATABASE_URL);
    if (process.env.NODE_ENV === 'production' && !projectDetails.isPooled) {
      diagnostics.recommendations.push('Use Supabase pooled connection for production');
    }
  }
  
  return diagnostics;
};
