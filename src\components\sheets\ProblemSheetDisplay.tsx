"use client";

import type { CodeforcesSubmission } from "@/lib/codeforces";
import {
  generateProblemId,
  getCompletionData,
  isProblemCompleted,
  markProblemCompleted,
  markProblemIncomplete,
} from "@/lib/codingSheet";
import React from "react";

// ============================================================================
// PROBLEM SHEET DISPLAY COMPONENT
// ============================================================================
// Displays problems in a clean, printable sheet format
// Includes checkboxes, problem links, tags, and completion tracking

interface ProblemSheetDisplayProps {
  problems: CodeforcesSubmission[]; // Array of problems to display
  title?: string; // Optional title for the sheet
  showCompletionStats?: boolean; // Whether to show completion statistics
  onProblemToggle?: (problemId: string, completed: boolean) => void; // Callback when problem completion changes
}

// Helper function to get rating color
const getRatingColor = (rating?: number): string => {
  if (!rating) return "#808080"; // Unrated (gray)
  if (rating < 1200) return "#808080"; // Newbie (gray)
  if (rating < 1400) return "#008000"; // Pupil (green)
  if (rating < 1600) return "#03A89E"; // Specialist (cyan)
  if (rating < 1900) return "#0000FF"; // Expert (blue)
  if (rating < 2100) return "#AA00AA"; // Candidate Master (violet)
  if (rating < 2300) return "#FF8C00"; // Master (orange)
  if (rating < 2400) return "#FF8C00"; // International Master (orange)
  if (rating < 2600) return "#FF0000"; // Grandmaster (red)
  if (rating < 3000) return "#FF0000"; // International Grandmaster (red)
  return "#AA0000"; // Legendary Grandmaster (dark red)
};

// Helper function to format date
const formatDate = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export const ProblemSheetDisplay: React.FC<ProblemSheetDisplayProps> = ({
  problems,
  title = "Coding Practice Sheet",
  showCompletionStats = true,
  onProblemToggle,
}) => {
  // State for problem completion status (local state for immediate UI updates)
  const [localCompletionStatus, setLocalCompletionStatus] = React.useState<
    Record<string, boolean>
  >({});
  const [completionData, setCompletionData] = React.useState<
    Record<string, any>
  >({});

  // Initialize local completion status from localStorage
  React.useEffect(() => {
    const initialStatus: Record<string, boolean> = {};
    problems.forEach((problem) => {
      const problemId = generateProblemId(problem);
      initialStatus[problemId] = isProblemCompleted(problemId);
    });
    setLocalCompletionStatus(initialStatus);
    setCompletionData(getCompletionData());
  }, [problems]);

  // Calculate completion statistics
  const completionStats = React.useMemo(() => {
    const completed = Object.values(localCompletionStatus).filter(
      Boolean
    ).length;
    const total = problems.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    return { completed, total, percentage };
  }, [localCompletionStatus, problems.length]);

  // Handle problem completion toggle
  const handleProblemToggle = (problem: CodeforcesSubmission) => {
    const problemId = generateProblemId(problem);
    const currentStatus = localCompletionStatus[problemId] || false;
    const newStatus = !currentStatus;

    // Update local state immediately for responsive UI
    setLocalCompletionStatus((prev) => ({
      ...prev,
      [problemId]: newStatus,
    }));

    // Update localStorage
    if (newStatus) {
      markProblemCompleted(problemId);
    } else {
      markProblemIncomplete(problemId);
    }

    // Update completion data
    setCompletionData(getCompletionData());

    // Call parent callback if provided
    onProblemToggle?.(problemId, newStatus);
  };

  // Generate problem URL
  const getProblemUrl = (problem: CodeforcesSubmission): string => {
    const { problem: p } = problem;
    if (p.contestId) {
      return `https://codeforces.com/contest/${p.contestId}/problem/${p.index}`;
    } else if (p.problemsetName) {
      return `https://codeforces.com/problemset/problem/${p.problemsetName}/${p.index}`;
    }
    return "https://codeforces.com/problemset";
  };

  if (problems.length === 0) {
    return (
      <div className="bg-gray-800 border border-gray-700 rounded-lg p-8 text-center">
        <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-300 mb-2">
          No Problems in Sheet
        </h3>
        <p className="text-gray-400">
          Add some problems to get started with your coding practice.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 rounded-xl shadow-lg">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-200 mb-1">{title}</h2>
            <p className="text-gray-400">
              {problems.length} problem{problems.length !== 1 ? "s" : ""} •
              Created {new Date().toLocaleDateString()}
            </p>
          </div>
          {showCompletionStats && (
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-500">
                {completionStats.completed} / {completionStats.total}
              </div>
              <div className="text-sm text-gray-400">
                {completionStats.percentage}% Complete
              </div>
            </div>
          )}
        </div>

        {/* Progress Bar */}
        {showCompletionStats && (
          <div className="mt-4">
            <div className="w-full bg-gray-600 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 shadow-lg shadow-blue-500/30"
                style={{ width: `${completionStats.percentage}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>

      {/* Problems List */}
      <div className="p-6">
        <div className="space-y-3">
          {problems.map((problem, index) => {
            const problemId = generateProblemId(problem);
            const isCompleted = localCompletionStatus[problemId] || false;
            const problemUrl = getProblemUrl(problem);
            const ratingColor = getRatingColor(problem.problem.rating);
            const completion = completionData[problemId];

            return (
              <div
                key={`${problemId}-${index}`}
                className={`p-4 rounded-lg border transition-all duration-200 ${
                  isCompleted
                    ? "bg-green-900/20 border-green-700/50 shadow-sm"
                    : "bg-gray-700/50 border-gray-600 hover:bg-gray-700/70"
                }`}
              >
                <div className="flex items-start gap-4">
                  {/* Checkbox */}
                  <div className="flex items-center pt-1">
                    <input
                      type="checkbox"
                      checked={isCompleted}
                      onChange={() => handleProblemToggle(problem)}
                      className="w-5 h-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 cursor-pointer"
                    />
                  </div>

                  {/* Problem Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <a
                          href={problemUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`text-lg font-semibold transition-colors cursor-pointer ${
                            isCompleted
                              ? "text-green-400 hover:text-green-300 line-through"
                              : "text-blue-500 hover:text-blue-400"
                          }`}
                        >
                          {problem.problem.contestId
                            ? `${problem.problem.contestId}${problem.problem.index}. ${problem.problem.name}`
                            : `${problem.problem.index}. ${problem.problem.name}`}
                        </a>
                      </div>

                      {/* Rating Badge */}
                      {problem.problem.rating && (
                        <span
                          className="px-3 py-1 rounded-full text-sm font-medium ml-4 flex-shrink-0"
                          style={{
                            backgroundColor: `${ratingColor}20`,
                            color: ratingColor,
                            border: `1px solid ${ratingColor}40`,
                          }}
                        >
                          {problem.problem.rating}
                        </span>
                      )}
                    </div>

                    {/* Problem Details */}
                    <div className="flex items-center gap-4 text-sm text-gray-400 mb-2">
                      <span>
                        Solved: {formatDate(problem.creationTimeSeconds)}
                      </span>
                      {problem.problem.contestId && (
                        <span>Contest: {problem.problem.contestId}</span>
                      )}
                      {problem.programmingLanguage && (
                        <span>Language: {problem.programmingLanguage}</span>
                      )}
                    </div>

                    {/* Tags */}
                    {problem.problem.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {problem.problem.tags.map((tag, tagIndex) => (
                          <span
                            key={`${tag}-${tagIndex}`}
                            className="px-2 py-1 bg-gray-600 text-gray-300 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}

                    {/* Completion Info */}
                    {isCompleted && completion && (
                      <div className="text-xs text-green-400 mt-2">
                        ✓ Completed on{" "}
                        {new Date(completion.completedAt).toLocaleDateString()}
                        {completion.notes && (
                          <span className="text-gray-400 ml-2">
                            • {completion.notes}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Action Button */}
                  <div className="flex-shrink-0">
                    <a
                      href={problemUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-all duration-200 cursor-pointer hover:scale-105 active:scale-95"
                    >
                      {isCompleted ? "Review" : "Solve"}
                    </a>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-700 bg-gray-800/50">
        <div className="flex items-center justify-between text-sm text-gray-400">
          <div>
            Total: {problems.length} problems • Completed:{" "}
            {completionStats.completed} • Remaining:{" "}
            {problems.length - completionStats.completed}
          </div>
          <div>Last updated: {new Date().toLocaleTimeString()}</div>
        </div>
      </div>
    </div>
  );
};
