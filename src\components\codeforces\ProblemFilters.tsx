"use client";

import type { CodeforcesSubmission } from "@/lib/codeforces";
import React from "react";

// ============================================================================
// PROBLEM FILTERS COMPONENT
// ============================================================================
// Comprehensive filtering component for Codeforces problems
// Allows filtering by difficulty rating, tags, and date

interface ProblemFiltersProps {
  submissions: CodeforcesSubmission[]; // All submissions to filter
  onFilteredSubmissions: (filtered: CodeforcesSubmission[]) => void; // Callback with filtered results
  isLoading?: boolean; // Whether submissions are still loading
}

// Filter state interface
interface FilterState {
  minRating: number | null; // Minimum difficulty rating
  maxRating: number | null; // Maximum difficulty rating
  selectedTags: string[]; // Selected problem tags
  dateFilter: "all" | "today" | "yesterday" | "last7days" | "last30days"; // Date filter options (removed custom)
}

export const ProblemFilters: React.FC<ProblemFiltersProps> = ({
  submissions,
  onFilteredSubmissions,
  isLoading = false,
}) => {
  // State for all filter criteria
  const [filters, setFilters] = React.useState<FilterState>({
    minRating: null,
    maxRating: null,
    selectedTags: [],
    dateFilter: "all",
  });

  // State for collapsible filter results section to prevent layout shifts
  const [isFilterResultsExpanded, setIsFilterResultsExpanded] =
    React.useState(false);

  // Applied filters state - only updated when filter button is clicked
  // This prevents real-time filtering and improves performance
  const [appliedFilters, setAppliedFilters] = React.useState<FilterState>({
    minRating: null,
    maxRating: null,
    selectedTags: [],
    dateFilter: "all",
  });

  // State for apply filters feedback
  const [isFiltersApplied, setIsFiltersApplied] = React.useState(false);

  // Extract unique tags from all submissions for the tag filter
  const availableTags = React.useMemo(() => {
    const tagSet = new Set<string>();
    submissions.forEach((submission) => {
      submission.problem.tags.forEach((tag) => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [submissions]);

  // Extract rating range from submissions for rating filter bounds
  const ratingRange = React.useMemo(() => {
    const ratings = submissions
      .map((s) => s.problem.rating)
      .filter((rating): rating is number => rating !== undefined);

    if (ratings.length === 0) return { min: 800, max: 3500 };

    return {
      min: Math.min(...ratings),
      max: Math.max(...ratings),
    };
  }, [submissions]);

  // Date range calculation for applied filters (used in actual filtering)
  // This function uses appliedFilters instead of current filters to prevent real-time filtering
  const getDateRangeForAppliedFilters = React.useCallback(() => {
    const now = new Date();

    switch (appliedFilters.dateFilter) {
      case "today":
        const startOfToday = new Date(now);
        startOfToday.setHours(0, 0, 0, 0);
        const endOfToday = new Date(now);
        endOfToday.setHours(23, 59, 59, 999);
        return {
          start: Math.floor(startOfToday.getTime() / 1000),
          end: Math.floor(endOfToday.getTime() / 1000),
        };
      case "yesterday":
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        const startOfYesterday = new Date(yesterday);
        startOfYesterday.setHours(0, 0, 0, 0);
        const endOfYesterday = new Date(yesterday);
        endOfYesterday.setHours(23, 59, 59, 999);
        return {
          start: Math.floor(startOfYesterday.getTime() / 1000),
          end: Math.floor(endOfYesterday.getTime() / 1000),
        };
      case "last7days":
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return {
          start: Math.floor(sevenDaysAgo.getTime() / 1000),
          end: Math.floor(now.getTime() / 1000),
        };
      case "last30days":
        const thirtyDaysAgo = new Date(now);
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        return {
          start: Math.floor(thirtyDaysAgo.getTime() / 1000),
          end: Math.floor(now.getTime() / 1000),
        };
      default:
        return null;
    }
  }, [appliedFilters.dateFilter]);

  // Apply all filters to submissions using appliedFilters instead of real-time filters
  // This ensures filtering only happens when the filter button is clicked
  const filteredSubmissions = React.useMemo(() => {
    return submissions.filter((submission) => {
      const problem = submission.problem;

      // Exclude unrated problems (problems without a rating)
      if (problem.rating === undefined || problem.rating === null) {
        return false;
      }

      // Date filter using applied filters
      const dateRange = getDateRangeForAppliedFilters();
      if (dateRange) {
        if (
          submission.creationTimeSeconds < dateRange.start ||
          submission.creationTimeSeconds > dateRange.end
        ) {
          return false;
        }
      }

      // Rating range filter using applied filters
      if (problem.rating !== undefined) {
        if (
          appliedFilters.minRating !== null &&
          problem.rating < appliedFilters.minRating
        ) {
          return false;
        }
        if (
          appliedFilters.maxRating !== null &&
          problem.rating > appliedFilters.maxRating
        ) {
          return false;
        }
      }

      // Tags filter (AND logic - must include ALL selected tags) using applied filters
      // This ensures that problems are only shown if they contain every single selected tag
      // Example: If user selects ["greedy", "dp"], only problems that have BOTH "greedy" AND "dp" tags will be shown
      // Problems with only "greedy" OR only "dp" will be filtered out
      if (appliedFilters.selectedTags.length > 0) {
        // Normalize tags for better matching (handle case sensitivity and spacing)
        const normalizeTag = (tag: string) => tag.toLowerCase().trim();

        const normalizedSelectedTags =
          appliedFilters.selectedTags.map(normalizeTag);
        const normalizedProblemTags = problem.tags.map(normalizeTag);

        const hasAllTags = normalizedSelectedTags.every((selectedTag) =>
          normalizedProblemTags.includes(selectedTag)
        );

        // Enhanced debug logging to show AND logic in action and help debug tag matching issues
        if (appliedFilters.selectedTags.length > 1) {
          // Special debugging for chinese remainder theorem issues
          const hasChineseTag = normalizedSelectedTags.some((tag) =>
            tag.includes("chinese")
          );

          if (hasChineseTag || hasAllTags) {
            // console.log(`🔍 Checking problem "${problem.name}":`, {
            //   selectedTags: appliedFilters.selectedTags,
            //   normalizedSelectedTags,
            //   problemTags: problem.tags,
            //   normalizedProblemTags,
            //   hasAllTags,
            //   missingTags: normalizedSelectedTags.filter(
            //     (tag) => !normalizedProblemTags.includes(tag)
            //   ),
            //   chineseTagCheck: hasChineseTag
            //     ? {
            //         chineseTagsSelected: normalizedSelectedTags.filter((tag) =>
            //           tag.includes("chinese")
            //         ),
            //         chineseTagsInProblem: normalizedProblemTags.filter((tag) =>
            //           tag.includes("chinese")
            //         ),
            //       }
            //     : null,
            // });
          }
        }

        if (!hasAllTags) {
          return false;
        }
      }

      return true;
    });
  }, [submissions, appliedFilters, getDateRangeForAppliedFilters]);

  // Update parent component when filtered submissions change
  React.useEffect(() => {
    onFilteredSubmissions(filteredSubmissions);
  }, [filteredSubmissions, onFilteredSubmissions]);

  // Apply initial filters when component mounts or submissions change
  // This ensures problems are shown initially without requiring a button click
  React.useEffect(() => {
    if (submissions.length > 0) {
      applyFilters();
    }
  }, [submissions]);

  // Handle tag selection/deselection
  const toggleTag = (tag: string) => {
    setFilters((prev) => ({
      ...prev,
      selectedTags: prev.selectedTags.includes(tag)
        ? prev.selectedTags.filter((t) => t !== tag)
        : [...prev.selectedTags, tag],
    }));
  };

  // Apply filters function - triggered by button click
  // This copies current filter state to appliedFilters to trigger actual filtering
  const applyFilters = () => {
    setAppliedFilters({ ...filters });

    // Show success feedback
    setIsFiltersApplied(true);

    // Clear feedback after 2 seconds
    setTimeout(() => {
      setIsFiltersApplied(false);
    }, 2000);
  };

  // Reset all filters
  const resetFilters = () => {
    const defaultFilters = {
      minRating: null,
      maxRating: null,
      selectedTags: [],
      dateFilter: "all" as const,
    };
    setFilters(defaultFilters);
    setAppliedFilters(defaultFilters);
    setIsFiltersApplied(false); // Clear any feedback state
  };

  if (isLoading) {
    return (
      <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-10 bg-gray-700 rounded"></div>
            <div className="h-10 bg-gray-700 rounded"></div>
            <div className="h-20 bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-4 h-4 bg-blue-400 rounded-full"></div>
          <h3 className="text-xl font-semibold text-gray-200">
            Filter Selected Problems
          </h3>
        </div>
        <div className="flex gap-2">
          <button
            onClick={applyFilters}
            className={`px-4 py-2 text-sm rounded font-medium transition-all duration-300 cursor-pointer flex items-center gap-2 ${
              isFiltersApplied
                ? "bg-green-600 text-white hover:bg-green-500"
                : "bg-blue-600 text-white hover:bg-blue-500"
            }`}
          >
            {isFiltersApplied ? (
              <>
                <svg
                  className="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                Filters Applied!
              </>
            ) : (
              "Apply Filters"
            )}
          </button>
          <button
            onClick={resetFilters}
            className="px-3 py-1 text-sm bg-gray-600 text-white hover:bg-gray-500 rounded font-medium transition-colors cursor-pointer"
          >
            Reset All
          </button>
        </div>
      </div>

      {/* Filter Status Message */}
      {isFiltersApplied && (
        <div className="mb-4 p-3 bg-green-900/30 border border-green-700/50 rounded-lg">
          <div className="flex items-center gap-2 text-sm text-green-300">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            <span className="font-medium">Filters applied successfully!</span>
            <span className="text-green-400">
              {filteredSubmissions.length} of {submissions.length} problems
              match your criteria.
            </span>
          </div>
        </div>
      )}

      {/* Improved responsive grid layout to prevent overlapping */}
      <div className="space-y-6">
        {/* Rating Range Filter */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">
            Difficulty Rating
          </label>
          <div className="flex gap-2">
            <input
              type="number"
              value={filters.minRating || ""}
              onChange={(e) =>
                setFilters((prev) => ({
                  ...prev,
                  minRating: e.target.value ? parseInt(e.target.value) : null,
                }))
              }
              placeholder={`Min (${ratingRange.min})`}
              className="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
            <input
              type="number"
              value={filters.maxRating || ""}
              onChange={(e) =>
                setFilters((prev) => ({
                  ...prev,
                  maxRating: e.target.value ? parseInt(e.target.value) : null,
                }))
              }
              placeholder={`Max (${ratingRange.max})`}
              className="flex-1 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Date Filter */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-300">
            Date Filter
          </label>
          <select
            value={filters.dateFilter}
            onChange={(e) =>
              setFilters((prev) => ({
                ...prev,
                dateFilter: e.target.value as FilterState["dateFilter"],
              }))
            }
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="last7days">Last 7 Days</option>
            <option value="last30days">Last 30 Days</option>
          </select>
        </div>

        {/* Third Row: Tags Filter (Full Width) */}
        <div className="space-y-2">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              Problem Tags
            </label>
            {/* Always render this container to prevent layout shifts */}
            <div className="space-y-2 text-xs min-h-[24px]">
              {filters.selectedTags.length > 0 ? (
                <>
                  <div className="text-blue-400 font-medium">
                    AND Logic: Problems must have ALL{" "}
                    {filters.selectedTags.length} selected tag
                    {filters.selectedTags.length !== 1 ? "s" : ""}
                  </div>
                  <div className="flex flex-wrap items-center gap-1 max-w-full overflow-hidden">
                    {filters.selectedTags.map((tag, index) => (
                      <React.Fragment key={tag}>
                        <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs font-medium whitespace-nowrap">
                          {tag}
                        </span>
                        {index < filters.selectedTags.length - 1 && (
                          <span className="text-blue-400 font-bold whitespace-nowrap">
                            AND
                          </span>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                </>
              ) : (
                <span className="text-gray-500 text-xs">
                  Select tags to filter problems (AND logic will be applied)
                </span>
              )}
            </div>
          </div>
          <div className="bg-gray-800 border border-gray-600 rounded-lg p-4">
            {availableTags.length === 0 ? (
              <p className="text-gray-400 text-sm">No tags available</p>
            ) : (
              <div className="max-h-60 scrollable-content">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3">
                  {availableTags.map((tag) => (
                    <label
                      key={tag}
                      className="flex items-center gap-2 cursor-pointer hover:bg-gray-700 p-2 rounded transition-colors"
                    >
                      <input
                        type="checkbox"
                        checked={filters.selectedTags.includes(tag)}
                        onChange={() => toggleTag(tag)}
                        className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2"
                      />
                      <span
                        className="text-sm text-gray-300 truncate"
                        title={tag}
                      >
                        {tag}
                      </span>
                    </label>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <p className="text-xs text-gray-400 text-center">
                    Showing all {availableTags.length} available tags
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Collapsible Filter Results Summary */}
        <div className="bg-gray-800 border border-gray-600 rounded-lg">
          <button
            onClick={() => setIsFilterResultsExpanded(!isFilterResultsExpanded)}
            className="w-full p-4 flex items-center justify-between hover:bg-gray-750 transition-colors cursor-pointer"
          >
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-sm font-medium text-gray-300">
                Filter Results
              </span>
              <div className="text-sm text-gray-400">
                ({filteredSubmissions.length} of {submissions.length} problems
                match)
              </div>
            </div>
            <div className="flex items-center gap-2">
              {filters.selectedTags.length > 0 && (
                <span className="text-xs text-blue-400 bg-blue-900/30 px-2 py-1 rounded">
                  {filters.selectedTags.length} tag
                  {filters.selectedTags.length !== 1 ? "s" : ""} selected
                </span>
              )}
              <svg
                className={`w-4 h-4 text-gray-400 transition-transform ${
                  isFilterResultsExpanded ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </button>

          {isFilterResultsExpanded && (
            <div className="px-4 pb-4 border-t border-gray-600">
              {filters.selectedTags.length > 0 && (
                <div className="mt-3 p-3 bg-blue-950/30 border border-blue-700/50 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-xs font-medium text-blue-300">
                      🔍 Tag Filter Active (AND Logic)
                    </span>
                    {filteredSubmissions.length === 0 && (
                      <span className="text-xs font-medium text-red-300 bg-red-900/30 px-2 py-1 rounded">
                        ⚠️ No matches found
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-blue-200">
                    Showing only problems that contain <strong>ALL</strong> of
                    these tags:
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2 max-w-full overflow-hidden">
                    {filters.selectedTags.map((tag, index) => (
                      <React.Fragment key={tag}>
                        <span className="px-2 py-1 bg-blue-600 text-white rounded text-xs font-medium whitespace-nowrap">
                          {tag}
                        </span>
                        {index < filters.selectedTags.length - 1 && (
                          <span className="text-blue-400 font-bold text-xs px-1 whitespace-nowrap">
                            AND
                          </span>
                        )}
                      </React.Fragment>
                    ))}
                  </div>
                  {filteredSubmissions.length === 0 && (
                    <div className="mt-2 p-2 bg-yellow-900/30 border border-yellow-700/50 rounded text-xs text-yellow-200">
                      💡{" "}
                      <strong>No problems found with ALL selected tags.</strong>{" "}
                      Try:
                      <ul className="mt-1 ml-4 list-disc">
                        <li>
                          Removing some tags to see if problems exist with fewer
                          requirements
                        </li>
                        <li>
                          Check the browser console for detailed tag matching
                          information
                        </li>
                        <li>
                          Verify tag names are spelled correctly
                          (case-insensitive matching is enabled)
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {filters.selectedTags.length === 0 && (
                <div className="mt-3 p-3 bg-gray-700/30 border border-gray-600 rounded-lg">
                  <div className="text-xs text-gray-300">
                    💡 Select problem tags above to see detailed filtering
                    information and tag matching logic.
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
