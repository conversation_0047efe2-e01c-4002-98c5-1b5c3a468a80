import type { CodeforcesSubmission } from "@/lib/codeforces";
import {
  fetchCodeforcesSubmissions,
  filterSolvedSubmissions,
  filterSubmissionsByDate,
} from "@/lib/codeforces-client";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";

// ============================================================================
// CLIENT-SIDE CODEFORCES SUBMISSIONS REACT QUERY HOOKS
// ============================================================================
// These hooks fetch Codeforces submissions directly from the Codeforces API
// on the client side, avoiding backend API calls and distributing requests
// across different user IPs to prevent rate limiting.

// Parameters for the submissions hook
interface UseCodeforcesSubmissionsClientParams {
  handle: string; // Codeforces username
  count?: number; // Number of submissions to fetch (optional, default: all)
  dateFilter?: string; // Optional single date filter in YYYY-MM-DD format
  startDate?: string; // Optional start date for range filtering
  endDate?: string; // Optional end date for range filtering
  enabled?: boolean; // Whether to enable the query (default: true)
}

// Response structure matching the backend API format for compatibility
interface SubmissionResponse {
  status: string;
  result: {
    submissions: CodeforcesSubmission[]; // Array of solved submissions
    totalSolved: number; // Total number of solved problems (all time)
    totalSolvedOnDate: number | null; // Number solved on specific date (if date filter applied)
    totalSolvedInRange: number | null; // Number solved in date range (if range filter applied)
    handle: string; // Username
    dateFilter: string | null; // Applied date filter (YYYY-MM-DD format)
    startDate: string | null; // Start date for range filtering
    endDate: string | null; // End date for range filtering
    dateRange: {
      // Unix timestamp range for the filtered date/range
      startOfDay: number;
      endOfDay: number;
    } | null;
  };
}

/**
 * React Query hook for fetching Codeforces submissions with date filtering
 * Makes direct API calls to Codeforces from the client side
 * Uses the shared base hook to prevent duplicate API calls
 * @param handle - Codeforces username
 * @param count - Number of submissions to return (optional)
 * @param dateFilter - Single date filter in YYYY-MM-DD format (optional)
 * @param startDate - Start date for range filtering (optional)
 * @param endDate - End date for range filtering (optional)
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with submissions data
 */
export const useCodeforcesSubmissionsClient = ({
  handle,
  count,
  dateFilter,
  startDate,
  endDate,
  enabled = true,
}: UseCodeforcesSubmissionsClientParams) => {
  // Use the shared base hook to prevent duplicate API calls
  const baseQuery = useCodeforcesSubmissionsBase({ handle, enabled });

  // Transform the data to apply filtering and match the expected format
  return {
    ...baseQuery,
    data: baseQuery.data
      ? (() => {
          try {
            // Filter submissions to only include solved problems (verdict "OK")
            // and remove duplicates based on problem ID
            const solvedSubmissions = filterSolvedSubmissions(baseQuery.data);

            // Apply date filtering if specified
            let filteredSubmissions = solvedSubmissions;
            let dateRange: { startOfDay: number; endOfDay: number } | null =
              null;

            if (dateFilter) {
              // Single date filtering
              filteredSubmissions = filterSubmissionsByDate(
                solvedSubmissions,
                dateFilter,
                dateFilter
              );

              // Calculate date range for single date
              const filterDate = new Date(dateFilter);
              const startOfDay = Math.floor(filterDate.getTime() / 1000);
              const endOfDay = startOfDay + 24 * 60 * 60 - 1;
              dateRange = { startOfDay, endOfDay };
            } else if (startDate && endDate) {
              // Date range filtering
              filteredSubmissions = filterSubmissionsByDate(
                solvedSubmissions,
                startDate,
                endDate
              );

              // Calculate date range
              const startDateObj = new Date(startDate);
              const endDateObj = new Date(endDate);
              const startOfDay = Math.floor(startDateObj.getTime() / 1000);
              const endOfDay =
                Math.floor(endDateObj.getTime() / 1000) + 24 * 60 * 60 - 1;
              dateRange = { startOfDay, endOfDay };
            }

            // Apply count limit if specified
            const limitedSubmissions = count
              ? filteredSubmissions.slice(0, count)
              : filteredSubmissions;

            // Calculate statistics
            const totalSolved = solvedSubmissions.length;
            const totalSolvedOnDate = dateFilter
              ? filteredSubmissions.length
              : null;
            const totalSolvedInRange =
              startDate && endDate ? filteredSubmissions.length : null;

            // Return response in the same format as backend API for compatibility
            return {
              status: "OK" as const,
              result: {
                submissions: limitedSubmissions,
                totalSolved,
                totalSolvedOnDate,
                totalSolvedInRange,
                handle: handle.trim(),
                dateFilter: dateFilter || null,
                startDate: startDate || null,
                endDate: endDate || null,
                dateRange,
              },
            };
          } catch (error: any) {
            console.error("Error processing Codeforces submissions:", error);
            throw error;
          }
        })()
      : undefined,
  };
};

/**
 * Base hook for fetching ALL submissions from Codeforces API
 * This is the shared hook that prevents duplicate API calls across components
 * @param handle - Codeforces username
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with raw submissions data
 */
const useCodeforcesSubmissionsBase = ({
  handle,
  enabled = true,
}: {
  handle: string;
  enabled?: boolean;
}) => {
  return useQuery({
    // Shared query key - this ensures all hooks using this base share the same cache
    queryKey: ["codeforces-submissions-base", handle],
    queryFn: async (): Promise<CodeforcesSubmission[]> => {
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      try {
        // Fetch all submissions from Codeforces API
        const response = await fetchCodeforcesSubmissions(handle);

        if (response.status !== "OK" || !response.result) {
          throw new Error(response.comment || "Failed to fetch submissions");
        }

        // Return raw submissions array
        return response.result;
      } catch (error) {
        console.error("Error fetching submissions:", error);
        throw error;
      }
    },
    enabled: !!handle.trim() && enabled,

    // Optimized cache configuration for shared usage
    staleTime: 5 * 60 * 1000, // 5 minutes - submissions don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache longer for sharing
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    refetchOnMount: false, // Don't refetch if data is fresh

    // Retry configuration with exponential backoff
    retry: (failureCount, error: any) => {
      // Don't retry if user is not found (404)
      if (
        error?.message?.includes("not found") ||
        error?.message?.includes("404")
      ) {
        return false;
      }
      return failureCount < 2; // Reduced retries for performance
    },

    // Exponential backoff for retries (1s, 2s, 4s, max 30s)
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching ALL solved questions for a user (no count limit)
 * This is equivalent to the useAllSolvedQuestions hook but using client-side API calls
 * Uses the shared base hook to prevent duplicate API calls
 * @param handle - Codeforces username
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with all solved submissions
 */
export const useAllSolvedQuestionsClient = ({
  handle,
  enabled = true,
}: {
  handle: string;
  enabled?: boolean;
}) => {
  // Use the shared base hook
  const baseQuery = useCodeforcesSubmissionsBase({ handle, enabled });

  // Memoize the transformed data to prevent infinite loops
  // Only create a new object when baseQuery.data actually changes
  const transformedData = useMemo(() => {
    if (!baseQuery.data) {
      return undefined;
    }

    return {
      status: "OK" as const,
      result: {
        submissions: filterSolvedSubmissions(baseQuery.data),
        totalSolved: baseQuery.data.filter((s) => s.verdict === "OK").length,
        totalSolvedOnDate: null,
        totalSolvedInRange: null,
        handle: handle,
        dateFilter: null,
        startDate: null,
        endDate: null,
        dateRange: null,
      },
    };
  }, [baseQuery.data, handle]);

  // Return the query result with memoized data
  return {
    ...baseQuery,
    data: transformedData,
  };
};

/**
 * Hook for fetching ALL submissions for a user (including failed ones, no count limit)
 * This fetches all submissions without filtering by verdict
 * Uses the shared base hook to prevent duplicate API calls
 * @param handle - Codeforces username
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with all submissions (solved and unsolved)
 */
export const useAllSubmissionsClient = ({
  handle,
  enabled = true,
}: {
  handle: string;
  enabled?: boolean;
}) => {
  // Use the shared base hook
  const baseQuery = useCodeforcesSubmissionsBase({ handle, enabled });

  // Memoize the transformed data to prevent infinite loops
  // Only create a new object when baseQuery.data actually changes
  const transformedData = useMemo(() => {
    if (!baseQuery.data) {
      return undefined;
    }

    return {
      status: "OK" as const,
      result: {
        submissions: baseQuery.data,
        totalSolved: baseQuery.data.filter((s) => s.verdict === "OK").length,
        totalSolvedOnDate: null,
        totalSolvedInRange: null,
        handle: handle,
        dateFilter: null,
        startDate: null,
        endDate: null,
        dateRange: null,
      },
    };
  }, [baseQuery.data, handle]);

  // Return the query result with memoized data
  return {
    ...baseQuery,
    data: transformedData,
  };
};
