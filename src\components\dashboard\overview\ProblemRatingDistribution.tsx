"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { useAllSolvedQuestionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import { BarChart3 } from "lucide-react";
import { useMemo } from "react";

// Types for rating distribution data
interface RatingBucket {
  range: string;
  count: number;
  color: string;
  label: string;
}

interface RatingStats {
  totalSolved: number;
  yourRating: number;
  aboveRating: number;
  highestSolved: number;
}

interface ProblemRatingDistributionProps {
  handle: string;
  userRating?: number;
}

const ProblemRatingDistribution = ({
  handle,
  userRating = 0,
}: ProblemRatingDistributionProps) => {
  // Fetch all solved problems
  const {
    data: solvedData,
    isLoading,
    error,
  } = useAllSolvedQuestionsClient({
    handle,
    enabled: !!handle.trim(),
  });

  // Process solved problems into rating distribution
  const { ratingBuckets, stats } = useMemo(() => {
    if (!solvedData?.result?.submissions) {
      return {
        ratingBuckets: [],
        stats: {
          totalSolved: 0,
          yourRating: userRating,
          aboveRating: 0,
          highestSolved: 0,
        },
      };
    }

    // Filter unique solved problems
    const uniqueProblems = new Map();
    solvedData.result.submissions.forEach((submission) => {
      const problemId = `${submission.problem.contestId || "unknown"}-${
        submission.problem.index
      }`;
      if (submission.verdict === "OK" && submission.problem.rating) {
        if (
          !uniqueProblems.has(problemId) ||
          uniqueProblems.get(problemId).rating < submission.problem.rating
        ) {
          uniqueProblems.set(problemId, submission.problem);
        }
      }
    });

    const problems = Array.from(uniqueProblems.values());

    // Define rating buckets
    const buckets = [
      {
        range: "800-999",
        min: 800,
        max: 999,
        color: "#22c55e",
        label: "Very Easy",
      },
      {
        range: "1000-1199",
        min: 1000,
        max: 1199,
        color: "#22c55e",
        label: "Easy",
      },
      {
        range: "1200-1399",
        min: 1200,
        max: 1399,
        color: "#eab308",
        label: "Easy",
      },
      {
        range: "1400-1599",
        min: 1400,
        max: 1599,
        color: "#eab308",
        label: "Moderate",
      },
      {
        range: "1600-1799",
        min: 1600,
        max: 1799,
        color: "#f97316",
        label: "Moderate",
      },
      {
        range: "1800-1999",
        min: 1800,
        max: 1999,
        color: "#ef4444",
        label: "Hard",
      },
      {
        range: "2000-2199",
        min: 2000,
        max: 2199,
        color: "#ef4444",
        label: "Hard",
      },
      {
        range: "2200-2399",
        min: 2200,
        max: 2399,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "2400-2599",
        min: 2400,
        max: 2599,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "2600-2799",
        min: 2600,
        max: 2799,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "2800-2999",
        min: 2800,
        max: 2999,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "3000-3199",
        min: 3000,
        max: 3199,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "3200-3399",
        min: 3200,
        max: 3399,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "3400-3599",
        min: 3400,
        max: 3599,
        color: "#8b5cf6",
        label: "Very Hard",
      },
      {
        range: "3600+",
        min: 3600,
        max: Infinity,
        color: "#8b5cf6",
        label: "Very Hard",
      },
    ];

    // Count problems in each bucket
    const ratingBuckets: RatingBucket[] = buckets.map((bucket) => ({
      range: bucket.range,
      count: problems.filter(
        (p) => p.rating >= bucket.min && p.rating <= bucket.max
      ).length,
      color: bucket.color,
      label: bucket.label,
    }));

    // Calculate statistics
    const totalSolved = problems.length;
    const aboveRating = problems.filter((p) => p.rating > userRating).length;
    const highestSolved =
      problems.length > 0 ? Math.max(...problems.map((p) => p.rating)) : 0;

    const stats: RatingStats = {
      totalSolved,
      yourRating: userRating,
      aboveRating,
      highestSolved,
    };

    return { ratingBuckets, stats };
  }, [solvedData, userRating]);

  // Find max count for scaling
  const maxCount = Math.max(...ratingBuckets.map((bucket) => bucket.count), 1);

  if (isLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-blue-400" />
            Problem Rating Distribution{" "}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || stats.totalSolved === 0) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-blue-400" />
            Problem Rating Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-400">No solved problems data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-blue-400" />
          Problem Rating Distribution
          <span className="text-sm text-gray-400 font-normal ml-2">
            unrated problems excluded && private contest problems don't count
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-blue-400">
              {stats.totalSolved}
            </div>
            <div className="text-xs text-gray-300">Total Solved</div>
          </div>
          <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-purple-400">
              {stats.yourRating}
            </div>
            <div className="text-xs text-gray-300">Your Rating</div>
          </div>
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-green-400">
              {stats.aboveRating}
            </div>
            <div className="text-xs text-gray-300">Above Rating</div>
          </div>
          <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-3 text-center">
            <div className="text-2xl font-bold text-orange-400">
              {stats.highestSolved}
            </div>
            <div className="text-xs text-gray-300">Highest Solved</div>
          </div>
        </div>

        {/* Bar Chart */}
        <div className="space-y-2">
          {ratingBuckets.map((bucket, index) => (
            <div key={bucket.range} className="flex items-center gap-3">
              {/* Rating Range Label */}
              <div className="w-20 text-xs text-gray-300 font-mono">
                {bucket.range}
              </div>

              {/* Bar Container */}
              <div className="flex-1 relative">
                <div className="h-6 bg-slate-800/50 rounded-lg overflow-hidden">
                  <div
                    className="h-full rounded-lg transition-all duration-500 ease-out"
                    style={{
                      width: `${(bucket.count / maxCount) * 100}%`,
                      backgroundColor: bucket.color,
                      opacity: bucket.count > 0 ? 0.8 : 0.3,
                    }}
                  />
                </div>

                {/* Count Label */}
                {bucket.count > 0 && (
                  <div className="absolute right-2 top-0 h-6 flex items-center">
                    <span className="text-xs font-medium text-white">
                      {bucket.count}
                    </span>
                  </div>
                )}
              </div>

              {/* Difficulty Label */}
              <div className="w-16 text-xs text-gray-400">{bucket.label}</div>
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="mt-6 flex flex-wrap gap-4 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-green-500"></div>
            <span className="text-gray-300">Very Easy (&lt; 1100)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-yellow-500"></div>
            <span className="text-gray-300">Easy (1100-1399)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-orange-500"></div>
            <span className="text-gray-300">Moderate (1400-1699)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-red-500"></div>
            <span className="text-gray-300">Hard (1700-1999)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded bg-purple-500"></div>
            <span className="text-gray-300">Very Hard (≥2000)</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProblemRatingDistribution;
