"use client";

import { Git<PERSON>ork, RotateCw } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import ReactFlow, {
  Controls,
  Edge,
  MiniMap,
  Node,
  Position,
  useEdgesState,
  useNodesState,
} from "reactflow";
import "reactflow/dist/style.css";

// Custom styles for nodes
const blueNodeStyles = {
  background: "#3b82f6",
  color: "white",
  border: "2px solid #60a5fa",
  borderRadius: "50%",
  width: "60px",
  height: "60px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "14px",
  fontWeight: "600",
  boxShadow:
    "0 4px 15px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)",
  padding: "8px",
};

const redRootNodeStyles = {
  background: "#ef4444",
  color: "white",
  border: "2px solid #f87171",
  borderRadius: "50%",
  width: "70px",
  height: "70px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "16px",
  fontWeight: "700",
  boxShadow:
    "0 4px 20px rgba(239, 68, 68, 0.5), 0 0 25px rgba(239, 68, 68, 0.3)",
  padding: "8px",
};

export default function TreeVisualizer() {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [inputValue, setInputValue] = useState(
    "7\n0 1\n0 2\n1 3\n1 4\n2 5\n2 6"
  );
  const [isBuilding, setIsBuilding] = useState(false);
  const [rootNodeId, setRootNodeId] = useState("0");
  const [traversalType, setTraversalType] = useState("inorder");
  const [isTraversing, setIsTraversing] = useState(false);
  const [highlightedNode, setHighlightedNode] = useState<string | null>(null);
  const [traversedNodes, setTraversedNodes] = useState<string[]>([]);

  const buildTree = useCallback(
    (input: string, forceReset = false) => {
      try {
        setIsBuilding(true);
        const lines = input
          .trim()
          .split("\n")
          .filter((line) => line.trim() !== "");

        if (lines.length === 0) {
          setNodes([]);
          setEdges([]);
          return;
        }

        const n = parseInt(lines[0]);
        if (isNaN(n) || n <= 0) {
          alert(
            "First line must be a positive number indicating the number of nodes"
          );
          return;
        }

        // Build adjacency list from parent-child relationships
        const adj = new Map<number, number[]>();
        const edgeList: { parent: number; child: number }[] = [];

        // Parse edges (skip first line which is n)
        for (let i = 1; i < lines.length; i++) {
          const parts = lines[i].trim().split(/\s+/);
          if (parts.length === 2) {
            const parent = parseInt(parts[0]);
            const child = parseInt(parts[1]);

            if (!isNaN(parent) && !isNaN(child)) {
              if (!adj.has(parent)) adj.set(parent, []);
              if (!adj.has(child)) adj.set(child, []);

              adj.get(parent)!.push(child);
              adj.get(child)!.push(parent);
              edgeList.push({ parent, child });
            }
          }
        }

        if (edgeList.length === 0) {
          setNodes([]);
          setEdges([]);
          return;
        }

        // Find root from the input field
        const rootId = parseInt(rootNodeId, 10);
        if (isNaN(rootId) || !adj.has(rootId)) {
          // Don't alert, just stop the build process if root is invalid.
          setIsBuilding(false);
          return;
        }

        // BFS to create tree layout - only traverse from the specified root
        const idealPositions = new Map<number, { x: number; y: number }>();
        const levels = new Map<number, number>();
        const visited = new Set<number>();
        const queue: { id: number; level: number }[] = [
          { id: rootId, level: 0 },
        ];

        visited.add(rootId);
        levels.set(rootId, 0);

        // Calculate levels - only include nodes reachable from root
        while (queue.length > 0) {
          const { id: currentId, level } = queue.shift()!;
          const neighbors = adj.get(currentId) || [];

          for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
              visited.add(neighbor);
              levels.set(neighbor, level + 1);
              queue.push({ id: neighbor, level: level + 1 });
            }
          }
        }

        // Layout algorithm - position nodes by level and spread them horizontally
        const nodesByLevel = new Map<number, number[]>();
        for (const nodeId of visited) {
          const level = levels.get(nodeId) || 0;
          if (!nodesByLevel.has(level)) nodesByLevel.set(level, []);
          nodesByLevel.get(level)!.push(nodeId);
        }

        const LEVEL_HEIGHT = 130;
        const NODE_SPACING = 120;

        for (const [level, nodesAtLevel] of nodesByLevel.entries()) {
          const totalWidth = (nodesAtLevel.length - 1) * NODE_SPACING;
          const startX = -totalWidth / 2;

          nodesAtLevel.forEach((nodeId, index) => {
            idealPositions.set(nodeId, {
              x: startX + index * NODE_SPACING,
              y: level * LEVEL_HEIGHT,
            });
          });
        }

        // Create React Flow edges, ensuring correct direction from root
        const newEdges: Edge[] = edgeList
          .filter((edge) => visited.has(edge.parent) && visited.has(edge.child))
          .map((edge, i) => {
            const parentLevel = levels.get(edge.parent);
            const childLevel = levels.get(edge.child);

            let source = edge.parent.toString();
            let target = edge.child.toString();

            // Ensure the edge points from the shallower node to the deeper one
            if (
              parentLevel !== undefined &&
              childLevel !== undefined &&
              parentLevel > childLevel
            ) {
              source = edge.child.toString();
              target = edge.parent.toString();
            }

            return {
              id: `e-${edge.parent}-${edge.child}-${i}`,
              source,
              target,
              style: {
                stroke: "#d1d5db",
                strokeWidth: 2,
                strokeDasharray: "none",
              },
            };
          });

        const sourceNodeIds = new Set(newEdges.map((e) => e.source));
        const targetNodeIds = new Set(newEdges.map((e) => e.target));

        const existingNodePositions = new Map<
          string,
          { x: number; y: number }
        >();
        if (!forceReset) {
          nodes.forEach((n) => existingNodePositions.set(n.id, n.position));
        }

        // Create React Flow nodes with a clean layout

        const newNodes: Node[] = [];
        const nodePositions = new Map<string, { x: number; y: number }>();
        const parentMap = new Map<string, string>();
        newEdges.forEach((edge) => {
          parentMap.set(edge.target, edge.source);
        });

        for (const nodeId of visited) {
          const nodeIdStr = nodeId.toString();
          const idealPosition = idealPositions.get(nodeId) || { x: 0, y: 0 };
          const isSource = sourceNodeIds.has(nodeIdStr);
          const isTarget = targetNodeIds.has(nodeIdStr);
          const existingPosition = existingNodePositions.get(nodeIdStr);

          let position;

          if (!forceReset && existingPosition) {
            position = existingPosition;
          } else {
            const parentId = parentMap.get(nodeIdStr);
            const parentPosition = parentId
              ? nodePositions.get(parentId)
              : undefined;

            if (!forceReset && parentPosition) {
              const siblings = newEdges.filter((e) => e.source === parentId);
              const siblingIndex = siblings.findIndex(
                (s) => s.target === nodeIdStr
              );
              const numSiblings = siblings.length;
              const siblingSpacing = 100;
              const LEVEL_HEIGHT = 130;

              position = {
                x:
                  parentPosition.x +
                  (siblingIndex - (numSiblings - 1) / 2) * siblingSpacing,
                y: parentPosition.y + LEVEL_HEIGHT,
              };
            } else {
              position = idealPosition;
            }
          }

          nodePositions.set(nodeIdStr, position);

          newNodes.push({
            id: nodeIdStr,
            data: { label: nodeId.toString() },
            position,
            style: nodeId === rootId ? redRootNodeStyles : blueNodeStyles,
            sourcePosition: isSource ? Position.Bottom : undefined,
            targetPosition: isTarget ? Position.Top : undefined,
          });
        }

        setNodes(newNodes);
        setEdges(newEdges);
      } catch (error) {
        console.error("Failed to build tree:", error);
        alert(
          "An error occurred while building the tree. Please check your input format."
        );
      } finally {
        setTimeout(() => setIsBuilding(false), 300);
      }
    },
    [nodes, setNodes, setEdges, rootNodeId]
  );

  useEffect(() => {
    buildTree(inputValue, true); // Initial build should always be a clean layout
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // This effect should only run once on mount for the initial build.

  const handleBuildTree = () => {
    setTraversedNodes([]);
    buildTree(inputValue, false); // Subsequent builds preserve positions
  };

  const handleResetPositions = () => {
    setTraversedNodes([]);
    buildTree(inputValue, true); // Force a reset to ideal positions
  };

  const handleStartTraversal = async () => {
    setIsTraversing(true);
    setHighlightedNode(null);
    setTraversedNodes([]);

    const childrenMap = new Map<string, string[]>();
    nodes.forEach((node) => {
      childrenMap.set(node.id, []);
    });
    edges.forEach((edge) => {
      childrenMap.get(edge.source)?.push(edge.target);
    });

    const inorder = async (nodeId: string) => {
      setHighlightedNode(nodeId);
      await new Promise((r) => setTimeout(r, 700));

      const children = childrenMap.get(nodeId) || [];
      if (children.length > 0) {
        await inorder(children[0]);
      }

      setHighlightedNode(nodeId);
      setTraversedNodes((prev) => [...prev, nodeId]);
      await new Promise((r) => setTimeout(r, 400));

      if (children.length > 1) {
        for (let i = 1; i < children.length; i++) {
          await inorder(children[i]);
        }
      }
      setHighlightedNode(nodeId);
      await new Promise((r) => setTimeout(r, 300));
    };

    const preorder = async (nodeId: string) => {
      setHighlightedNode(nodeId);
      setTraversedNodes((prev) => [...prev, nodeId]);
      await new Promise((r) => setTimeout(r, 700));

      const children = childrenMap.get(nodeId) || [];
      for (const child of children) {
        await preorder(child);
      }
      setHighlightedNode(nodeId);
      await new Promise((r) => setTimeout(r, 300));
    };

    const postorder = async (nodeId: string) => {
      setHighlightedNode(nodeId);
      await new Promise((r) => setTimeout(r, 700));

      const children = childrenMap.get(nodeId) || [];
      for (const child of children) {
        await postorder(child);
      }

      setHighlightedNode(nodeId);
      setTraversedNodes((prev) => [...prev, nodeId]);
      await new Promise((r) => setTimeout(r, 400));
    };

    if (nodes.length > 0) {
      if (traversalType === "inorder") {
        await inorder(rootNodeId);
      } else if (traversalType === "preorder") {
        await preorder(rootNodeId);
      } else if (traversalType === "postorder") {
        await postorder(rootNodeId);
      }
    }

    setTimeout(() => {
      setHighlightedNode(null);
      setIsTraversing(false);
    }, 700);
  };

  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        const isHighlighted = node.id === highlightedNode;
        const isRoot = node.id === rootNodeId;

        // Base style from constants
        const baseStyle = isRoot ? redRootNodeStyles : blueNodeStyles;

        let style = { ...baseStyle };

        // Apply highlight style
        if (isHighlighted) {
          style = {
            ...style,
            boxShadow: `0 0 30px #fef08a, 0 0 15px #facc15`,
            border: "3px solid #facc15",
          };
        }

        return { ...node, style };
      })
    );
  }, [highlightedNode, rootNodeId, setNodes]);

  return (
    <div className="min-h-screen bg-black text-white flex flex-col pt-24">
      {/* Animated Heading */}
      <div className="text-center py-8 animate-fadeIn">
        <h1 className="text-4xl md:text-5xl font-light text-white drop-shadow-[0_0_15px_rgba(29,78,216,0.6)]">
          🌳 Tree Visualizer
        </h1>
      </div>

      {/* Main Content */}
      <div className="flex flex-col flex-1 gap-4 p-4">
        <div className="flex flex-col lg:flex-row flex-1 gap-4">
          {/* Left Panel - Tree Visualization */}
          <div className="relative h-[60vh] lg:h-auto lg:flex-1 bg-black rounded-lg overflow-hidden shadow-2xl border border-gray-800">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              fitView
              fitViewOptions={{ padding: 20 }}
              nodesConnectable={false}
              nodesDraggable={true}
              className="bg-black"
              proOptions={{ hideAttribution: true }}
            >
              <MiniMap
                className="hidden lg:block"
                style={{
                  backgroundColor: "#1f2937",
                  border: "2px solid #374151",
                  borderRadius: "8px",
                }}
                nodeColor={(node) =>
                  node.id === rootNodeId ? "#ef4444" : "#3b82f6"
                }
                maskColor="rgba(0, 0, 0, 0.2)"
              />
              <Controls
                style={{
                  backgroundColor: "#1f2937",
                  border: "2px solid #374151",
                  borderRadius: "8px",
                  color: "white",
                }}
              />
            </ReactFlow>

            {/* Stats Panel */}
            {nodes.length > 0 && (
              <div className="absolute top-4 left-4 bg-gray-900 bg-opacity-90 backdrop-blur-sm border border-gray-600 rounded-lg p-3">
                <div className="text-xs text-gray-300">
                  <div className="font-semibold mb-1">Tree Stats</div>
                  <div>Nodes: {nodes.length}</div>
                  <div>Edges: {edges.length}</div>
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Input */}
          <div className="w-full lg:w-80 flex-shrink-0 bg-zinc-900 border border-gray-700 rounded-2xl p-6 flex flex-col space-y-6">
            {/* Input Section */}

            {/* we do not support duplicate node values yet */}
            <div className="">
              <label className="block text-sm font-medium text-gray-300">
                Enter Edge List Input
              </label>
              <span className="text-xs text-gray-500">
                We do not support duplicate node values yet
              </span>

              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full h-48 bg-[#111] border border-gray-700 rounded-xl p-3 text-white placeholder-gray-500 font-mono text-sm focus:ring-2 focus:ring-[#1d4ed8] focus:border-transparent transition-all duration-200 resize-none"
                placeholder="7&#10;0 1&#10;0 2&#10;..."
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                Root Node <br />
                <span className="text-xs text-gray-500">
                  Don't change unless you know what you're doing
                </span>
              </label>
              <input
                type="text"
                value={rootNodeId}
                onChange={(e) => setRootNodeId(e.target.value)}
                className="w-full bg-[#111] border border-gray-700 rounded-xl p-3 text-white placeholder-gray-500 font-mono text-sm focus:ring-2 focus:ring-[#1d4ed8] focus:border-transparent transition-all duration-200"
                placeholder="e.g., 0"
              />
            </div>

            {/* Buttons */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleBuildTree}
                disabled={isBuilding || isTraversing}
                className={`flex-grow py-3 px-4 rounded-full font-bold text-white transition-all duration-300 transform shadow-lg disabled:cursor-not-allowed disabled:bg-gray-600 ${
                  isBuilding
                    ? ""
                    : "bg-[#1d4ed8] hover:bg-[#2563eb] hover:scale-105 active:scale-95 hover:shadow-[0_0_15px_rgba(29,78,216,0.6)]"
                }`}
              >
                {isBuilding ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Building...
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-2 drop-shadow-md">
                    <GitFork size={18} />
                    Build Tree
                  </span>
                )}
              </button>
              <button
                onClick={handleResetPositions}
                disabled={isBuilding || isTraversing}
                title="Reset Node Positions"
                className={`p-3 rounded-full text-white transition-all duration-300 transform disabled:cursor-not-allowed disabled:bg-gray-600 ${
                  isBuilding
                    ? ""
                    : "bg-gray-800 hover:bg-gray-700 hover:scale-105 active:scale-95"
                }`}
              >
                <RotateCw size={20} />
              </button>
            </div>

            {/* Instructions */}
            <div className="mt-4 p-4 bg-[#111] rounded-lg border border-gray-800">
              <h3 className="text-xs font-semibold text-gray-300 mb-2">
                Format:
              </h3>
              <ul className="text-xs text-gray-400 space-y-1 font-mono">
                <li>• First line: number of nodes</li>
                <li>• Then: parent child</li>
                <li>• Root: specified above</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Traversal Section */}
        <div className="w-full bg-zinc-900 border border-gray-700 rounded-2xl p-6">
          <div className="flex flex-col md:flex-row gap-8 items-start">
            {/* Left side: Controls */}
            <div className="w-full md:w-1/3 space-y-4">
              <h3 className="text-lg font-bold text-white text-center">
                Animate Traversal
              </h3>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-300">
                  Traversal Type
                </label>
                <select
                  value={traversalType}
                  onChange={(e) => setTraversalType(e.target.value)}
                  disabled={isTraversing}
                  className="w-full bg-[#111] border border-gray-700 rounded-xl p-3 text-white font-mono text-sm focus:ring-2 focus:ring-[#1d4ed8] focus:border-transparent transition-all duration-200"
                >
                  <option value="inorder">Inorder</option>
                  <option value="preorder">Preorder</option>
                  <option value="postorder">Postorder</option>
                </select>
              </div>
              <button
                onClick={handleStartTraversal}
                disabled={isBuilding || isTraversing || nodes.length === 0}
                className="w-full py-3 px-4 rounded-full font-bold text-white transition-all duration-300 transform shadow-lg bg-green-600 hover:bg-green-500 disabled:bg-gray-600 disabled:cursor-not-allowed"
              >
                Start Traversal
              </button>
            </div>

            {/* Right side: Progress Display */}
            <div className="w-full md:w-2/3">
              <h3 className="text-lg font-bold text-white text-center mb-2">
                Traversal Progress
              </h3>
              <div className="h-28 bg-[#111] border border-gray-700 rounded-xl p-3 scrollable-content">
                {traversedNodes.map((nodeId, index) => (
                  <div
                    key={`${nodeId}-${index}`}
                    className={`inline-block mr-2 mb-2 text-center rounded-full text-white px-3 py-1 font-mono text-sm ${
                      nodeId === rootNodeId ? "bg-red-600" : "bg-blue-700"
                    }`}
                  >
                    {nodeId}
                  </div>
                ))}
                {isTraversing && traversedNodes.length === 0 && (
                  <p className="text-gray-400 text-sm text-center">
                    Starting traversal...
                  </p>
                )}
                {!isTraversing && traversedNodes.length === 0 && (
                  <p className="text-gray-400 text-sm text-center">
                    Click "Start Traversal" to begin.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
