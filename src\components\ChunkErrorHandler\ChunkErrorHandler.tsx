"use client";

import { useEffect } from "react";

export default function ChunkErrorHandler() {
  useEffect(() => {
    const handler = (e: ErrorEvent) => {
      if (
        e.message?.includes("Loading chunk") ||
        e.message?.includes("ChunkLoadError")
      ) {
        console.warn("Detected chunk load error, reloading...");
        window.location.reload(); // no need for true, modern browsers always reload fresh
      }
    };

    window.addEventListener("error", handler);
    return () => {
      window.removeEventListener("error", handler);
    };
  }, []);

  return null; // No UI needed
}
