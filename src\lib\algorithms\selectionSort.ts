import { ArrayElement } from "../types";

export function* selectionSort(
  array: ArrayElement[]
): Generator<ArrayElement[], void, void> {
  const arr = [...array];
  const n = arr.length;

  for (let i = 0; i < n - 1; i++) {
    let minIndex = i;

    // Highlight the current element of the main iteration
    arr[i].isCurrent = true;
    // Mark the starting element of the unsorted part as the initial minimum.
    arr[minIndex].isMinimum = true;
    yield [...arr];

    for (let j = i + 1; j < n; j++) {
      // Highlight the element being compared against the current minimum.
      arr[j].isComparing = true;
      yield [...arr];

      if (arr[j].value < arr[minIndex].value) {
        // Found a new minimum. Reset the old one's highlight.
        arr[minIndex].isMinimum = false;
        minIndex = j;
        // Highlight the new minimum.
        arr[minIndex].isMinimum = true;
      }

      // Reset the comparison highlight for the j-th element after comparison.
      arr[j].isComparing = false;
      yield [...arr]; // Yield to show the comparison highlight removed
    }

    // Now, minIndex points to the minimum element in the unsorted part.
    // Swap if necessary.
    if (minIndex !== i) {
      // Highlight elements for swapping.
      arr[i].isSwapping = true;
      arr[minIndex].isSwapping = true;
      yield [...arr];

      // Perform the swap
      [arr[i], arr[minIndex]] = [arr[minIndex], arr[i]];
      yield [...arr];

      // Reset swapping highlight.
      arr[i].isSwapping = false;
      arr[minIndex].isSwapping = false;
    }

    // The element at `i` is now sorted.
    // Reset the highlights on it.
    arr[i].isMinimum = false;
    arr[i].isCurrent = false;
    arr[i].isSorted = true;
    yield [...arr];
  }

  // Mark the last element as sorted
  if (n > 0) {
    arr[n - 1].isSorted = true;
  }

  // Final clean up and yield
  for (const el of arr) {
    el.isMinimum = false;
    el.isComparing = false;
    el.isSwapping = false;
  }
  yield [...arr];
}
