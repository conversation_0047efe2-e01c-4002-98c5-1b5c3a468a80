import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'

export default async function PrivatePage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getUser()
  if (error || !data?.user) {
    redirect('/login')
  }

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="max-w-md w-full space-y-8 p-8 text-center">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            Private Page
          </h2>
          <p className="mt-2 text-center text-sm text-gray-400">
            This is a protected page that only authenticated users can access.
          </p>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
          <h3 className="text-lg font-semibold mb-4">User Information</h3>
          <div className="space-y-2 text-left">
            <p className="text-gray-400">
              <span className="text-white font-medium">Email:</span> {data.user.email}
            </p>
            <p className="text-gray-400">
              <span className="text-white font-medium">User ID:</span> {data.user.id}
            </p>
            <p className="text-gray-400">
              <span className="text-white font-medium">Email Confirmed:</span> {data.user.email_confirmed_at ? 'Yes' : 'No'}
            </p>
          </div>
        </div>

        <div className="text-center">
          <p className="text-green-400 text-sm">
            ✓ Authentication successful! You have access to this protected content.
          </p>
        </div>
      </div>
    </div>
  )
}
