"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  useCodeforcesRatingClient,
  type RatingChangeWithExtras,
} from "@/hooks/useCodeforcesRatingClient";
import { useAllSolvedQuestionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import React from "react";
import {
  // Bar,
  // BarChart,
  CartesianGrid,
  // Cell,
  Line,
  LineChart,
  ReferenceDot,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { ProblemFilters } from "./ProblemFilters";
import { ProblemsDisplay } from "./ProblemsDisplay";

// ============================================================================
// CODEFORCES RATING GRAPH COMPONENT
// ============================================================================
// Interactive rating graph using Recharts library
// Shows complete rating progression with hover tooltips and Codeforces styling

// Props for the rating graph component
interface CodeforcesRatingGraphProps {
  handle: string; // Codeforces username to display rating for
  height?: number; // Height of the graph in pixels (default: 480)
  width?: number; // Width of the graph in pixels (default: responsive 100%)
}

// ============================================================================
// DAILY RATING DATA TRANSFORMATION
// ============================================================================
// Function to convert contest-based rating data to daily rating progression
// This fills in all days between contests with the user's rating on each day

interface DailyRatingData {
  date: string; // Date in YYYY-MM-DD format
  dateTimestamp: number; // Unix timestamp for the date
  rating: number; // User's rating on this date
  contestName?: string; // Contest name if this date had a contest
  isContestDay: boolean; // Whether this date had a rated contest
  ratingDelta?: number; // Rating change if this was a contest day
  rank?: number; // Rank if this was a contest day
}

// Transform contest-based rating history into daily rating progression
const transformToDaily = (
  ratingHistory: RatingChangeWithExtras[]
): DailyRatingData[] => {
  if (ratingHistory.length === 0) return [];

  const dailyData: DailyRatingData[] = [];

  // Sort rating history by date (should already be sorted, but ensure it)
  const sortedHistory = [...ratingHistory].sort(
    (a, b) => a.ratingUpdateTimeSeconds - b.ratingUpdateTimeSeconds
  );

  // Get the date range from first contest to today
  const firstContestDate = new Date(
    sortedHistory[0].ratingUpdateTimeSeconds * 1000
  );
  const today = new Date();

  // Start from the first contest date
  let currentDate = new Date(firstContestDate);

  // FIXED: Handle initial rating properly
  // For users who started unrated (oldRating = 0), start with their first newRating
  // For users who had a previous rating, start with their oldRating
  let currentRating = sortedHistory[0].oldRating || sortedHistory[0].newRating;

  let contestIndex = 0;

  // Generate daily data for each day from first contest to today
  while (currentDate <= today) {
    const dateStr = currentDate.toISOString().split("T")[0]; // YYYY-MM-DD format
    const dateTimestamp = Math.floor(currentDate.getTime() / 1000);

    // Check if this date has a contest
    let isContestDay = false;
    let contestInfo: RatingChangeWithExtras | undefined;

    // FIXED: Handle multiple contests on the same day
    while (contestIndex < sortedHistory.length) {
      const contest = sortedHistory[contestIndex];
      const contestDate = new Date(contest.ratingUpdateTimeSeconds * 1000)
        .toISOString()
        .split("T")[0];

      if (contestDate === dateStr) {
        isContestDay = true;
        contestInfo = contest; // Keep the last contest info for this day
        const oldRating = currentRating;
        currentRating = contest.newRating; // Update rating after contest

        contestIndex++;
      } else {
        // No more contests on this date, break out of the while loop
        break;
      }
    }

    // Add daily data point
    dailyData.push({
      date: dateStr,
      dateTimestamp: dateTimestamp,
      rating: currentRating,
      contestName: contestInfo?.contestName,
      isContestDay: isContestDay,
      ratingDelta: contestInfo?.ratingDelta,
      rank: contestInfo?.rank,
    });

    // Move to next day
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dailyData;
};

// ============================================================================
// CODEFORCES RATING SYSTEM UTILITIES
// ============================================================================
// These functions implement the official Codeforces rating colors and titles

// Get the color associated with a rating (matches official Codeforces colors)
const getRatingColor = (rating: number): string => {
  if (rating >= 3000) return "#8B0000"; // Legendary Grandmaster (Maroon)
  if (rating >= 2600) return "#ff0000"; // International Grandmaster (red)
  if (rating >= 2400) return "#ff0000"; // Grandmaster (red)
  if (rating >= 2300) return "#ff8c00"; // International Master (orange)
  if (rating >= 2100) return "#ff8c00"; // Master (orange)
  if (rating >= 1900) return "#aa00aa"; // Candidate Master (purple)
  if (rating >= 1600) return "#0000ff"; // Expert (blue)
  if (rating >= 1400) return "#00aaaa"; // Specialist (cyan)
  if (rating >= 1200) return "#008000"; // Pupil (green)
  return "#808080"; // Newbie (gray)
};

// Get the title/rank name associated with a rating
const getRatingTitle = (rating: number): string => {
  if (rating >= 3000) return "Legendary Grandmaster";
  if (rating >= 2600) return "International Grandmaster";
  if (rating >= 2400) return "Grandmaster";
  if (rating >= 2300) return "International Master";
  if (rating >= 2100) return "Master";
  if (rating >= 1900) return "Candidate Master";
  if (rating >= 1600) return "Expert";
  if (rating >= 1400) return "Specialist";
  if (rating >= 1200) return "Pupil";
  return "Newbie";
};

// Get the abbreviated title for display (following Codeforces conventions)
const getAbbreviatedRatingTitle = (rating: number): string => {
  if (rating >= 3000) return "LGM"; // Legendary Grandmaster
  if (rating >= 2600) return "IGM"; // International Grandmaster
  if (rating >= 2400) return "GM"; // Grandmaster
  if (rating >= 2300) return "IM"; // International Master
  if (rating >= 2100) return "M"; // Master
  if (rating >= 1900) return "CM"; // Candidate Master
  if (rating >= 1600) return "E"; // Expert
  if (rating >= 1400) return "S"; // Specialist
  if (rating >= 1200) return "P"; // Pupil
  return "N"; // Newbie
};

// Generate Codeforces problem URL from problem data
const getCodeforcesUrl = (problem: any): string => {
  if (problem.contestId) {
    return `https://codeforces.com/contest/${problem.contestId}/problem/${problem.index}`;
  }
  // For problems without contestId (like gym problems), try alternative URL format
  return `https://codeforces.com/problemset/problem/${
    problem.contestId || "unknown"
  }/${problem.index}`;
};

// ============================================================================
// CUSTOM TOOLTIP COMPONENT FOR HOVER INTERACTIONS
// ============================================================================
// This component shows detailed information when hovering over graph points

// Enhanced custom tooltip with improved styling and better range selection features
const CustomTooltip = ({
  active,
  payload,
  label,
  onClose,
  isPinned,
  startPoint,
  endPoint,
  submissionsData, // Add submissions data to show problems solved on this date
}: any) => {
  // Only show tooltip when hovering over a valid data point
  if (active && payload && payload.length) {
    // Extract the daily rating data from the hovered point
    const data = payload[0].payload as DailyRatingData;
    const [year, month, day] = data.date.split("-").map(Number);
    const displayDate = new Date(year, month - 1, day);

    // Check if this point is already selected as start or end
    const isStartPoint = startPoint?.date === data.date;
    const isEndPoint = endPoint?.date === data.date;

    return (
      <div className="bg-gray-900 p-4 border border-gray-700 rounded-lg shadow-lg text-white min-w-[280px]">
        {/* Date as the main title with enhanced styling */}
        <div className="flex items-center justify-between mb-2">
          <p className="font-semibold text-gray-100 text-base">
            {displayDate.toLocaleDateString("en-US", {
              weekday: "short",
              year: "numeric",
              month: "short",
              day: "numeric",
            })}
          </p>
          {/* Show selection status */}
          {(isStartPoint || isEndPoint) && (
            <span className="text-xs px-2 py-1 rounded-full bg-blue-600 text-white">
              {isStartPoint ? "START" : "END"}
            </span>
          )}
        </div>

        {/* Show contest information if this was a contest day */}
        {data.isContestDay && data.contestName && (
          <div className="mb-2 p-2 bg-blue-900/30 rounded border border-blue-700/50">
            <p className="text-sm text-blue-200 font-medium flex items-center gap-1">
              🏆 Contest Day
            </p>
            <p
              className="text-sm text-blue-100 truncate"
              title={data.contestName}
            >
              {data.contestName}
            </p>
            {data.rank && (
              <p className="text-sm text-blue-200">Rank: #{data.rank}</p>
            )}
          </div>
        )}

        {/* Show regular day information */}
        {!data.isContestDay && (
          <p className="text-sm text-gray-400 mb-2 flex items-center gap-1">
            📅 Regular Day
          </p>
        )}

        <div className="space-y-2 mb-3">
          {/* Current rating with enhanced color coding and larger text */}
          <div className="flex items-center justify-between">
            <span className="text-gray-300 text-sm">Rating:</span>
            <div className="text-right">
              <span
                className="font-bold text-lg"
                style={{ color: getRatingColor(data.rating) }}
              >
                {data.rating}
              </span>
              <p className="text-xs text-gray-400">
                {getRatingTitle(data.rating)}
              </p>
            </div>
          </div>

          {/* Rating change if this was a contest day */}
          {data.isContestDay && data.ratingDelta !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-gray-300 text-sm">Change:</span>
              <span
                className="font-semibold text-base"
                style={{ color: data.ratingDelta >= 0 ? "#22c55e" : "#ef4444" }}
              >
                {data.ratingDelta >= 0 ? "+" : ""}
                {data.ratingDelta}
              </span>
            </div>
          )}
        </div>

        {/* Show all problems solved on this date with smart display */}
        {submissionsData &&
          (() => {
            // Filter submissions for this specific date
            const dateStart =
              new Date(data.date + "T00:00:00Z").getTime() / 1000;
            const dateEnd = dateStart + 24 * 60 * 60 - 1;

            const problemsOnDate = submissionsData.result.submissions.filter(
              (submission: any) =>
                submission.creationTimeSeconds >= dateStart &&
                submission.creationTimeSeconds <= dateEnd
            );

            if (problemsOnDate.length > 0) {
              // Determine display strategy based on number of problems
              const shouldUseCompactView = problemsOnDate.length > 5;
              const maxHeight = problemsOnDate.length > 10 ? "120px" : "auto";

              return (
                <div className="pt-3 border-t border-gray-700">
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-xs text-green-400 flex items-center gap-1">
                      🎯 Problems Solved
                      <span className="text-blue-400 text-xs">
                        🔗 Click to open
                      </span>
                    </p>
                    <span className="text-xs text-green-300 bg-green-900/30 px-2 py-0.5 rounded-full border border-green-700/50">
                      {problemsOnDate.length} problem
                      {problemsOnDate.length !== 1 ? "s" : ""}
                    </span>
                  </div>

                  <div
                    className={`space-y-1 ${
                      problemsOnDate.length > 10
                        ? "scrollable-content pr-1"
                        : ""
                    }`}
                    style={{ maxHeight }}
                  >
                    {problemsOnDate.map((submission: any, index: number) => {
                      const problem = submission.problem;
                      const ratingColor = getRatingColor(problem.rating);

                      if (shouldUseCompactView) {
                        // Compact view for many problems - single line per problem (clickable)
                        return (
                          <div
                            key={`${problem.contestId}-${problem.index}-${index}`}
                            className="text-xs bg-gray-800/60 rounded px-2 py-1 border border-gray-600/50 flex items-center justify-between cursor-pointer hover:bg-gray-700/60 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(getCodeforcesUrl(problem), "_blank");
                            }}
                            title={`Click to open ${problem.name} on Codeforces`}
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <span className="text-gray-200 font-medium">
                                {problem.contestId
                                  ? `${problem.contestId}${problem.index}`
                                  : problem.index}
                              </span>
                              <span
                                className="text-gray-300 truncate flex-1"
                                title={problem.name}
                              >
                                {problem.name}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              {problem.rating && (
                                <span
                                  className="px-1 py-0.5 rounded text-xs font-medium flex-shrink-0"
                                  style={{
                                    backgroundColor: `${ratingColor}15`,
                                    color: ratingColor,
                                    border: `1px solid ${ratingColor}30`,
                                  }}
                                >
                                  {problem.rating}
                                </span>
                              )}
                              <span className="text-blue-400 text-xs">🔗</span>
                            </div>
                          </div>
                        );
                      } else {
                        // Detailed view for fewer problems - two lines per problem (clickable)
                        return (
                          <div
                            key={`${problem.contestId}-${problem.index}-${index}`}
                            className="text-xs bg-gray-800 rounded p-2 border border-gray-600 cursor-pointer hover:bg-gray-700 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(getCodeforcesUrl(problem), "_blank");
                            }}
                            title={`Click to open ${problem.name} on Codeforces`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-gray-200 font-medium truncate flex-1">
                                {problem.contestId
                                  ? `${problem.contestId}${problem.index}`
                                  : problem.index}
                              </span>
                              <div className="flex items-center gap-2">
                                {problem.rating && (
                                  <span
                                    className="px-1 py-0.5 rounded text-xs font-medium"
                                    style={{
                                      backgroundColor: `${ratingColor}20`,
                                      color: ratingColor,
                                      border: `1px solid ${ratingColor}40`,
                                    }}
                                  >
                                    {problem.rating}
                                  </span>
                                )}
                                <span className="text-blue-400 text-xs">
                                  🔗
                                </span>
                              </div>
                            </div>
                            <p
                              className="text-gray-300 text-xs truncate mt-1"
                              title={problem.name}
                            >
                              {problem.name}
                            </p>
                          </div>
                        );
                      }
                    })}
                  </div>

                  {/* Show scroll hint for many problems */}
                  {problemsOnDate.length > 10 && (
                    <p className="text-xs text-gray-500 mt-1 text-center">
                      ↕️ Scroll to see all problems
                    </p>
                  )}
                </div>
              );
            }
            return null;
          })()}

        {/* Show instructions for non-pinned tooltips, close button for pinned tooltips */}
        {!isPinned ? (
          <div className="pt-3 border-t border-gray-700 text-center">
            <p className="text-xs text-blue-400 mb-1">
              💡 Hover over the chart to see rating details
            </p>
          </div>
        ) : (
          <>
            {/* Close button for pinned tooltips */}
            <div className="mt-3 pt-2 border-t border-gray-700 text-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
                className="text-xs text-gray-400 hover:text-gray-200 focus:outline-none px-2 py-1 rounded hover:bg-gray-800"
              >
                Close
              </button>
            </div>
          </>
        )}
      </div>
    );
  }
  return null;
};

// ============================================================================
// MAIN RATING GRAPH COMPONENT
// ============================================================================

// Main component that renders the interactive rating graph
export const CodeforcesRatingGraph: React.FC<CodeforcesRatingGraphProps> = ({
  handle,
  height = 480,
  width,
}) => {
  // Debounce the handle to prevent excessive API calls during typing/slider interactions
  // This creates a 500ms delay before triggering new API requests
  const [debouncedHandle, setDebouncedHandle] = React.useState(handle);

  // State for date range inputs (start and end dates)
  const [startMonth, setStartMonth] = React.useState("");
  const [startYear, setStartYear] = React.useState("");
  const [endMonth, setEndMonth] = React.useState("");
  const [endYear, setEndYear] = React.useState("");

  const [pinnedTooltipData, setPinnedTooltipData] = React.useState<{
    payload: any;
    coordinate: { x: number; y: number };
  } | null>(null);

  const latestHoverData = React.useRef<{
    payload: any;
    coordinate: { x: number; y: number };
  } | null>(null);

  // ============================================================================
  // START/END POINT SELECTION STATE MANAGEMENT
  // ============================================================================
  // State to track selected start and end points for range selection

  interface SelectedPoint {
    date: string; // Date in YYYY-MM-DD format
    rating: number; // Rating value at that date
    dateTimestamp: number; // Unix timestamp for comparison
    contestName?: string; // Contest name if applicable
    isContestDay: boolean; // Whether this was a contest day
  }

  const [startPoint, setStartPoint] = React.useState<SelectedPoint | null>(
    null
  );
  const [endPoint, setEndPoint] = React.useState<SelectedPoint | null>(null);
  const [rangeSelectionError, setRangeSelectionError] = React.useState<
    string | null
  >(null);

  // ============================================================================
  // INFO MODAL STATE MANAGEMENT
  // ============================================================================
  // State for the info button and modal functionality

  const [isInfoModalOpen, setIsInfoModalOpen] = React.useState(false);
  const [hasInfoBeenClicked, setHasInfoBeenClicked] = React.useState(false);

  // State for date range validation errors
  const [dateRangeError, setDateRangeError] = React.useState<string | null>(
    null
  );

  // State for brush selection to sync with date inputs
  const [brushSelection, setBrushSelection] = React.useState<{
    startIndex?: number;
    endIndex?: number;
  }>({});

  // ============================================================================
  // SUBMISSIONS STATE MANAGEMENT
  // ============================================================================
  // State to track the date range for graph context (when users zoom into a range)
  const [submissionsDateRange, setSubmissionsDateRange] = React.useState<{
    startDate: string;
    endDate: string;
  } | null>(null);

  // State to track filtered submissions from the ProblemFilters component
  const [filteredSubmissions, setFilteredSubmissions] = React.useState<any[]>(
    []
  );

  // State to store all solved problems for the current user
  // This will be populated once when user is searched and displayed immediately
  const [allSolvedProblems, setAllSolvedProblems] = React.useState<any[]>([]);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedHandle(handle);
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timer);
  }, [handle]);

  // ============================================================================
  // RESET STATE WHEN HANDLE CHANGES
  // ============================================================================
  // Effect to reset all state when searching for a different user
  // This ensures that start/end points and date filters don't persist across users

  // Add a flag to track if we're initializing a new user to prevent auto-zoom
  const [isInitializingNewUser, setIsInitializingNewUser] =
    React.useState(false);

  // Add state to track zoom button loading to prevent DOM manipulation conflicts
  const [isZoomingToRange, setIsZoomingToRange] = React.useState(false);

  // Add ref to track if we should skip the next date input change (for new user initialization)
  const skipNextDateInputChange = React.useRef(false);

  React.useEffect(() => {
    // Set flag to indicate we're initializing a new user
    setIsInitializingNewUser(true);

    // Set flag to skip the next date input change to prevent auto-zoom
    skipNextDateInputChange.current = true;

    // Reset all selection state when handle changes
    setStartPoint(null);
    setEndPoint(null);
    setRangeSelectionError(null);
    setDateRangeError(null);
    setSubmissionsDateRange(null);
    setFilteredSubmissions([]);
    setPinnedTooltipData(null);
    setIsZoomingToRange(false); // Reset zoom button state

    // Reset date input fields to empty so they get re-initialized with new user's data
    setStartMonth("");
    setStartYear("");
    setEndMonth("");
    setEndYear("");

    // CRITICAL: Force brush selection to be cleared immediately and persistently
    // This ensures the chart shows full range for new users
    setBrushSelection({});

    // Clear the initialization flag after a short delay to allow data to load
    const timer = setTimeout(() => {
      setIsInitializingNewUser(false);
      // Keep the skip flag for a bit longer to ensure the debounced effect is also skipped
      setTimeout(() => {
        skipNextDateInputChange.current = false;
      }, 1000); // Additional delay to ensure debounced effects are skipped
    }, 1000); // Give enough time for data to load and initialize

    return () => clearTimeout(timer);
  }, [handle]); // Trigger when the actual handle prop changes (not debounced)

  // Fetch rating data using our custom React Query hook with debounced handle
  const { data, isLoading, error, refetch } = useCodeforcesRatingClient({
    handle: debouncedHandle,
    enabled: !!debouncedHandle.trim(), // Only fetch if debounced handle is provided
  });

  // Note: We no longer use useCodeforcesSubmissionsByRange for API calls
  // Instead, we filter from the stored allSolvedProblems state for better performance

  // Fetch all solved questions for the user to show comprehensive statistics
  const {
    data: allSolvedData,
    isLoading: allSolvedLoading,
    error: allSolvedError,
  } = useAllSolvedQuestionsClient({
    handle: debouncedHandle,
    enabled: !!debouncedHandle.trim(), // Fetch whenever a valid handle is provided
  });

  // Effect to populate allSolvedProblems state when data is fetched
  // This stores all problems in state for efficient date range filtering
  React.useEffect(() => {
    if (allSolvedData?.result?.submissions) {
      setAllSolvedProblems(allSolvedData.result.submissions);
    } else {
      setAllSolvedProblems([]);
    }
  }, [allSolvedData]);

  // Filter problems based on the selected date range from graph zoom
  // This creates a filtered list that respects the user's graph selection
  const problemsToDisplay = React.useMemo(() => {
    if (!submissionsDateRange || allSolvedProblems.length === 0) {
      // If no date range is selected, show all problems
      return allSolvedProblems;
    }

    // Convert date strings to timestamps for filtering
    const startTimestamp =
      new Date(submissionsDateRange.startDate + "T00:00:00Z").getTime() / 1000;
    const endTimestamp =
      new Date(submissionsDateRange.endDate + "T23:59:59Z").getTime() / 1000;

    // Filter problems that were solved within the selected date range
    return allSolvedProblems.filter((submission: any) => {
      return (
        submission.creationTimeSeconds >= startTimestamp &&
        submission.creationTimeSeconds <= endTimestamp
      );
    });
  }, [allSolvedProblems, submissionsDateRange]);

  // ============================================================================
  // DATA EXTRACTION & DERIVED STATE (HOOKS)
  // ============================================================================
  // All hooks must be called at the top level, before any conditional returns.
  // We use optional chaining and default values to handle the initial loading state.

  const { ratingHistory, currentRating, maxRating, minRating, totalContests } =
    data?.result || {};

  // Calculate dynamic Y-axis domain based on user's rating range
  const calculateYAxisDomain = React.useMemo(() => {
    if (minRating === undefined || maxRating === undefined) {
      return [1100, 1500]; // Default range while loading
    }
    // Add padding above and below the actual rating range for better visualization
    const padding = Math.max(100, (maxRating - minRating) * 0.1); // At least 100 points padding

    // Calculate the range bounds
    const lowerBound = Math.max(0, minRating - padding);
    const upperBound = maxRating + padding;

    // Round to nearest 100 for cleaner axis values
    const roundedLowerBound = Math.floor(lowerBound / 100) * 100;
    const roundedUpperBound = Math.ceil(upperBound / 100) * 100;

    return [roundedLowerBound, roundedUpperBound];
  }, [minRating, maxRating]);

  // Generate dynamic rating ticks based on the calculated range
  const ratingTicks = React.useMemo(() => {
    const [lowerBound, upperBound] = calculateYAxisDomain;
    const allTicks = [1200, 1400, 1600, 1900, 2100, 2300, 2400, 2600, 3000];

    // Filter ticks to only show those within the visible range
    const visibleTicks = allTicks.filter(
      (tick) => tick >= lowerBound && tick <= upperBound
    );

    // Add boundary ticks if they're not already included
    if (visibleTicks.length === 0 || visibleTicks[0] > lowerBound + 100) {
      visibleTicks.unshift(lowerBound);
    }
    if (
      visibleTicks.length > 0 &&
      visibleTicks[visibleTicks.length - 1] < upperBound - 100
    ) {
      visibleTicks.push(upperBound);
    }

    return visibleTicks;
  }, [calculateYAxisDomain]);

  // Memoize the expensive daily data transformation
  const dailyRatingData = React.useMemo(() => {
    if (!ratingHistory) return [];
    const result = transformToDaily(ratingHistory);
    return result;
  }, [ratingHistory]);

  // Memoize chart data sampling for performance
  const chartData = React.useMemo(() => {
    const data = dailyRatingData;
    if (data.length > 1000) {
      const sampledData: DailyRatingData[] = [];
      const sampleRate = Math.ceil(data.length / 800);
      for (let i = 0; i < data.length; i++) {
        const point = data[i];
        if (
          point.isContestDay ||
          i % sampleRate === 0 ||
          i === data.length - 1
        ) {
          sampledData.push(point);
        }
      }

      return sampledData;
    }

    return data;
  }, [dailyRatingData]);

  // Memoize chart data based on the selected date range
  // Only update chart data for brush selection, NOT for start/end point selection
  const chartDataToDisplay = React.useMemo(() => {
    // Only use brush selection for chart filtering, ignore start/end points to prevent re-renders
    if (
      brushSelection.startIndex !== undefined &&
      brushSelection.endIndex !== undefined
    ) {
      const slicedData = chartData.slice(
        brushSelection.startIndex,
        brushSelection.endIndex + 1
      );

      return slicedData;
    }

    return chartData;
  }, [brushSelection, chartData]); // Removed startPoint, endPoint from dependencies

  // ============================================================================
  // DATE INPUT HANDLERS AND SYNCHRONIZATION
  // ============================================================================
  // Functions to handle date input changes and sync with brush selection

  // Initialize date inputs when data is loaded
  React.useEffect(() => {
    if (chartData.length > 0) {
      const firstDate = new Date(chartData[0].date);
      const lastDate = new Date(chartData[chartData.length - 1].date);

      // Always set to full range when we have new chart data and empty date inputs
      // This ensures new users always start with full range view
      if (!startMonth && !startYear && !endMonth && !endYear) {
        // Set date inputs to full range
        setStartMonth(String(firstDate.getMonth() + 1).padStart(2, "0"));
        setStartYear(String(firstDate.getFullYear()));
        setEndMonth(String(lastDate.getMonth() + 1).padStart(2, "0"));
        setEndYear(String(lastDate.getFullYear()));

        // Explicitly clear brush selection to ensure full chart is shown
        // Use setTimeout to ensure this happens after any pending state updates
        setTimeout(() => {
          setBrushSelection({});
        }, 0);
      }
    }
  }, [chartData, startMonth, startYear, endMonth, endYear]);

  // Additional effect to ensure brush selection is cleared when new chart data loads
  // This is a safety net to prevent zoom state from persisting across users
  React.useEffect(() => {
    if (chartData.length > 0 && isInitializingNewUser) {
      setBrushSelection({});
    }
  }, [chartData, isInitializingNewUser]);

  // Validation function to check date range
  const validateDateRange = React.useCallback(
    (
      startMonth: string,
      startYear: string,
      endMonth: string,
      endYear: string
    ) => {
      if (!startMonth || !startYear || !endMonth || !endYear) {
        return { isValid: true, error: null };
      }

      const startDate = new Date(
        parseInt(startYear),
        parseInt(startMonth) - 1,
        1
      );
      const endDate = new Date(parseInt(endYear), parseInt(endMonth), 0); // Last day of the month

      // Calculate the difference in days
      const diffInMs = endDate.getTime() - startDate.getTime();
      const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));

      if (diffInDays < 0) {
        return {
          isValid: false,
          error: "End date must be after start date.",
        };
      }

      return { isValid: true, error: null };
    },
    []
  );

  // Handle date input changes and update brush selection
  const handleDateInputChange = React.useCallback(() => {
    // Skip if we're initializing a new user to prevent auto-zoom
    if (isInitializingNewUser || skipNextDateInputChange.current) {
      if (skipNextDateInputChange.current) {
        skipNextDateInputChange.current = false; // Reset the flag after skipping once
      }
      return;
    }

    if (
      !startMonth ||
      !startYear ||
      !endMonth ||
      !endYear ||
      chartData.length === 0
    ) {
      return;
    }

    // Validate the date range first
    const validation = validateDateRange(
      startMonth,
      startYear,
      endMonth,
      endYear
    );
    setDateRangeError(validation.error);

    // If validation fails, don't update the brush selection
    if (!validation.isValid) {
      return;
    }

    const startDate = new Date(
      parseInt(startYear),
      parseInt(startMonth) - 1,
      1
    );
    const endDate = new Date(parseInt(endYear), parseInt(endMonth), 0); // Last day of the month

    // Check if this represents the full date range (no zoom needed)
    const firstDate = new Date(chartData[0].date);
    const lastDate = new Date(chartData[chartData.length - 1].date);

    const isFullRange =
      startDate.getTime() <= firstDate.getTime() &&
      endDate.getTime() >= lastDate.getTime();

    if (isFullRange) {
      setBrushSelection({}); // Clear brush selection to show full chart
      return;
    }

    // Find the closest data points to the selected dates
    let startIndex = 0;
    let endIndex = chartData.length - 1;

    for (let i = 0; i < chartData.length; i++) {
      const dataDate = new Date(chartData[i].date);
      if (dataDate >= startDate) {
        startIndex = i;
        break;
      }
    }

    for (let i = chartData.length - 1; i >= 0; i--) {
      const dataDate = new Date(chartData[i].date);
      if (dataDate <= endDate) {
        endIndex = i;
        break;
      }
    }

    setBrushSelection({ startIndex, endIndex });
  }, [
    startMonth,
    startYear,
    endMonth,
    endYear,
    chartData,
    validateDateRange,
    isInitializingNewUser,
  ]);

  // Debounced date input change handler to prevent excessive updates
  React.useEffect(() => {
    const timer = setTimeout(() => {
      handleDateInputChange();
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timer);
  }, [startMonth, startYear, endMonth, endYear, handleDateInputChange]);

  // ============================================================================
  // ENHANCED START/END POINT HANDLERS WITH VALIDATION
  // ============================================================================
  // Updated handlers to use the new state management and provide better validation

  const handleSetStartPoint = React.useCallback(
    (data: DailyRatingData) => {
      // Clear any previous error
      setRangeSelectionError(null);

      // Create the new start point
      const newStartPoint: SelectedPoint = {
        date: data.date,
        rating: data.rating,
        dateTimestamp: data.dateTimestamp,
        contestName: data.contestName,
        isContestDay: data.isContestDay,
      };

      // Validate against existing end point
      if (endPoint && data.dateTimestamp >= endPoint.dateTimestamp) {
        setRangeSelectionError("Start date must be before the end date");
        return;
      }

      // Set the new start point
      setStartPoint(newStartPoint);
    },
    [endPoint]
  );

  const handleSetEndPoint = React.useCallback(
    (data: DailyRatingData) => {
      // Clear any previous error
      setRangeSelectionError(null);

      // Create the new end point
      const newEndPoint: SelectedPoint = {
        date: data.date,
        rating: data.rating,
        dateTimestamp: data.dateTimestamp,
        contestName: data.contestName,
        isContestDay: data.isContestDay,
      };

      // Validate against existing start point
      if (startPoint && data.dateTimestamp <= startPoint.dateTimestamp) {
        setRangeSelectionError("End date must be after the start date");
        return;
      }

      // Set the new end point
      setEndPoint(newEndPoint);

      // NOTE: Removed automatic zoom to prevent chart rerendering
      // Users can manually zoom using the "Zoom to Range" button
    },
    [startPoint]
  );

  // Memoize tick formatters to prevent unnecessary re-renders
  const xAxisTickFormatter = React.useCallback((value: string) => {
    const date = new Date(value);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  }, []);

  // ============================================================================
  // INFO BUTTON HANDLERS
  // ============================================================================
  // Handler for the info button click behavior

  const handleInfoButtonClick = React.useCallback(() => {
    if (!hasInfoBeenClicked) {
      // First click: stop blinking and open modal
      setHasInfoBeenClicked(true);
    }
    // Toggle modal (works for both first click and subsequent clicks)
    setIsInfoModalOpen((prev) => !prev);
  }, [hasInfoBeenClicked]);

  // ============================================================================
  // CONDITIONAL RENDERING
  // ============================================================================
  // Now that all hooks are called, we can safely return early.

  // Show placeholder when no handle is provided
  if (!handle.trim()) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-lg">
        <p className="text-gray-400">Enter a handle to view rating graph</p>
      </div>
    );
  }

  // Show loading spinner while fetching data
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-400">Loading rating history...</p>
        </div>
      </div>
    );
  }

  // Show error state with retry button
  if (error) {
    return (
      <div className="bg-red-950 border border-red-800 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-red-300 font-semibold">
              Error loading rating history
            </h3>
            <p className="text-red-400 text-sm mt-1">
              {error instanceof Error
                ? error.message
                : "Failed to fetch rating data"}
            </p>
          </div>
          {/* Retry button to refetch data */}
          <button
            onClick={() => refetch()}
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // This check is important to satisfy TypeScript that `data` is defined below
  if (!data?.result) {
    // This state should ideally be handled by the loading/error states above,
    // but this provides a fallback and type guard.
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-lg">
        <p className="text-gray-400">Preparing rating data...</p>
      </div>
    );
  }

  // Show message when user has no rating history
  if (!ratingHistory || ratingHistory.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-800 rounded-lg">
        <p className="text-gray-400">No rating history found for this user</p>
      </div>
    );
  }

  // Extract rating data and statistics from the API response
  const {
    currentRating: currentRatingValue,
    maxRating: maxRatingValue,
    totalContests: totalContestsValue,
  } = data.result;

  return (
    <div className="bg-[#0d1117] rounded-lg border border-gray-800 p-4 relative">
      {/* Header section with title, statistics, and reset button */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <div className="flex items-center gap-3">
            <h3 className="text-2xl font-semibold text-gray-200">
              Daily Rating Progression for{" "}
              <span
                className="font-bold"
                style={{ color: getRatingColor(currentRatingValue) }}
                title={`${getRatingTitle(
                  currentRatingValue
                )} (${currentRatingValue})`}
              >
                {handle}
              </span>
            </h3>
            {/* Info Button with blinking animation */}
            <button
              onClick={handleInfoButtonClick}
              className={`w-6 h-6 rounded-full bg-blue-600/20 border border-blue-500/30 flex items-center justify-center hover:bg-blue-600/30 transition-all duration-200 ${
                !hasInfoBeenClicked ? "animate-slow-blink" : ""
              }`}
              title="How to use the rating graph"
            >
              <span className="text-blue-400 text-xs font-bold">i</span>
            </button>
          </div>
          <p className="text-lg text-gray-400 mt-1">
            📊 Shows rating for every day • 🔍 Use the date inputs to zoom into
            specific periods
          </p>
          <p className="text-sm text-blue-400 mt-1">
            💡 Click any point on the graph to select it - the action buttons
            below will become active for setting start/end points
          </p>
          {/* Display key statistics with color-coded ratings */}
          <div className="flex items-center gap-4 mt-2 text-lg text-gray-300">
            <span>
              Current:{" "}
              <span
                className="font-semibold"
                style={{ color: getRatingColor(currentRatingValue) }}
              >
                {currentRatingValue}
              </span>
            </span>
            <span>
              Max:{" "}
              <span
                className="font-semibold"
                style={{ color: getRatingColor(maxRatingValue) }}
              >
                {maxRatingValue}
              </span>
            </span>
            <span>
              Contests:{" "}
              <span className="font-semibold">{totalContestsValue}</span>
            </span>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {/* Reset button to clear all selections and filters */}
          <button
            onClick={() => {
              // Clear all selection state
              setStartPoint(null);
              setEndPoint(null);
              setRangeSelectionError(null);
              setPinnedTooltipData(null); // Also clear any pinned tooltip

              // Clear the date range context
              setSubmissionsDateRange(null);

              // Reset date range filter to default (full range)
              if (chartData.length > 0) {
                const firstDate = new Date(chartData[0].date);
                const lastDate = new Date(chartData[chartData.length - 1].date);

                // Reset to full date range
                setStartMonth(
                  String(firstDate.getMonth() + 1).padStart(2, "0")
                );
                setStartYear(String(firstDate.getFullYear()));
                setEndMonth(String(lastDate.getMonth() + 1).padStart(2, "0"));
                setEndYear(String(lastDate.getFullYear()));
                setBrushSelection({});
                setDateRangeError(null);
              }
            }}
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
          >
            Reset
          </button>
        </div>
      </div>

      {/* Range Selection Buttons - positioned below header and above graph */}
      {/* Always render the container to prevent page rerendering, but make it semi-transparent when no point is selected */}
      <div
        className={`mb-6 transition-all duration-300 ${
          pinnedTooltipData &&
          pinnedTooltipData.payload &&
          pinnedTooltipData.payload.length > 0
            ? "opacity-100"
            : "opacity-50 pointer-events-none"
        }`}
      >
        <div className="flex gap-3 justify-center flex-wrap">
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (
                pinnedTooltipData &&
                pinnedTooltipData.payload &&
                pinnedTooltipData.payload.length > 0
              ) {
                const data = pinnedTooltipData.payload[0].payload;
                handleSetStartPoint(data);

                // Add visual feedback
                const button = e.currentTarget;
                button.style.transform = "scale(0.95)";
                setTimeout(() => {
                  button.style.transform = "scale(1)";
                }, 150);
              }
            }}
            disabled={
              !pinnedTooltipData ||
              !pinnedTooltipData.payload ||
              pinnedTooltipData.payload.length === 0 ||
              startPoint?.date ===
                pinnedTooltipData?.payload?.[0]?.payload?.date
            }
            className={`group relative px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 hover:-translate-y-1 active:scale-95 shadow-lg hover:shadow-xl ${
              pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              startPoint?.date === pinnedTooltipData.payload[0].payload.date
                ? "bg-gradient-to-r from-green-600 to-green-500 text-white cursor-default shadow-green-500/25"
                : "bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white hover:shadow-blue-500/50 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-gray-900"
            }`}
          >
            <span className="flex items-center gap-2">
              {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              startPoint?.date === pinnedTooltipData.payload[0].payload.date ? (
                <>
                  <span className="animate-pulse">✅</span>
                  Start Point Set
                </>
              ) : (
                <>
                  <span className="group-hover:animate-bounce">🚀</span>
                  Set as Start
                </>
              )}
            </span>
            {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              startPoint?.date !==
                pinnedTooltipData.payload[0].payload.date && (
                <div className="absolute inset-0 rounded-lg bg-white opacity-0 group-hover:opacity-20 transition-all duration-200 group-hover:animate-pulse"></div>
              )}
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              if (
                pinnedTooltipData &&
                pinnedTooltipData.payload &&
                pinnedTooltipData.payload.length > 0
              ) {
                const data = pinnedTooltipData.payload[0].payload;
                handleSetEndPoint(data);

                // Add visual feedback
                const button = e.currentTarget;
                button.style.transform = "scale(0.95)";
                setTimeout(() => {
                  button.style.transform = "scale(1)";
                }, 150);
              }
            }}
            disabled={
              !pinnedTooltipData ||
              !pinnedTooltipData.payload ||
              pinnedTooltipData.payload.length === 0 ||
              endPoint?.date === pinnedTooltipData?.payload?.[0]?.payload?.date
            }
            className={`group relative px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 hover:-translate-y-1 active:scale-95 shadow-lg hover:shadow-xl ${
              pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              endPoint?.date === pinnedTooltipData.payload[0].payload.date
                ? "bg-gradient-to-r from-green-600 to-green-500 text-white cursor-default shadow-green-500/25"
                : "bg-gradient-to-r from-orange-600 to-orange-500 hover:from-orange-500 hover:to-orange-400 text-white hover:shadow-orange-500/50 cursor-pointer focus:outline-none focus:ring-2 focus:ring-orange-400 focus:ring-offset-2 focus:ring-offset-gray-900"
            }`}
          >
            <span className="flex items-center gap-2">
              {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              endPoint?.date === pinnedTooltipData.payload[0].payload.date ? (
                <>
                  <span className="animate-pulse">✅</span>
                  End Point Set
                </>
              ) : (
                <>
                  <span className="group-hover:animate-bounce">🏁</span>
                  Set as End
                </>
              )}
            </span>
            {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 &&
              endPoint?.date !== pinnedTooltipData.payload[0].payload.date && (
                <div className="absolute inset-0 rounded-lg bg-white opacity-0 group-hover:opacity-20 transition-all duration-200 group-hover:animate-pulse"></div>
              )}
          </button>

          <button
            onClick={async (e) => {
              if (startPoint && endPoint && !isZoomingToRange) {
                setIsZoomingToRange(true);

                // Add visual feedback
                const button = e.currentTarget;
                button.style.transform = "scale(0.95)";

                // Use setTimeout to allow React to update the UI
                setTimeout(() => {
                  const startDate = new Date(startPoint.date);
                  const endDate = new Date(endPoint.date);

                  setDateRangeError(null);
                  setStartMonth(
                    String(startDate.getMonth() + 1).padStart(2, "0")
                  );
                  setStartYear(String(startDate.getFullYear()));
                  setEndMonth(String(endDate.getMonth() + 1).padStart(2, "0"));
                  setEndYear(String(endDate.getFullYear()));

                  // Set the date range for graph context display
                  setSubmissionsDateRange({
                    startDate: startPoint.date,
                    endDate: endPoint.date,
                  });

                  // Reset button state after action
                  setTimeout(() => {
                    setIsZoomingToRange(false);
                    button.style.transform = "scale(1)";
                  }, 500);
                }, 300);
              }
            }}
            disabled={!startPoint || !endPoint || isZoomingToRange}
            className={`group relative px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 hover:-translate-y-1 active:scale-95 shadow-lg hover:shadow-xl ${
              startPoint && endPoint && !isZoomingToRange
                ? "bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-500 hover:to-purple-400 text-white hover:shadow-purple-500/50 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-gray-900"
                : "bg-gradient-to-r from-gray-600 to-gray-500 text-gray-300 cursor-not-allowed"
            }`}
          >
            <span className="flex items-center gap-2">
              <span
                className={
                  startPoint && endPoint && !isZoomingToRange
                    ? "group-hover:animate-pulse"
                    : isZoomingToRange
                    ? "animate-spin"
                    : ""
                }
              >
                {isZoomingToRange ? "⚡" : "🔍"}
              </span>
              {isZoomingToRange
                ? "Fetching..."
                : "Retrieve problems from the selected range"}
            </span>
            {startPoint && endPoint && !isZoomingToRange && (
              <div className="absolute inset-0 rounded-lg bg-white opacity-0 group-hover:opacity-20 transition-all duration-200 group-hover:animate-pulse"></div>
            )}
          </button>

          <button
            onClick={(e) => {
              const button = e.currentTarget;

              // Add visual feedback
              button.style.transform = "scale(0.95)";
              setTimeout(() => {
                setPinnedTooltipData(null);
                button.style.transform = "scale(1)";
              }, 150);
            }}
            disabled={
              !pinnedTooltipData ||
              !pinnedTooltipData.payload ||
              pinnedTooltipData.payload.length === 0
            }
            className={`group relative px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 hover:-translate-y-1 active:scale-95 shadow-lg hover:shadow-xl ${
              pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0
                ? "bg-gradient-to-r from-gray-600 to-gray-500 hover:from-gray-500 hover:to-gray-400 text-white hover:shadow-gray-500/50 cursor-pointer focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-gray-900"
                : "bg-gradient-to-r from-gray-700 to-gray-600 text-gray-400 cursor-not-allowed"
            }`}
          >
            <span className="flex items-center gap-2">
              <span
                className={`${
                  pinnedTooltipData &&
                  pinnedTooltipData.payload &&
                  pinnedTooltipData.payload.length > 0
                    ? "group-hover:rotate-90"
                    : ""
                } transition-transform duration-200`}
              >
                ✕
              </span>
              Close
            </span>
            {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 && (
                <div className="absolute inset-0 rounded-lg bg-white opacity-0 group-hover:opacity-20 transition-all duration-200 group-hover:animate-pulse"></div>
              )}
          </button>
        </div>
      </div>

      {/* ============================================================================ */}
      {/* RECHARTS LINE CHART CONFIGURATION */}
      {/* ============================================================================ */}
      {/* Main chart container with responsive sizing */}
      <div className="w-full">
        <div
          className="w-full"
          style={{
            height: height + 80, // Increase total height to accommodate larger bottom margin
            minWidth: "700px",
            pointerEvents: "auto", // Ensure pointer events are enabled
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            {/* Main line chart with rating data */}
            <LineChart
              key={`chart-${chartDataToDisplay.length}-${
                chartDataToDisplay[0]?.date || "empty"
              }-${
                chartDataToDisplay[chartDataToDisplay.length - 1]?.date ||
                "empty"
              }-${startPoint?.date || "no-start"}-${
                endPoint?.date || "no-end"
              }`}
              data={chartDataToDisplay}
              margin={{ top: 20, right: 30, left: 20, bottom: 70 }} // Add more space at the bottom
              style={{ cursor: "pointer" }}
              onMouseMove={(e: any) => {
                if (
                  e.isTooltipActive &&
                  e.activeCoordinate &&
                  e.activeIndex !== undefined &&
                  e.activeIndex !== null
                ) {
                  // Convert activeIndex to number if it's a string
                  const activeIndex =
                    typeof e.activeIndex === "string"
                      ? parseInt(e.activeIndex, 10)
                      : e.activeIndex;

                  if (
                    !isNaN(activeIndex) &&
                    activeIndex >= 0 &&
                    activeIndex < chartDataToDisplay.length
                  ) {
                    // Get the data point from the chart data using the active index
                    const dataPoint = chartDataToDisplay[activeIndex];
                    latestHoverData.current = {
                      payload: [{ payload: dataPoint }], // Mimic the expected structure
                      coordinate: e.activeCoordinate,
                    };
                  }
                }
                // Don't clear latestHoverData.current when moving away from data points
                // Keep the last valid hover data for clicking
              }}
              onClick={() => {
                // Use the latest hover data to determine what to pin
                if (latestHoverData.current) {
                  setPinnedTooltipData({
                    payload: latestHoverData.current.payload,
                    coordinate: latestHoverData.current.coordinate,
                  });
                } else {
                  // Only clear pinned tooltip if clicking on empty space
                  setPinnedTooltipData(null);
                }
              }}
            >
              {/* Grid lines for better readability */}
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              {/* X-axis showing dates */}
              <XAxis
                dataKey="date"
                stroke="#9ca3af"
                fontSize={12}
                label={{
                  value: "Date",
                  position: "insideBottom",
                  offset: -60, // Adjust label position to be at the very bottom
                }}
                tickFormatter={xAxisTickFormatter}
                interval="preserveStartEnd" // Only show start and end dates by default
                // Move ticks lower to avoid overlap with the brush
                tick={{ dy: 10 }}
              />
              {/* Y-axis showing rating values with dynamic range */}
              <YAxis
                stroke="#9ca3af"
                fontSize={12}
                label={{ value: "Rating", angle: -90, position: "insideLeft" }}
                domain={calculateYAxisDomain}
                ticks={ratingTicks}
              />
              {/* Custom tooltip component for hover interactions */}
              <Tooltip
                content={({ active, payload, label }) => (
                  <CustomTooltip
                    active={active}
                    payload={payload}
                    label={label}
                    startPoint={startPoint}
                    endPoint={endPoint}
                    submissionsData={
                      allSolvedProblems.length > 0
                        ? { result: { submissions: allSolvedProblems } }
                        : null
                    }
                  />
                )}
                cursor={true}
              />
              {/* Dynamic reference lines for Codeforces rating thresholds */}
              {/* Only show reference lines that are visible within the current Y-axis range */}
              {[1200, 1400, 1600, 1900, 2100, 2300, 2400, 2600, 3000]
                .filter((rating) => {
                  const [lowerBound, upperBound] = calculateYAxisDomain;
                  return rating >= lowerBound && rating <= upperBound;
                })
                .map((rating) => (
                  <ReferenceLine
                    key={rating}
                    y={rating}
                    stroke={getRatingColor(rating)}
                    strokeDasharray="3 3"
                    strokeOpacity={0.7}
                  />
                ))}
              {/* Main rating line showing daily progression over time */}
              <Line
                type="monotone" // Smooth line interpolation
                dataKey="rating" // Use rating field from daily data
                stroke="#3b82f6" // Blue line color
                strokeWidth={2} // Line thickness
                dot={false} // Hide dots for daily view (too many points)
                isAnimationActive={false} // Disable animation to prevent replay on rerender
              />

              {/* Visual markers for selected start and end points */}
              {startPoint && (
                <ReferenceDot
                  x={startPoint.date}
                  y={startPoint.rating}
                  r={10}
                  fill="#22c55e"
                  stroke="white"
                  strokeWidth={3}
                  label={{
                    value: "START",
                    position: "top",
                    fill: "#22c55e",
                    fontSize: 12,
                    fontWeight: "bold",
                  }}
                />
              )}
              {endPoint && (
                <ReferenceDot
                  x={endPoint.date}
                  y={endPoint.rating}
                  r={10}
                  fill="#f97316"
                  stroke="white"
                  strokeWidth={3}
                  label={{
                    value: "END",
                    position: "top",
                    fill: "#f97316",
                    fontSize: 12,
                    fontWeight: "bold",
                  }}
                />
              )}

              {/* Pinned tooltip marker */}
              {pinnedTooltipData &&
                pinnedTooltipData.payload &&
                pinnedTooltipData.payload.length > 0 && (
                  <ReferenceDot
                    x={pinnedTooltipData.payload[0].payload.date}
                    y={pinnedTooltipData.payload[0].payload.rating}
                    r={8}
                    fill="#ffeb3b"
                    stroke="white"
                    strokeWidth={2}
                  />
                )}
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Horizontal tabs below the graph */}
        <div className="w-full mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
            {/* Tab 1: Selected Point */}
            <div className="lg:col-span-7 bg-gradient-to-br from-blue-950 to-blue-900 border border-blue-700 rounded-xl p-4 shadow-lg">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                <p className="text-blue-200 font-semibold">Selected Point</p>
              </div>
              {pinnedTooltipData &&
              pinnedTooltipData.payload &&
              pinnedTooltipData.payload.length > 0 ? (
                <div className="space-y-3">
                  <button
                    onClick={() => setPinnedTooltipData(null)}
                    className="text-blue-400 hover:text-blue-200 text-sm w-full text-right"
                  >
                    Clear ×
                  </button>
                  <div className="bg-gray-900/50 rounded-lg p-3 border border-gray-700">
                    <CustomTooltip
                      active={true}
                      payload={pinnedTooltipData.payload}
                      onClose={() => setPinnedTooltipData(null)}
                      isPinned={true}
                      startPoint={startPoint}
                      endPoint={endPoint}
                      submissionsData={
                        allSolvedProblems.length > 0
                          ? { result: { submissions: allSolvedProblems } }
                          : null
                      }
                    />
                  </div>
                </div>
              ) : (
                <p className="text-blue-300 text-sm">
                  Click any point on the graph to select it
                </p>
              )}
            </div>

            {/* Tab 2: Selected Range */}
            <div className="lg:col-span-3 bg-gradient-to-br from-emerald-950 to-emerald-900 border border-emerald-700 rounded-xl p-4 shadow-lg">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-3 h-3 bg-emerald-400 rounded-full"></div>
                <p className="text-emerald-200 font-semibold">Selected Range</p>
              </div>

              {/* Error displays */}
              {rangeSelectionError && (
                <div className="bg-red-950 border border-red-800 rounded-lg p-2 mb-3">
                  <p className="text-red-300 text-xs font-medium">
                    ⚠️ Selection Error
                  </p>
                  <p className="text-red-400 text-xs">{rangeSelectionError}</p>
                </div>
              )}

              {!startPoint && !endPoint ? (
                <p className="text-emerald-300 text-sm">
                  Select start and end points from the graph to analyze a range
                </p>
              ) : (
                <div className="space-y-3">
                  {startPoint && (
                    <div className="bg-green-900/30 border border-green-700/50 rounded-lg p-2">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span className="text-green-300 font-medium text-xs">
                          START
                        </span>
                      </div>
                      <p className="text-green-100 text-xs">
                        {startPoint.date}
                      </p>
                      <p className="text-green-200 text-xs">
                        Rating: {startPoint.rating}
                      </p>
                    </div>
                  )}

                  {endPoint && (
                    <div className="bg-orange-900/30 border border-orange-700/50 rounded-lg p-2">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                        <span className="text-orange-300 font-medium text-xs">
                          END
                        </span>
                      </div>
                      <p className="text-orange-100 text-xs">{endPoint.date}</p>
                      <p className="text-orange-200 text-xs">
                        Rating: {endPoint.rating}
                      </p>
                    </div>
                  )}

                  {startPoint && endPoint && (
                    <div className="bg-gray-900/30 border border-gray-700/50 rounded-lg p-2">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        <span className="text-blue-300 font-medium text-xs">
                          ANALYSIS
                        </span>
                      </div>
                      <div className="space-y-1">
                        <p className="text-gray-300 text-xs">
                          Duration:{" "}
                          <span className="text-blue-300 font-medium">
                            {Math.ceil(
                              (endPoint.dateTimestamp -
                                startPoint.dateTimestamp) /
                                (24 * 60 * 60)
                            )}{" "}
                            days
                          </span>
                        </p>
                        <p className="text-gray-300 text-xs">
                          Change:{" "}
                          <span
                            className={`font-medium ${
                              endPoint.rating > startPoint.rating
                                ? "text-green-400"
                                : endPoint.rating < startPoint.rating
                                ? "text-red-400"
                                : "text-gray-400"
                            }`}
                          >
                            {endPoint.rating > startPoint.rating ? "+" : ""}
                            {endPoint.rating - startPoint.rating}
                          </span>
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2 mt-3">
                    <button
                      onClick={() => {
                        // Clear all selection state
                        setStartPoint(null);
                        setEndPoint(null);
                        setRangeSelectionError(null);
                        setPinnedTooltipData(null); // Also clear any pinned tooltip

                        // Clear the date range context
                        setSubmissionsDateRange(null);

                        // Reset date range filter to default (full range)
                        if (chartData.length > 0) {
                          const firstDate = new Date(chartData[0].date);
                          const lastDate = new Date(
                            chartData[chartData.length - 1].date
                          );

                          // Reset to full date range
                          setStartMonth(
                            String(firstDate.getMonth() + 1).padStart(2, "0")
                          );
                          setStartYear(String(firstDate.getFullYear()));
                          setEndMonth(
                            String(lastDate.getMonth() + 1).padStart(2, "0")
                          );
                          setEndYear(String(lastDate.getFullYear()));
                          setBrushSelection({});
                          setDateRangeError(null);
                        }
                      }}
                      className="flex-1 px-2 py-1 text-xs bg-red-600 text-white hover:bg-red-700 rounded font-medium"
                    >
                      Reset
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Tab 3: Date Range Filter */}
            <div className="lg:col-span-2 bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-4 shadow-lg">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                <span className="font-semibold text-purple-200">
                  Date Range Filter
                </span>
              </div>

              {/* Error display for date range validation */}
              {dateRangeError && (
                <div className="bg-red-950 border border-red-800 rounded-lg p-2 mb-3">
                  <p className="text-red-300 text-xs font-medium">
                    ⚠️ Date Range Error
                  </p>
                  <p className="text-red-400 text-xs">{dateRangeError}</p>
                </div>
              )}

              <p className="text-xs text-purple-300 mb-3">
                📅 Filter by date range
              </p>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-gray-400 text-xs w-8">From:</span>
                  <div className="flex items-center gap-1">
                    <input
                      type="number"
                      placeholder="MM"
                      min="1"
                      max="12"
                      value={startMonth}
                      onChange={(e) => setStartMonth(e.target.value)}
                      className="w-12 px-1 py-1 bg-gray-800 border border-gray-600 rounded text-center text-xs focus:border-purple-500 focus:outline-none"
                    />
                    <span className="text-gray-500 text-xs">/</span>
                    <input
                      type="number"
                      placeholder="YYYY"
                      min="2000"
                      max="2030"
                      value={startYear}
                      onChange={(e) => setStartYear(e.target.value)}
                      className="w-16 px-1 py-1 bg-gray-800 border border-gray-600 rounded text-center text-xs focus:border-purple-500 focus:outline-none"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-gray-400 text-xs w-8">To:</span>
                  <div className="flex items-center gap-1">
                    <input
                      type="number"
                      placeholder="MM"
                      min="1"
                      max="12"
                      value={endMonth}
                      onChange={(e) => setEndMonth(e.target.value)}
                      className="w-12 px-1 py-1 bg-gray-800 border border-gray-600 rounded text-center text-xs focus:border-purple-500 focus:outline-none"
                    />
                    <span className="text-gray-500 text-xs">/</span>
                    <input
                      type="number"
                      placeholder="YYYY"
                      min="2000"
                      max="2030"
                      value={endYear}
                      onChange={(e) => setEndYear(e.target.value)}
                      className="w-16 px-1 py-1 bg-gray-800 border border-gray-600 rounded text-center text-xs focus:border-purple-500 focus:outline-none"
                    />
                  </div>
                </div>
              </div>

              <button
                onClick={() => {
                  if (chartData.length > 0) {
                    const firstDate = new Date(chartData[0].date);
                    const lastDate = new Date(
                      chartData[chartData.length - 1].date
                    );

                    // Reset to full date range
                    setStartMonth(
                      String(firstDate.getMonth() + 1).padStart(2, "0")
                    );
                    setStartYear(String(firstDate.getFullYear()));
                    setEndMonth(
                      String(lastDate.getMonth() + 1).padStart(2, "0")
                    );
                    setEndYear(String(lastDate.getFullYear()));
                    setBrushSelection({});
                    setDateRangeError(null);
                    // Clear the problem filtering date range as well
                    setSubmissionsDateRange(null);
                  }
                }}
                className="w-full mt-3 px-2 py-1 text-xs bg-purple-600 text-white hover:bg-purple-700 rounded font-medium"
              >
                Reset Filter
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* All Solved Questions Statistics Section */}
      {allSolvedData && !allSolvedLoading && (
        <div className="mt-8">
          <div className="bg-gradient-to-r from-green-900/20 to-blue-900/20 border border-green-700/30 rounded-xl p-6 shadow-lg">
            <div className="flex items-center gap-3 mb-6">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <h3 className="text-xl font-bold text-green-300">
                All Solved Problems Statistics
              </h3>
              <div className="flex-1 h-px bg-gradient-to-r from-green-400/50 to-transparent"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Average Rating */}
              <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-1">
                    {(() => {
                      const ratedSubmissions =
                        allSolvedData.result.submissions.filter(
                          (s) => s.problem.rating
                        );
                      if (ratedSubmissions.length === 0) return "N/A";

                      const sortedRatings = ratedSubmissions
                        .map((s) => s.problem.rating!)
                        .sort((a, b) => a - b);

                      const middleIndex = Math.floor(sortedRatings.length / 2);
                      const median =
                        sortedRatings.length % 2 === 0
                          ? (sortedRatings[middleIndex - 1] +
                              sortedRatings[middleIndex]) /
                            2
                          : sortedRatings[middleIndex];

                      return Math.round(median + 100);
                    })()}
                  </div>
                  <div className="text-sm text-gray-300">
                    Average Problem Rating
                  </div>
                </div>
              </div>

              {/* Latest Solve */}
              <div className="bg-gray-800/50 border border-gray-600 rounded-lg p-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-cyan-400 mb-1">
                    {allSolvedData.result.submissions.length > 0
                      ? new Date(
                          allSolvedData.result.submissions[0]
                            .creationTimeSeconds * 1000
                        ).toLocaleDateString()
                      : "N/A"}
                  </div>
                  <div className="text-sm text-gray-300">Latest Solve</div>
                </div>
              </div>
            </div>

            {/* Difficulty Distribution Chart */}
            {allSolvedData.result.submissions.length > 0 && (
              <div className="mt-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full"></div>
                  <h4 className="text-xl font-semibold text-gray-200">
                    Difficulty Distribution
                  </h4>
                  <div className="flex-1 h-px bg-gradient-to-r from-blue-400/50 to-transparent"></div>
                </div>

                {(() => {
                  // Process data for the chart
                  const ratingCounts = allSolvedData.result.submissions
                    .filter((s) => s.problem.rating)
                    .reduce((acc, s) => {
                      const rating = s.problem.rating!;
                      const range = Math.floor(rating / 100) * 100;
                      acc[range] = (acc[range] || 0) + 1;
                      return acc;
                    }, {} as Record<number, number>);

                  // Convert to chart data format
                  const chartData = Object.entries(ratingCounts)
                    .sort(([a], [b]) => parseInt(a) - parseInt(b))
                    .map(([range, count]) => ({
                      range: `${range}`,
                      count,
                      rating: parseInt(range),
                    }));

                  // Color function based on Codeforces rating colors
                  const getBarColor = (rating: number) => {
                    if (rating < 1200) return "#808080"; // Gray - Newbie
                    if (rating < 1400) return "#008000"; // Green - Pupil
                    if (rating < 1600) return "#03A89E"; // Cyan - Specialist
                    if (rating < 1900) return "#0000FF"; // Blue - Expert
                    if (rating < 2100) return "#AA00AA"; // Violet - Candidate Master
                    if (rating < 2300) return "#FF8C00"; // Orange - Master
                    if (rating < 2400) return "#FF8C00"; // Orange - International Master
                    if (rating < 2600) return "#FF0000"; // Red - Grandmaster
                    if (rating < 3000) return "#FF0000"; // Red - International Grandmaster
                    return "#AA0000"; // Dark Red - Legendary Grandmaster
                  };

                  return (
                    <div className="bg-gray-800/30 rounded-xl p-6 border border-gray-700">
                      <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                          data={chartData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                        >
                          <CartesianGrid
                            strokeDasharray="3 3"
                            stroke="#374151"
                          />
                          <XAxis
                            dataKey="range"
                            stroke="#9CA3AF"
                            fontSize={12}
                            angle={-45}
                            textAnchor="end"
                            height={80}
                          />
                          <YAxis stroke="#9CA3AF" fontSize={12} />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: "#1F2937",
                              border: "1px solid #374151",
                              borderRadius: "8px",
                              color: "#F3F4F6",
                            }}
                            formatter={(value: any) => [
                              value,
                              "Problems Solved",
                            ]}
                            labelFormatter={(label: any) =>
                              `Rating Range: ${label}`
                            }
                          />
                          <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                            {chartData.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={getBarColor(entry.rating)}
                              />
                            ))}
                          </Bar>
                        </BarChart>
                      </ResponsiveContainer>

                      {/* Legend */}
                      <div className="mt-4 text-center">
                        <p className="text-sm text-gray-400">
                          Colors represent Codeforces rating categories • Hover
                          over bars for details
                        </p>
                      </div>
                    </div>
                  );
                })()}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Loading state for all solved questions */}
      {allSolvedLoading && (
        <div className="mt-8">
          <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-lg">
            <div className="flex items-center gap-3 mb-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-400"></div>
              <span className="text-gray-300">
                Loading all solved questions statistics...
              </span>
            </div>
            <div className="animate-pulse">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className="bg-gray-800/50 border border-gray-600 rounded-lg p-4"
                  >
                    <div className="h-8 bg-gray-700 rounded w-1/2 mx-auto mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded w-3/4 mx-auto"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error state for all solved questions */}
      {allSolvedError && !allSolvedLoading && (
        <div className="mt-8">
          <div className="bg-red-950 border border-red-800 rounded-xl p-6 shadow-lg">
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-red-400 rounded-full"></div>
              <span className="text-red-300">
                Failed to load all solved questions: {allSolvedError.message}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Problem Filters and Display Section */}
      {/* Shows problems filtered by the selected date range from graph zoom */}
      {/* When no date range is selected, shows all problems */}
      {(problemsToDisplay.length > 0 || allSolvedProblems.length > 0) && (
        <div className="mt-8 space-y-6">
          {/* Problem Filters */}
          <ProblemFilters
            submissions={problemsToDisplay}
            onFilteredSubmissions={setFilteredSubmissions}
            isLoading={allSolvedLoading} // Show loading state while fetching all problems
          />

          {/* Problems Display - Shows problems filtered by graph date range and additional filters */}
          <ProblemsDisplay
            submissions={
              filteredSubmissions.length > 0
                ? filteredSubmissions
                : problemsToDisplay
            }
            isLoading={allSolvedLoading} // Show loading state while fetching all problems
            error={allSolvedError?.message || null} // Show any API errors
            dateRange={submissionsDateRange}
          />
        </div>
      )}

      {/* Info Modal */}
      <Dialog open={isInfoModalOpen} onOpenChange={setIsInfoModalOpen}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl">
              <span className="w-6 h-6 rounded-full bg-blue-600/20 border border-blue-500/30 flex items-center justify-center">
                <span className="text-blue-400 text-xs font-bold">i</span>
              </span>
              How to Use the Rating Graph
            </DialogTitle>
            <DialogDescription className="text-left space-y-4 mt-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <span className="text-lg">💡</span>
                  <div>
                    <p className="font-medium text-gray-200">
                      Hover over the rating graph
                    </p>
                    <p className="text-gray-400 text-sm">
                      See how many problems you solved on each day.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-lg">🖱️</span>
                  <div>
                    <p className="font-medium text-gray-200">
                      Left-click on any point
                    </p>
                    <p className="text-gray-400 text-sm">
                      Mark it as the start or end of your desired range.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-lg">🔍</span>
                  <div>
                    <p className="font-medium text-gray-200">
                      Zoom into selected range
                    </p>
                    <p className="text-gray-400 text-sm">
                      Once both points are selected, click "Zoom into selected
                      range" to view problems from that specific time period.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-lg">📋</span>
                  <div>
                    <p className="font-medium text-gray-200">
                      Explore and filter
                    </p>
                    <p className="text-gray-400 text-sm">
                      Scroll down to explore and filter the problems within the
                      chosen range.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-lg">📝</span>
                  <div>
                    <p className="font-medium text-gray-200">
                      Create custom sheet
                    </p>
                    <p className="text-gray-400 text-sm">
                      Create a custom sheet using the filtered problems for
                      focused practice.
                    </p>
                  </div>
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </div>
  );
};
