"use client";

import { CodeforcesRatingGraph } from "@/components/codeforces/CodeforcesRatingGraph";
import { useState } from "react";

export default function TestRatingGraphPage() {
  const [handle, setHandle] = useState("tourist");

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">
          Test Rating Graph - Info Button
        </h1>
        
        <div className="mb-6">
          <label className="block text-white mb-2">
            Codeforces Handle:
          </label>
          <input
            type="text"
            value={handle}
            onChange={(e) => setHandle(e.target.value)}
            className="px-4 py-2 bg-gray-800 text-white border border-gray-600 rounded-lg"
            placeholder="Enter Codeforces handle"
          />
        </div>

        {handle && (
          <CodeforcesRatingGraph
            handle={handle}
            height={450}
          />
        )}
      </div>
    </div>
  );
}
