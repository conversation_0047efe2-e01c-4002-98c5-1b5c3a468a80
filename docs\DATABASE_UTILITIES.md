# 🗄️ Database Utilities Documentation

## 📋 Overview

TraceStack includes a comprehensive set of database utilities built on top of Prisma ORM, following official best practices for production deployments. These utilities provide enhanced error handling, connection management, health monitoring, and performance optimization.

## 🏗️ Architecture

```
src/lib/database/
├── index.ts          # Main exports and utilities
├── prisma.ts         # Enhanced Prisma client configuration
├── errors.ts         # Error handling and retry logic
├── health.ts         # Health checks and monitoring
├── config.ts         # Environment configuration
└── types.ts          # TypeScript type definitions
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { prisma, safeDbOperation } from '@/lib/database';

// Safe database operation with automatic error handling
const result = await safeDbOperation(async () => {
  return await prisma.users.findFirst({
    where: { email: '<EMAIL>' },
  });
});

if (result.success) {
  console.log('User found:', result.data);
} else {
  console.error('Error:', result.error.userMessage);
}
```

### Health Checks

```typescript
import { dbUtils } from '@/lib/database';

// Initialize database with health check
await dbUtils.initialize();

// Get current database status
const status = await dbUtils.getStatus();

// Run comprehensive diagnostics
const diagnostics = await dbUtils.diagnose();
```

## 🔧 Core Features

### 1. Enhanced Prisma Client

The enhanced Prisma client provides:

- **Global Instance Management**: Prevents multiple connections in development
- **Environment-Specific Configuration**: Optimized settings for dev/prod
- **Connection Pooling**: Automatic optimization for serverless environments
- **Comprehensive Logging**: Configurable log levels

```typescript
// Automatically configured based on environment
import { prisma } from '@/lib/database';

// Production: connection_limit=1, error-only logging
// Development: comprehensive logging, hot-reload protection
```

### 2. Error Handling

Comprehensive error handling with user-friendly messages:

```typescript
import { handlePrismaError, withRetry } from '@/lib/database';

try {
  const user = await prisma.users.create(userData);
} catch (error) {
  const dbError = handlePrismaError(error);
  
  console.log('Error code:', dbError.code);
  console.log('User message:', dbError.userMessage);
  console.log('Is retryable:', dbError.isRetryable);
}

// Automatic retry for transient errors
const result = await withRetry(async () => {
  return await prisma.users.findMany();
}, 3, 1000); // 3 retries, 1 second base delay
```

### 3. Health Monitoring

Built-in health checks and monitoring:

```typescript
import { performHealthCheck, checkDatabaseConnection } from '@/lib/database';

// Quick connection check
const isConnected = await checkDatabaseConnection();

// Comprehensive health check
const health = await performHealthCheck();
console.log('Status:', health.status); // 'healthy' | 'degraded' | 'unhealthy'
console.log('Response time:', health.responseTime);
```

### 4. Configuration Management

Environment-specific configuration with validation:

```typescript
import { 
  validateDatabaseUrl, 
  generateOptimizedDatabaseUrl,
  runConfigurationDiagnostics 
} from '@/lib/database';

// Validate current configuration
const validation = validateDatabaseUrl(process.env.DATABASE_URL);

// Generate optimized URL for production
const optimizedUrl = generateOptimizedDatabaseUrl(
  process.env.DATABASE_URL,
  'production'
);

// Run configuration diagnostics
const diagnostics = runConfigurationDiagnostics();
```

## 🔌 API Integration

### Health Check Endpoints

The utilities provide ready-to-use API endpoints:

```typescript
// GET /api/health - Basic health check
// GET /api/health?detailed=true - Detailed health information
// GET /api/health?diagnostics=true - Full diagnostics
// POST /api/health - Initialize/warm up database

// Example response
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "database": {
    "connection": true,
    "queryExecution": true,
    "responseTime": 45,
    "schemaIntegrity": true
  }
}
```

### Enhanced API Routes

```typescript
import { safeDbOperation, withDatabase } from '@/lib/database';

// Wrap API routes with database middleware
export const GET = withDatabase(async (req, res) => {
  const result = await safeDbOperation(async () => {
    return await prisma.users.findMany();
  });
  
  if (!result.success) {
    return Response.json(
      { error: result.error.userMessage },
      { status: 500 }
    );
  }
  
  return Response.json(result.data);
});
```

## 🎯 Best Practices

### 1. Error Handling Pattern

```typescript
// Always use safeDbOperation for user-facing operations
const createUser = async (userData: CreateUserInput) => {
  const result = await safeDbOperation(async () => {
    return await prisma.users.create({ data: userData });
  }, 'createUser');
  
  if (!result.success) {
    // Log error details for debugging
    console.error('User creation failed:', result.error);
    
    // Return user-friendly message
    throw new Error(result.error.userMessage);
  }
  
  return result.data;
};
```

### 2. Connection Management

```typescript
// Initialize database on application startup
import { dbUtils } from '@/lib/database';

export default async function handler(req, res) {
  // Warm up database connection
  await dbUtils.initialize();
  
  // Your API logic here
}
```

### 3. Performance Optimization

```typescript
// Use efficient queries with proper selection
const getUser = async (email: string) => {
  return await safeDbOperation(async () => {
    return await prisma.users.findFirst({
      where: { email },
      select: {
        id: true,
        email: true,
        handle: true,
        _count: { select: { sheets: true } },
      },
    });
  });
};

// Implement pagination for large datasets
const getUsers = async (page: number = 1, limit: number = 20) => {
  return await safeDbOperation(async () => {
    return await prisma.users.findMany({
      take: limit,
      skip: (page - 1) * limit,
      orderBy: { createdAt: 'desc' },
    });
  });
};
```

## 🔍 Monitoring and Debugging

### Development Debugging

```typescript
// Enable comprehensive logging in development
// Set in .env.local:
// DATABASE_LOG_LEVEL="query"
// DATABASE_ENABLE_METRICS="true"

// View query logs in console
// Monitor connection pool usage
// Track performance metrics
```

### Production Monitoring

```typescript
// Set up health check monitoring
const monitorHealth = async () => {
  const health = await performHealthCheck();
  
  if (health.status !== 'healthy') {
    // Send alert to monitoring service
    console.error('Database health issue:', health);
  }
  
  // Log metrics for analysis
  console.log('Database metrics:', {
    status: health.status,
    responseTime: health.responseTime,
    timestamp: health.timestamp,
  });
};

// Run health checks periodically
setInterval(monitorHealth, 60000); // Every minute
```

## 🚨 Troubleshooting

### Common Issues and Solutions

1. **Connection Pool Exhaustion**
   ```typescript
   // Check connection pool status
   const status = await dbUtils.getStatus();
   console.log('Pool status:', status.connectionPool);
   
   // Solution: Optimize connection_limit
   DATABASE_URL="...?connection_limit=1"
   ```

2. **Slow Query Performance**
   ```typescript
   // Monitor query performance
   const health = await performHealthCheck();
   if (health.responseTime > 1000) {
     console.warn('Slow database response:', health.responseTime);
   }
   
   // Solution: Add database indexes, optimize queries
   ```

3. **SSL Connection Issues**
   ```typescript
   // Check SSL configuration
   const diagnostics = runConfigurationDiagnostics();
   console.log('SSL config:', diagnostics.currentConfig.ssl);
   
   // Solution: Update DATABASE_URL with SSL parameters
   ```

### Debug Commands

```bash
# Check database configuration
curl http://localhost:3000/api/health?diagnostics=true

# Test database connection
curl http://localhost:3000/api/health

# Initialize database
curl -X POST http://localhost:3000/api/health

# Check specific user
curl "http://localhost:3000/api/database?email=<EMAIL>"
```

## 📚 Type Definitions

The utilities include comprehensive TypeScript types:

```typescript
import type {
  DatabaseResult,
  UserWithSheets,
  DatabaseHealthCheck,
  DatabaseError,
  PaginatedResult,
} from '@/lib/database';

// Type-safe database operations
const result: DatabaseResult<UserWithSheets> = await safeDbOperation(
  async () => await prisma.users.findFirst({ include: { sheets: true } })
);
```

## 🔄 Migration Support

```typescript
// Safe migration execution
import { dbUtils } from '@/lib/database';

const runMigrations = async () => {
  try {
    // Check database health before migration
    const health = await performHealthCheck();
    if (health.status !== 'healthy') {
      throw new Error('Database not healthy for migration');
    }
    
    // Run migrations
    // This would typically be done via CLI: npx prisma migrate deploy
    
    // Verify schema integrity after migration
    const isValid = await validateSchemaIntegrity();
    if (!isValid) {
      throw new Error('Schema integrity check failed after migration');
    }
    
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
};
```

---

## 📞 Support

For issues or questions about the database utilities:

1. Check the [Production Guide](./PRISMA_PRODUCTION_GUIDE.md)
2. Review the [troubleshooting section](#-troubleshooting)
3. Use the health check endpoints for diagnostics
4. Create an issue in the repository with diagnostic output
