"use client";

import { BinarySearchAlgorithm, BinarySearchElement } from "@/lib/types";
import { AnimatePresence, motion } from "framer-motion";

interface ArrayDisplayProps {
  array: BinarySearchElement[];
  target: number;
  isSearching: boolean;
  selectedAlgorithm: BinarySearchAlgorithm;
}

const ArrayDisplay = ({
  array,
  target,
  isSearching,
  selectedAlgorithm,
}: ArrayDisplayProps) => {
  if (array.length === 0) return null;

  const getElementColor = (element: BinarySearchElement) => {
    if (element.isFound) return "#10b981"; // green-500 - final result
    if (element.isTarget) return "#10b981"; // green-500 - target found
    if (element.isMid) return "#f59e0b"; // amber-500 - current middle
    if (element.isComparing) return "#ef4444"; // red-500 - comparing
    if (element.isLeft) return "#8b5cf6"; // violet-500 - left boundary
    if (element.isRight) return "#ec4899"; // pink-500 - right boundary
    if (element.isInRange) return "#3b82f6"; // blue-500 - in search range
    return "#6b7280"; // gray-500 - out of range
  };

  const getBorderColor = (element: BinarySearchElement) => {
    if (element.isFound || element.isTarget) return "#059669"; // green-600
    if (element.isMid) return "#d97706"; // amber-600
    if (element.isComparing) return "#dc2626"; // red-600
    if (element.isLeft) return "#7c3aed"; // violet-600
    if (element.isRight) return "#db2777"; // pink-600
    if (element.isInRange) return "#2563eb"; // blue-600
    return "#4b5563"; // gray-600
  };

  const getAlgorithmDescription = () => {
    switch (selectedAlgorithm) {
      case "standard":
        return "Standard Binary Search - Find exact target value";
      case "lowerBound":
        return "Lower Bound - Find first position where value ≥ target";
      case "upperBound":
        return "Upper Bound - Find first position where value > target";
      default:
        return "";
    }
  };

  return (
    <div className="mt-8 w-full">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-white mb-2">
          Binary Search Visualization
        </h3>
        <p className="text-sm text-gray-400 mb-2">
          {getAlgorithmDescription()}
        </p>
        <p className="text-sm text-blue-300">
          Target: <span className="font-mono font-bold">{target}</span>
        </p>
      </div>

      <div className="flex flex-wrap justify-center gap-4 p-8 bg-gray-800/30 rounded-xl border border-gray-700/30">
        <AnimatePresence mode="wait">
          {array.map((element, index) => (
            <motion.div
              key={element.id}
              layout
              initial={{ opacity: 0, scale: 0.8, y: -20 }}
              animate={{
                opacity: 1,
                scale: element.isMid ? 1.1 : 1,
                y: 0,
                backgroundColor: getElementColor(element),
                borderColor: getBorderColor(element),
                boxShadow: element.isMid
                  ? "0 0 20px rgba(245, 158, 11, 0.5)"
                  : element.isFound || element.isTarget
                  ? "0 0 20px rgba(16, 185, 129, 0.5)"
                  : element.isComparing
                  ? "0 0 20px rgba(239, 68, 68, 0.5)"
                  : "none",
              }}
              transition={{
                duration: 0.3,
                ease: "easeInOut",
              }}
              className="relative flex flex-col items-center justify-center w-20 h-20 rounded-lg border-2 font-bold text-white shadow-lg cursor-pointer hover:scale-105"
              whileHover={!isSearching ? { scale: 1.1 } : {}}
              whileTap={!isSearching ? { scale: 0.95 } : {}}
            >
              <span className="text-xl font-mono">{element.value}</span>

              {/* Index indicator */}
              <motion.div
                className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 font-mono"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                [{element.index}]
              </motion.div>

              {/* Status message */}
              {element.message && (
                <motion.div
                  className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs font-semibold px-2 py-1 rounded bg-gray-900/80 border border-gray-600/50 whitespace-nowrap"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  style={{
                    color: element.isFound || element.isTarget
                      ? "#10b981"
                      : element.isMid
                      ? "#f59e0b"
                      : element.isComparing
                      ? "#ef4444"
                      : element.isLeft
                      ? "#8b5cf6"
                      : element.isRight
                      ? "#ec4899"
                      : "#ffffff",
                  }}
                >
                  {element.message}
                </motion.div>
              )}

              {/* Special indicators */}
              {element.isLeft && (
                <motion.div
                  className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-violet-500 rounded-full"
                  initial={{ scaleY: 0 }}
                  animate={{ scaleY: 1 }}
                  transition={{ delay: 0.2 }}
                />
              )}
              {element.isRight && (
                <motion.div
                  className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-pink-500 rounded-full"
                  initial={{ scaleY: 0 }}
                  animate={{ scaleY: 1 }}
                  transition={{ delay: 0.2 }}
                />
              )}
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Legend */}
      <div className="mt-6 flex flex-wrap justify-center gap-4 text-xs">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-violet-500 rounded border border-violet-600"></div>
          <span className="text-gray-300">Left Boundary</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-pink-500 rounded border border-pink-600"></div>
          <span className="text-gray-300">Right Boundary</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-amber-500 rounded border border-amber-600"></div>
          <span className="text-gray-300">Middle Element</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-500 rounded border border-red-600"></div>
          <span className="text-gray-300">Comparing</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-green-500 rounded border border-green-600"></div>
          <span className="text-gray-300">Result</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-blue-500 rounded border border-blue-600"></div>
          <span className="text-gray-300">In Range</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-500 rounded border border-gray-600"></div>
          <span className="text-gray-300">Out of Range</span>
        </div>
      </div>
    </div>
  );
};

export default ArrayDisplay;
