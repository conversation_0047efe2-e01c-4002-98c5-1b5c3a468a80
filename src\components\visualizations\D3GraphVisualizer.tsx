"use client";

import * as d3 from "d3";
import React, { useC<PERSON>back, useEffect, useRef, useState } from "react";

// TypeScript interfaces for graph data structures
export interface GraphNode extends d3.SimulationNodeDatum {
  id: string;
  label: string;
  group?: number;
  radius?: number;
  color?: string;
  // D3 simulation properties (added by d3-force)
  x?: number;
  y?: number;
  vx?: number;
  vy?: number;
  fx?: number | null;
  fy?: number | null;
}

export interface GraphLink extends d3.SimulationLinkDatum<GraphNode> {
  id: string;
  source: string | GraphNode;
  target: string | GraphNode;
  value?: number;
  color?: string;
}

export interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

export interface TooltipData {
  type: "node" | "link";
  data: GraphNode | GraphLink;
  x: number;
  y: number;
}

// Props interface for the D3GraphVisualizer component
interface D3GraphVisualizerProps {
  data: GraphData;
  width?: number;
  height?: number;
  onNodeClick?: (node: GraphNode) => void;
  onLinkClick?: (link: GraphLink) => void;
  className?: string;
}

/**
 * D3GraphVisualizer Component
 *
 * An interactive graph visualizer built with D3.js featuring:
 * - Force-directed layout using d3-force
 * - Interactive node dragging and selection
 * - Zoom and pan capabilities
 * - Hover effects and tooltips
 * - Smooth transitions and animations
 */
const D3GraphVisualizer: React.FC<D3GraphVisualizerProps> = ({
  data,
  width = 800,
  height = 600,
  onNodeClick,
  onLinkClick,
  className = "",
}) => {
  // Refs for D3 elements
  const svgRef = useRef<SVGSVGElement>(null);
  const simulationRef = useRef<d3.Simulation<GraphNode, GraphLink> | null>(
    null
  );

  // State for interactions
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [tooltip, setTooltip] = useState<TooltipData | null>(null);
  const [isSimulationRunning, setIsSimulationRunning] = useState(false);

  /**
   * Initialize the D3 force simulation
   * Sets up forces for links, collision detection, and centering
   */
  const initializeSimulation = useCallback(() => {
    if (!data.nodes.length) return null;

    const simulation = d3
      .forceSimulation<GraphNode>(data.nodes)
      .force(
        "link",
        d3
          .forceLink<GraphNode, GraphLink>(data.links)
          .id((d) => d.id)
          .distance(100)
          .strength(0.1)
      )
      .force("charge", d3.forceManyBody().strength(-300).distanceMax(400))
      .force("center", d3.forceCenter(width / 2, height / 2))
      .force(
        "collision",
        d3
          .forceCollide<GraphNode>()
          .radius((d) => (d.radius || 20) + 2)
          .strength(0.5)
      );

    return simulation;
  }, [data, width, height]);

  /**
   * Handle node drag behavior
   * Implements smooth dragging with simulation integration
   */
  const handleDrag = useCallback(() => {
    const dragstarted = (
      event: d3.D3DragEvent<SVGCircleElement, GraphNode, unknown>,
      d: GraphNode
    ) => {
      if (!event.active && simulationRef.current) {
        simulationRef.current.alphaTarget(0.3).restart();
      }
      d.fx = d.x;
      d.fy = d.y;
      setIsSimulationRunning(true);
    };

    const dragged = (
      event: d3.D3DragEvent<SVGCircleElement, GraphNode, unknown>,
      d: GraphNode
    ) => {
      d.fx = event.x;
      d.fy = event.y;
    };

    const dragended = (
      event: d3.D3DragEvent<SVGCircleElement, GraphNode, unknown>,
      d: GraphNode
    ) => {
      if (!event.active && simulationRef.current) {
        simulationRef.current.alphaTarget(0);
      }
      d.fx = null;
      d.fy = null;
      setIsSimulationRunning(false);
    };

    return d3
      .drag<SVGCircleElement, GraphNode>()
      .on("start", dragstarted)
      .on("drag", dragged)
      .on("end", dragended);
  }, []);

  /**
   * Handle zoom and pan behavior
   * Provides smooth zooming with limits and pan boundaries
   */
  const handleZoom = useCallback(() => {
    const zoomed = (event: d3.D3ZoomEvent<SVGSVGElement, unknown>) => {
      const { transform } = event;
      d3.select(svgRef.current)
        .select(".graph-container")
        .attr("transform", transform.toString());
    };

    return d3
      .zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 4])
      .on("zoom", zoomed);
  }, []);

  /**
   * Show tooltip on hover
   */
  const showTooltip = useCallback(
    (type: "node" | "link", data: GraphNode | GraphLink, event: MouseEvent) => {
      setTooltip({
        type,
        data,
        x: event.clientX,
        y: event.clientY,
      });
    },
    []
  );

  /**
   * Hide tooltip
   */
  const hideTooltip = useCallback(() => {
    setTooltip(null);
  }, []);

  // Main effect for setting up the D3 visualization
  useEffect(() => {
    if (!svgRef.current || !data.nodes.length) return;

    const svg = d3.select(svgRef.current);

    // Clear previous content
    svg.selectAll("*").remove();

    // Setup main container group for zoom/pan
    const container = svg.append("g").attr("class", "graph-container");

    // Initialize simulation
    const simulation = initializeSimulation();
    if (!simulation) return;

    simulationRef.current = simulation;

    // Create links (edges)
    const link = container
      .append("g")
      .attr("class", "links")
      .selectAll("line")
      .data(data.links)
      .enter()
      .append("line")
      .attr("class", "link")
      .attr("stroke", (d) => d.color || "#999")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", (d) => Math.sqrt(d.value || 1) * 2)
      .style("cursor", "pointer")
      .on("mouseover", function (event, d) {
        d3.select(this).attr("stroke-opacity", 1);
        showTooltip("link", d, event);
      })
      .on("mouseout", function (event, d) {
        d3.select(this).attr("stroke-opacity", 0.6);
        hideTooltip();
      })
      .on("click", function (event, d) {
        event.stopPropagation();
        onLinkClick?.(d);
      });

    // Create nodes
    const node = container
      .append("g")
      .attr("class", "nodes")
      .selectAll("circle")
      .data(data.nodes)
      .enter()
      .append("circle")
      .attr("class", "node")
      .attr("r", (d) => d.radius || 20)
      .attr("fill", (d) => d.color || "#69b3a2")
      .attr("stroke", "#fff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .call(handleDrag())
      .on("mouseover", function (event, d) {
        // Node shaking effect on hover
        d3.select(this)
          .transition()
          .duration(100)
          .attr("r", (d.radius || 20) * 1.2)
          .attr("stroke-width", 4);

        showTooltip("node", d, event);
      })
      .on("mouseout", function (event, d) {
        d3.select(this)
          .transition()
          .duration(200)
          .attr("r", d.radius || 20)
          .attr("stroke-width", 2);

        hideTooltip();
      })
      .on("click", function (event, d) {
        event.stopPropagation();
        setSelectedNode(d);
        onNodeClick?.(d);

        // Selection highlight effect
        node.attr("stroke", "#fff").attr("stroke-width", 2);
        d3.select(this).attr("stroke", "#ff6b6b").attr("stroke-width", 4);
      });

    // Add node labels
    const labels = container
      .append("g")
      .attr("class", "labels")
      .selectAll("text")
      .data(data.nodes)
      .enter()
      .append("text")
      .attr("class", "label")
      .attr("text-anchor", "middle")
      .attr("dy", ".35em")
      .attr("font-family", "Arial, sans-serif")
      .attr("font-size", "12px")
      .attr("fill", "#fff")
      .attr("pointer-events", "none")
      .text((d) => d.label);

    // Setup zoom behavior
    svg.call(handleZoom());

    // Simulation tick function - updates positions
    const ticked = () => {
      link
        .attr("x1", (d) => (d.source as GraphNode).x || 0)
        .attr("y1", (d) => (d.source as GraphNode).y || 0)
        .attr("x2", (d) => (d.target as GraphNode).x || 0)
        .attr("y2", (d) => (d.target as GraphNode).y || 0);

      node.attr("cx", (d) => d.x || 0).attr("cy", (d) => d.y || 0);

      labels.attr("x", (d) => d.x || 0).attr("y", (d) => d.y || 0);
    };

    // Start simulation
    simulation.on("tick", ticked);
    simulation.on("end", () => setIsSimulationRunning(false));
    setIsSimulationRunning(true);

    // Cleanup function
    return () => {
      simulation.stop();
      setIsSimulationRunning(false);
    };
  }, [
    data,
    width,
    height,
    initializeSimulation,
    handleDrag,
    handleZoom,
    showTooltip,
    hideTooltip,
    onNodeClick,
    onLinkClick,
  ]);

  return (
    <div className={`relative ${className}`}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        className="bg-black w-full h-full"
      />

      {/* Tooltip */}
      {tooltip && (
        <div
          className="absolute z-10 bg-gray-800 text-white p-2 rounded shadow-lg pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
          }}
        >
          {tooltip.type === "node" ? (
            <div>
              <div className="font-semibold">
                {(tooltip.data as GraphNode).label}
              </div>
              <div className="text-sm text-gray-300">ID: {tooltip.data.id}</div>
            </div>
          ) : (
            <div>
              <div className="font-semibold">Link</div>
              <div className="text-sm text-gray-300">
                {(() => {
                  const linkData = tooltip.data as GraphLink;
                  const sourceId =
                    typeof linkData.source === "string"
                      ? linkData.source
                      : (linkData.source as GraphNode).id;
                  const targetId =
                    typeof linkData.target === "string"
                      ? linkData.target
                      : (linkData.target as GraphNode).id;
                  return `${sourceId} → ${targetId}`;
                })()}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Simulation status indicator - Hidden as requested */}
      {/* {isSimulationRunning && (
        <div className="absolute top-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-sm">
          Simulation Running...
        </div>
      )} */}
    </div>
  );
};

export default D3GraphVisualizer;
