// Types for the sorting visualizer
export interface ArrayElement {
  value: number;
  id: string;
  isComparing?: boolean;
  isSwapping?: boolean;
  isSorted?: boolean;
  isMinimum?: boolean;
  isCurrent?: boolean;
  isPivot?: boolean;
  message?: string;
}

export type SortingAlgorithm = "bubble" | "selection" | "insertion" | "merge";

export interface SortingState {
  array: ArrayElement[];
  isSorting: boolean;
  isComplete: boolean;
  selectedAlgorithm: SortingAlgorithm;
  speed: number;
}

export type SortingGenerator = Generator<ArrayElement[], void, void>;

// Types for merge sort tree visualization
export interface MergeTreeNode {
  id: string;
  array: ArrayElement[];
  depth: number;
  position: number; // Position at this depth level (0-based)
  parentId?: string;
  leftChildId?: string;
  rightChildId?: string;
  state: "dividing" | "divided" | "merging" | "merged" | "complete";
  isActive?: boolean; // Currently being processed
  isBeingCompared?: boolean; // Node is being compared during merge
  comparisonText?: string; // Text to display for comparisons
  x?: number; // Calculated position for rendering
  y?: number; // Calculated position for rendering
}

export interface MergeTreeState {
  nodes: Map<string, MergeTreeNode>;
  currentStep: number;
  maxDepth: number;
  isComplete: boolean;
}

export interface MergeTreeStep {
  type: "divide" | "merge" | "complete";
  nodeId: string;
  leftChildId?: string;
  rightChildId?: string;
  mergedArray?: ArrayElement[];
  description: string;
}

export type MergeTreeGenerator = Generator<MergeTreeState, void, void>;

// Types for binary search visualizer
export interface BinarySearchElement {
  value: number;
  id: string;
  index: number;
  isLeft?: boolean; // Left boundary of search range
  isRight?: boolean; // Right boundary of search range
  isMid?: boolean; // Current middle element
  isTarget?: boolean; // Target element (if found)
  isComparing?: boolean; // Currently being compared
  isInRange?: boolean; // Within current search range
  isFound?: boolean; // Final result element
  message?: string; // Status message for this element
}

export type BinarySearchAlgorithm = "standard" | "lowerBound" | "upperBound";

export interface BinarySearchState {
  array: BinarySearchElement[];
  target: number;
  isSearching: boolean;
  isComplete: boolean;
  selectedAlgorithm: BinarySearchAlgorithm;
  speed: number;
  result: {
    found: boolean;
    index: number;
    message: string;
  } | null;
  currentStep: {
    left: number;
    right: number;
    mid: number;
    comparison: string;
    description: string;
  } | null;
}

export type BinarySearchGenerator = Generator<
  BinarySearchElement[],
  void,
  void
>;
