"use client";

import { useEffect, useState } from "react";

export default function TabSuccessPage() {
  const [countdown, setCountdown] = useState(3);
  const [autoCloseBlocked, setAutoCloseBlocked] = useState(false);
  const [showManualClose, setShowManualClose] = useState(false);

  const attemptWindowClose = () => {
    try {
      // Store the current window state
      const wasWindowClosed = window.closed;

      // Attempt to close the window
      window.close();

      // Check if the window actually closed after a short delay
      setTimeout(() => {
        if (!window.closed) {
          // Window close was blocked
          setAutoCloseBlocked(true);
          setShowManualClose(true);

          // Try to ask parent window to close this tab
          if (window.opener && !window.opener.closed) {
            window.opener.postMessage(
              {
                type: "CLOSE_AUTH_TAB",
                timestamp: Date.now(),
              },
              window.location.origin
            );
          }
        }
      }, 100);
    } catch (error) {
      console.error("Error attempting to close window:", error);
      setAutoCloseBlocked(true);
      setShowManualClose(true);
    }
  };

  useEffect(() => {
    // Notify the parent window that authentication was successful
    try {
      // Try to communicate with the parent window if it exists
      if (window.opener && !window.opener.closed) {
        // Send a message to the parent window
        window.opener.postMessage(
          {
            type: "AUTH_SUCCESS",
            timestamp: Date.now(),
          },
          window.location.origin
        );
      }

      // Also try localStorage approach for cross-tab communication
      localStorage.setItem("auth_success", Date.now().toString());

      // Remove the flag after a short delay
      setTimeout(() => {
        localStorage.removeItem("auth_success");
      }, 5000);
    } catch (error) {
      console.error("Error communicating authentication success:", error);
    }

    // Start countdown and close the tab
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // Attempt to close the tab with detection
          attemptWindowClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Fallback: try to close after 5 seconds even if countdown doesn't work
    const fallbackTimer = setTimeout(() => {
      attemptWindowClose();
    }, 5000);

    // Show manual close option after 3 seconds regardless
    const manualCloseTimer = setTimeout(() => {
      setShowManualClose(true);
    }, 3000);

    return () => {
      clearInterval(timer);
      clearTimeout(fallbackTimer);
      clearTimeout(manualCloseTimer);
    };
  }, []);

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <div className="text-center max-w-md">
        {/* Success Icon */}
        <div className="rounded-full h-16 w-16 bg-green-500 flex items-center justify-center mx-auto mb-6">
          <svg
            className="w-8 h-8 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>

        {/* Success Message */}
        <h1 className="text-2xl font-bold text-green-400 mb-4">
          Authentication Successful!
        </h1>

        <p className="text-gray-400 mb-6">
          You have been successfully logged in. You can now return to the main
          application.
        </p>

        {/* Countdown or Status */}
        {countdown > 0 && !autoCloseBlocked ? (
          <div className="bg-gray-900 rounded-lg p-4 mb-6">
            <p className="text-sm text-gray-300 mb-2">
              This tab will close automatically in:
            </p>
            <div className="text-3xl font-bold text-white">{countdown}</div>
          </div>
        ) : autoCloseBlocked ? (
          <div className="bg-yellow-900/50 border border-yellow-600 rounded-lg p-4 mb-6">
            <p className="text-sm text-yellow-300 mb-2">
              ⚠️ Auto-close was blocked by your browser
            </p>
            <p className="text-xs text-yellow-400">
              Please use the button below to close this tab manually.
            </p>
          </div>
        ) : (
          <div className="bg-green-900/50 border border-green-600 rounded-lg p-4 mb-6">
            <p className="text-sm text-green-300">
              ✓ Attempting to close tab...
            </p>
          </div>
        )}

        {/* Manual Close Button - Always visible but more prominent when needed */}
        <button
          onClick={attemptWindowClose}
          className={`px-6 py-2 rounded-lg transition-all duration-200 ${
            autoCloseBlocked || showManualClose
              ? "bg-red-600 hover:bg-red-700 text-white font-semibold animate-pulse"
              : "bg-blue-600 hover:bg-blue-700 text-white"
          }`}
        >
          {autoCloseBlocked ? "Close Tab Manually" : "Close Tab Now"}
        </button>

        <p className="text-xs text-gray-500 mt-4">
          {autoCloseBlocked
            ? "Your browser blocked automatic tab closing. Please click the button above."
            : "If the tab doesn't close automatically, you can close it manually."}
        </p>
      </div>
    </div>
  );
}
