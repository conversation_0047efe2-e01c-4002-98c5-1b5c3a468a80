"use client";

import { BinarySearchAlgorithm } from "@/lib/types";
import { Pause, Play, RotateCcw, Square } from "lucide-react";

interface ControlsProps {
  inputValue: string;
  targetValue: string;
  selectedAlgorithm: BinarySearchAlgorithm;
  speed: number;
  isSearching: boolean;
  isPaused: boolean;
  error: string | null;
  onInputChange: (value: string) => void;
  onTargetChange: (value: string) => void;
  onAlgorithmChange: (algorithm: BinarySearchAlgorithm) => void;
  onSpeedChange: (speed: number) => void;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onReset: () => void;
  onErrorDismiss: () => void;
}

const Controls = ({
  inputValue,
  targetValue,
  selectedAlgorithm,
  speed,
  isSearching,
  isPaused,
  error,
  onInputChange,
  onTargetChange,
  onAlgorithmChange,
  onSpeedChange,
  onStart,
  onPause,
  onStop,
  onReset,
  onErrorDismiss,
}: ControlsProps) => {
  const algorithms: { value: BinarySearchAlgorithm; label: string; description: string }[] = [
    {
      value: "standard",
      label: "Standard Binary Search",
      description: "Find exact target value",
    },
    {
      value: "lowerBound",
      label: "Lower Bound",
      description: "First position ≥ target",
    },
    {
      value: "upperBound",
      label: "Upper Bound",
      description: "First position > target",
    },
  ];

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-700/50 p-6">
      {/* Error Display */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700/50 rounded-lg flex items-center justify-between">
          <span className="text-red-300 text-sm">{error}</span>
          <button
            onClick={onErrorDismiss}
            className="text-red-400 hover:text-red-300 ml-2"
          >
            ×
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Controls */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Sorted Array (comma-separated)
            </label>
            <input
              type="text"
              value={inputValue}
              onChange={(e) => onInputChange(e.target.value)}
              disabled={isSearching}
              placeholder="1,3,5,7,9,11,13,15,17,19"
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            />
            <p className="text-xs text-gray-400 mt-1">
              Enter up to 15 numbers in ascending order
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Target Value
            </label>
            <input
              type="text"
              value={targetValue}
              onChange={(e) => onTargetChange(e.target.value)}
              disabled={isSearching}
              placeholder="7"
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            />
            <p className="text-xs text-gray-400 mt-1">
              The value to search for
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Algorithm
            </label>
            <select
              value={selectedAlgorithm}
              onChange={(e) => onAlgorithmChange(e.target.value as BinarySearchAlgorithm)}
              disabled={isSearching}
              className="w-full px-3 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {algorithms.map((algo) => (
                <option key={algo.value} value={algo.value}>
                  {algo.label}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-400 mt-1">
              {algorithms.find((a) => a.value === selectedAlgorithm)?.description}
            </p>
          </div>
        </div>

        {/* Control Buttons and Speed */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Speed: {speed}x
            </label>
            <input
              type="range"
              min="0.5"
              max="3"
              step="0.5"
              value={speed}
              onChange={(e) => onSpeedChange(parseFloat(e.target.value))}
              disabled={isSearching}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>0.5x</span>
              <span>1.5x</span>
              <span>3x</span>
            </div>
          </div>

          <div className="flex flex-wrap gap-3">
            <button
              onClick={onStart}
              disabled={isSearching && !isPaused}
              className="flex items-center gap-2 px-4 py-2 bg-green-600/80 hover:bg-green-600 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 font-medium"
            >
              <Play size={16} />
              Start
            </button>

            <button
              onClick={onPause}
              disabled={!isSearching}
              className="flex items-center gap-2 px-4 py-2 bg-yellow-600/80 hover:bg-yellow-600 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 font-medium"
            >
              <Pause size={16} />
              {isPaused ? "Resume" : "Pause"}
            </button>

            <button
              onClick={onStop}
              disabled={!isSearching}
              className="flex items-center gap-2 px-4 py-2 bg-red-600/80 hover:bg-red-600 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 font-medium"
            >
              <Square size={16} />
              Stop
            </button>

            <button
              onClick={onReset}
              disabled={isSearching}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600/80 hover:bg-blue-600 disabled:bg-gray-600/50 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 font-medium"
            >
              <RotateCcw size={16} />
              Reset
            </button>
          </div>

          {/* Algorithm Information */}
          <div className="bg-gray-800/30 rounded-lg p-4 border border-gray-700/30">
            <h4 className="text-sm font-semibold text-white mb-2">
              {algorithms.find((a) => a.value === selectedAlgorithm)?.label}
            </h4>
            <p className="text-xs text-gray-400 leading-relaxed">
              {selectedAlgorithm === "standard" && 
                "Searches for an exact match of the target value. Returns the index if found, or indicates not found."}
              {selectedAlgorithm === "lowerBound" && 
                "Finds the first position where the value is greater than or equal to the target. Useful for insertion points."}
              {selectedAlgorithm === "upperBound" && 
                "Finds the first position where the value is strictly greater than the target. Useful for range queries."}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Controls;
