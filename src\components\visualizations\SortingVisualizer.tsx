"use client";

import { useSorting } from "@/hooks/useSorting";
import { SortingAlgorithm } from "@/lib/types";
import ArrayDisplay from "./sorting/ArrayDisplay";
import Controls from "./sorting/Controls";
import MergeSortTreeVisualizer from "./sorting/MergeSortTreeVisualizer";
import StatusDisplay from "./sorting/StatusDisplay";

const SortingVisualizer = () => {
  const {
    array,
    isSorting,
    isPaused,
    isComplete,
    selectedAlgorithm,
    speed,
    inputValue,
    treeState,
    error,
    setInputValue,
    setSelectedAlgorithm,
    setSpeed,
    startVisualization,
    pauseVisualization,
    stopVisualization,
    resetVisualization,
    setError,
  } = useSorting();

  return (
    <div className="flex flex-col w-full max-w-7xl mx-auto p-4 sm:p-6 lg:p-8 bg-gradient-to-br from-slate-900 via-blue-950/20 to-slate-900 rounded-3xl border border-slate-800 shadow-2xl shadow-blue-500/10 min-h-[85vh] text-white">
      <header className="text-center mb-6">
        <p className="mt-3 text-base text-gray-400 max-w-2xl mx-auto">
          Enter numbers separated by commas, or generate a random array, and
          watch how different sorting algorithms work their magic.
        </p>
      </header>

      <div className="flex flex-col flex-grow gap-6 mt-4">
        <Controls
          inputValue={inputValue}
          onInputChange={(value) => {
            setInputValue(value);
            if (error) setError(null);
          }}
          selectedAlgorithm={selectedAlgorithm}
          onAlgorithmChange={(algo) =>
            setSelectedAlgorithm(algo as SortingAlgorithm)
          }
          speed={speed}
          onSpeedChange={setSpeed}
          onStart={startVisualization}
          onPause={pauseVisualization}
          onStop={stopVisualization}
          onReset={resetVisualization}
          isSorting={isSorting}
          isPaused={isPaused}
          error={error}
        />

        <StatusDisplay isSorting={isSorting} isComplete={isComplete} />

        <main className="flex flex-col flex-grow items-center justify-center p-4 bg-black/40 rounded-xl border border-slate-800 min-h-[400px] scrollable-content">
          {selectedAlgorithm === "merge" ? (
            <MergeSortTreeVisualizer
              treeState={treeState}
              isSorting={isSorting}
            />
          ) : (
            <ArrayDisplay
              array={array}
              isSorting={isSorting}
              selectedAlgorithm={selectedAlgorithm}
            />
          )}
        </main>
      </div>
    </div>
  );
};

export default SortingVisualizer;
