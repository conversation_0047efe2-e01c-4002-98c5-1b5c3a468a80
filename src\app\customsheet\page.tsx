"use client";

import { TwoPartModal } from "@/components/ui/reusable-modal";
import { CodeforcesSubmission } from "@/lib/codeforces";
import { generateProblemId, isProblemCompleted } from "@/lib/codingSheet";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { Gift, Loader2, Shield } from "lucide-react";
import { useRouter } from "next/navigation";
import Script from "next/script";
import React from "react";

// ============================================================================
// BACKGROUND COMPONENT - STABLE AND MEMOIZED
// ============================================================================
/**
 * Static background component with visual effects
 * - Creates a dark themed background with blue accents
 * - Uses gradient overlays, grid patterns, and geometric shapes
 * - Memoized to prevent unnecessary re-renders during state changes
 */
const StableBackground = React.memo(() => (
  <>
    {/* Static gradient overlays - creates depth with radial gradients */}
    <div className="absolute inset-0 bg-gradient-radial from-blue-950/60 via-transparent to-slate-950/70" />

    {/* Static grid pattern - subtle dotted background */}
    <div
      className="absolute inset-0 bg-grid-white/[0.03]"
      style={{ backgroundSize: "40px 40px" }}
    />

    {/* Static geometric patterns - decorative lines and accents */}
    <div className="absolute inset-0 pointer-events-none">
      {/* Diagonal lines for visual interest */}
      <div className="absolute top-1/4 left-0 w-1/3 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent" />
      <div className="absolute top-3/4 right-0 w-1/4 h-px bg-gradient-to-l from-transparent via-blue-400/15 to-transparent" />
      <div className="absolute left-1/4 top-0 w-px h-1/2 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent" />
      <div className="absolute right-1/3 top-1/3 w-px h-1/3 bg-gradient-to-b from-transparent via-blue-400/15 to-transparent" />
    </div>

    {/* Subtle corner accents - glowing effects in corners */}
    <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl" />
    <div className="absolute bottom-0 right-0 w-96 h-96 bg-slate-500/5 rounded-full blur-3xl" />
  </>
));

StableBackground.displayName = "StableBackground";

// ============================================================================
// REACT QUERY HOOKS FOR API CALLS
// ============================================================================

/**
 * Hook to fetch all user's custom sheets
 */
const useCustomSheets = () => {
  return useQuery({
    queryKey: ["customSheets"],
    queryFn: async () => {
      const response = await axios.get("/api/sheetInfo/customSheet");
      return response.data;
    },
    staleTime: 0, // Always consider data stale to ensure fresh fetches after sheet creation
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: true,
    refetchOnMount: true, // Always refetch when component mounts
    retry: (failureCount, error: any) => {
      // Don't retry 4xx errors (client errors)
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 1;
    },
  });
};

/**
 * Hook to check Codeforces verification status
 */
const useVerificationStatus = (enabled: boolean = true) => {
  return useQuery({
    queryKey: ["verificationStatus"],
    queryFn: async () => {
      const response = await axios.get("/api/codeforces/verifyHandle");
      return { verified: response.status === 200 };
    },
    enabled,
    staleTime: 0, // Consider data fresh for 0 minutes
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false,
    retry: (failureCount, error: any) => {
      // Don't retry 404 errors (user not verified)
      if (error?.response?.status === 404) {
        return false;
      }
      return failureCount < 1;
    },
  });
};

/**
 * Mutation hook for creating Razorpay orders
 */
const useCreateOrder = () => {
  return useMutation({
    mutationFn: async (amount: number) => {
      const response = await axios.post("/api/razorpay/createOrder", {
        amount: amount * 100, // Convert rupees to paise
      });
      return response.data;
    },
  });
};

/**
 * Mutation hook for verifying Razorpay payments
 */
const useVerifyPayment = () => {
  return useMutation({
    mutationFn: async (paymentData: {
      orderId: string;
      razorpayPaymentId: string;
      razorpaySignature: string;
    }) => {
      const response = await axios.post(
        "/api/razorpay/verifyOrder",
        paymentData
      );
      return response.data;
    },
  });
};

// ============================================================================
// CUSTOM CODING SHEET PAGE - MAIN COMPONENT
// ============================================================================
/**
 * MAIN PAGE FUNCTIONALITY:
 * 1. Displays user's coding sheets (problems organized by rating)
 * 2. Allows switching between multiple sheets
 * 3. Provides filtering options (all/completed/incomplete problems)
 * 4. Handles payment for additional sheet slots
 * 5. Integrates with sheet management components
 *
 * DATA FLOW:
 * API -> Transform Data -> Display Sheets -> User Interactions -> Update State
 */
const CustomSheetPage = () => {
  // ============================================================================
  // NAVIGATION AND ROUTING
  // ============================================================================
  const router = useRouter(); // For navigating between pages
  const queryClient = useQueryClient(); // For invalidating queries

  // ============================================================================
  // REACT QUERY HOOKS
  // ============================================================================
  /**
   * Fetch all user's custom sheets using React Query
   */
  const {
    data: sheetsResponse,
    isLoading,
    error: sheetsError,
    refetch: refetchSheets,
  } = useCustomSheets();

  /**
   * Check verification status (only when needed)
   */
  const {
    data: verificationData,
    isLoading: isCheckingVerification,
    refetch: checkVerificationStatus,
  } = useVerificationStatus(false); // Disabled by default

  /**
   * Mutation hooks for payment processing
   */
  const createOrderMutation = useCreateOrder();
  const verifyPaymentMutation = useVerifyPayment();

  // ============================================================================
  // STATE MANAGEMENT - SHEET DATA
  // ============================================================================
  /**
   * Core sheet data states:
   * - selectedSheetIndex: Which sheet is currently being viewed
   * - sheetData: Transformed data for the currently selected sheet
   */
  const [selectedSheetIndex, setSelectedSheetIndex] = React.useState<number>(0);
  const [sheetData, setSheetData] = React.useState<any>(null);

  /**
   * Extract data from React Query response
   */
  const allSheets = sheetsResponse?.sheets || [];
  const maxSheetSlots = sheetsResponse?.maxSheetSlots || 1;
  const isAllowedToCreateMore = sheetsResponse?.isAllowedToCreateMore || true;
  const error = sheetsError ? "Failed to load sheet information." : null;

  // ============================================================================
  // STATE MANAGEMENT - FILTERING
  // ============================================================================
  /**
   * Problem filtering functionality:
   * - filter: Current filter type (all/completed/incomplete)
   * - filteredProblems: Processed list based on current filter
   * - filterUpdateTrigger: Forces re-filtering when localStorage changes
   */
  const [filter, setFilter] = React.useState<
    "all" | "completed" | "incomplete"
  >("all");
  const [filteredProblems, setFilteredProblems] = React.useState<
    CodeforcesSubmission[]
  >([]);
  const [filterUpdateTrigger, setFilterUpdateTrigger] = React.useState(0);

  // ============================================================================
  // STATE MANAGEMENT - UI STATES
  // ============================================================================
  /**
   * UI states for payment and verification flows:
   * - isProcessingPayment: Payment flow state
   * - isVerificationModalOpen: Whether verification modal is visible
   * - isVerified: Whether user's Codeforces account is verified
   */
  const [isProcessingPayment, setIsProcessingPayment] = React.useState(false);
  const [isVerificationModalOpen, setIsVerificationModalOpen] =
    React.useState(false);
  const [isVerified, setIsVerified] = React.useState<boolean | null>(null);

  // ============================================================================
  // DATA TRANSFORMATION HELPER
  // ============================================================================
  /**
   * CRITICAL FUNCTION: Transforms API sheet data to component-friendly format
   *
   * Process:
   * 1. Parse JSON string problems from API
   * 2. Convert simplified SheetProblem objects to CodeforcesSubmission format
   * 3. Extract contest info from problem URLs
   * 4. Generate fake IDs and standardize structure
   *
   * Why needed: API stores simplified data, but components expect full submission objects
   */
  const transformSheetData = React.useCallback((sheet: any) => {
    // Step 1: Parse problems from JSON string or use array directly
    const rawProblems =
      typeof sheet.problems === "string"
        ? JSON.parse(sheet.problems)
        : sheet.problems;

    // Step 2: Transform each problem to CodeforcesSubmission format
    const transformedProblems: CodeforcesSubmission[] = rawProblems
      .filter((item: any) => item && typeof item === "object") // Safety check
      .map((sheetProblem: any, index: number) => {
        // Extract simplified data
        const { problemUrl, problemRating, tags, problemName } = sheetProblem;

        // Step 3: Parse contest info from URL (e.g., "/contest/123/problem/A")
        let contestId: number | undefined;
        let problemIndex = "A";

        if (problemUrl) {
          const urlMatch = problemUrl.match(
            /\/contest\/(\d+)\/problem\/([A-Z0-9]+)/
          );
          if (urlMatch) {
            contestId = parseInt(urlMatch[1]);
            problemIndex = urlMatch[2];
          }
        }

        // Step 4: Create standardized submission object
        const submission: CodeforcesSubmission = {
          id: index + 1, // Generate fake ID for React keys
          contestId: contestId,
          creationTimeSeconds: Date.now() / 1000,
          relativeTimeSeconds: 0,
          problem: {
            contestId: contestId,
            index: problemIndex,
            name: problemName || `Problem ${problemIndex}`,
            type: "PROGRAMMING",
            rating: problemRating ? parseInt(problemRating) : undefined,
            tags: Array.isArray(tags) ? tags : [],
          },
          author: {
            members: [{ handle: "user" }],
            participantType: "CONTESTANT",
            ghost: false,
          },
          programmingLanguage: "Unknown",
          verdict: "OK", // Assume all problems are valid
          testset: "TESTS",
          passedTestCount: 1,
          timeConsumedMillis: 0,
          memoryConsumedBytes: 0,
        };

        return submission;
      });

    // Step 5: Return sheet with transformed data
    return {
      id: sheet.id,
      problems: transformedProblems,
      createdAt: sheet.createdAt || new Date().toISOString(),
      totalProblems: transformedProblems.length,
      sheetName: sheet.name || "My Coding Sheet",
    };
  }, []);

  // ============================================================================
  // SHEET SELECTION HANDLING
  // ============================================================================
  /**
   * Handles user clicking on a different sheet
   *
   * Process:
   * 1. Validate sheet index
   * 2. Update selected sheet state
   * 3. Transform and display new sheet data
   * 4. Reset filters to show all problems
   */
  const handleSheetSelect = React.useCallback(
    (index: number) => {
      if (index >= 0 && index < allSheets.length) {
        setSelectedSheetIndex(index);
        const transformedData = transformSheetData(allSheets[index]);
        setSheetData(transformedData);
        // Reset filter when switching sheets for better UX
        setFilter("all");
      }
    },
    [allSheets, transformSheetData]
  );

  // ============================================================================
  // NAVIGATION HANDLERS
  // ============================================================================
  /**
   * Navigate to sheet creation page
   */
  const handleCreateNewSheet = React.useCallback(() => {
    router.push("/sheetscope");
  }, [router]);

  /**
   * Navigate back to main sheets page
   */
  const handleGoBack = React.useCallback(() => {
    router.push("/sheetscope");
  }, [router]);

  // ============================================================================
  // SHEET DATA INITIALIZATION
  // ============================================================================
  /**
   * Initialize sheet data when sheets are loaded
   * This replaces the old loadAllSheets function
   */
  React.useEffect(() => {
    if (allSheets.length > 0 && !sheetData) {
      // If sheets exist and no sheet is selected, display the first one by default
      const transformedData = transformSheetData(allSheets[0]);
      setSheetData(transformedData);
      setSelectedSheetIndex(0);
    } else if (allSheets.length === 0) {
      // No sheets exist - clear display
      setSheetData(null);
      setSelectedSheetIndex(-1);
    }
  }, [allSheets, sheetData, transformSheetData]);

  // ============================================================================
  // PAYMENT PROCESSING FOR ADDITIONAL SHEET SLOTS
  // ============================================================================
  /**
   * PAYMENT FLOW for purchasing additional sheet slots using React Query mutations
   *
   * Process:
   * 1. Create Razorpay order (₹40 = 4000 paise)
   * 2. Open Razorpay checkout modal
   * 3. Handle payment success/failure
   * 4. Verify payment on server
   * 5. Invalidate queries to refetch updated data
   *
   * Integration: Uses Razorpay for payment processing with React Query
   */
  const createOrder = React.useCallback(async () => {
    try {
      setIsProcessingPayment(true);

      // Step 1: Create order using React Query mutation
      const orderData = await createOrderMutation.mutateAsync(40); // ₹40

      // Step 2: Configure Razorpay checkout
      const paymentData = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID! as string,
        order_id: orderData.id,
        name: "MyCPTrainer",
        description: "Unlock New Sheet Slot",
        // Step 3: Handle successful payment
        handler: async function (response: any) {
          try {
            // Verify payment using React Query mutation
            const verificationData = await verifyPaymentMutation.mutateAsync({
              orderId: response.razorpay_order_id,
              razorpayPaymentId: response.razorpay_payment_id,
              razorpaySignature: response.razorpay_signature,
            });

            if (verificationData.isOk) {
              // Step 4: Payment verified - invalidate queries to refetch data
              alert("Payment successful! New sheet slot unlocked.");
              await queryClient.invalidateQueries({
                queryKey: ["customSheets"],
              });
            } else {
              alert("Payment verification failed. Please contact support.");
            }
          } catch (error) {
            console.error("Payment verification error:", error);
            alert("Payment verification failed. Please contact support.");
          } finally {
            setIsProcessingPayment(false);
          }
        },
        // Handle payment modal dismissal
        modal: {
          ondismiss: function () {
            setIsProcessingPayment(false);
          },
        },
      };

      // Step 5: Open Razorpay checkout
      const payment = new (window as any).Razorpay(paymentData);
      payment.open();
    } catch (error) {
      console.error("Payment creation error:", error);
      alert("Failed to initiate payment. Please try again.");
      setIsProcessingPayment(false);
    }
  }, [createOrderMutation, verifyPaymentMutation, queryClient]);

  // ============================================================================
  // CODEFORCES VERIFICATION HANDLING
  // ============================================================================
  /**
   * Check verification status using React Query
   * Returns true if verified, false if not verified
   */
  const handleCheckVerification =
    React.useCallback(async (): Promise<boolean> => {
      try {
        // Trigger the verification query
        const result = await checkVerificationStatus();
        const isVerified = result.data?.verified || false;
        setIsVerified(isVerified);
        return isVerified;
      } catch (error) {
        console.error("Error checking verification status:", error);
        setIsVerified(false);
        return false;
      }
    }, [checkVerificationStatus]);

  /**
   * Handle verification modal completion
   */
  const handleVerificationComplete = React.useCallback(
    (handle: string) => {
      console.log("Verification completed for handle:", handle);
      setIsVerificationModalOpen(false);
      setIsVerified(true);
      // After verification, proceed with payment flow
      createOrder();
    },
    [createOrder]
  );

  /**
   * Handle verification success
   */
  const handleVerificationSuccess = React.useCallback((handle: string) => {
    console.log("Verification successful for handle:", handle);
    setIsVerified(true);
  }, []);

  /**
   * Handle verification failure
   */
  const handleVerificationFailed = React.useCallback(
    (handle: string, reason: string) => {
      console.log("Verification failed for handle:", handle, "Reason:", reason);
      // Keep modal open so user can try again
    },
    []
  );

  /**
   * Handle purchase slot click with verification check
   */
  const handlePurchaseSlotClick = React.useCallback(async () => {
    if (isProcessingPayment || isCheckingVerification) return;

    // First check if user is verified using React Query
    const verified = await handleCheckVerification();

    if (verified) {
      // User is verified, proceed with payment
      createOrder();
    } else {
      // User is not verified, show verification modal
      setIsVerificationModalOpen(true);
    }
  }, [
    isProcessingPayment,
    isCheckingVerification,
    handleCheckVerification,
    createOrder,
  ]);

  // Note: React Query automatically handles data loading on component mount

  // ============================================================================
  // PROBLEM FILTERING LOGIC
  // ============================================================================
  /**
   * FILTER PROCESSING: Updates filteredProblems based on completion status
   *
   * Process:
   * 1. Check if sheet data exists
   * 2. Apply current filter (all/completed/incomplete)
   * 3. Use completion tracking from localStorage
   * 4. Update filtered problems for display
   *
   * Triggers: When sheetData, filter, or localStorage changes
   */
  React.useEffect(() => {
    if (!sheetData?.problems) {
      setFilteredProblems([]);
      return;
    }

    let filtered = sheetData.problems;

    if (filter === "completed") {
      // Show only problems marked as completed in localStorage
      filtered = sheetData.problems.filter((problem: CodeforcesSubmission) => {
        const problemId = generateProblemId(problem);
        return isProblemCompleted(problemId);
      });
    } else if (filter === "incomplete") {
      // Show only problems not marked as completed
      filtered = sheetData.problems.filter((problem: CodeforcesSubmission) => {
        const problemId = generateProblemId(problem);
        return !isProblemCompleted(problemId);
      });
    }
    // "all" filter shows everything (no filtering needed)

    setFilteredProblems(filtered);
  }, [sheetData, filter, filterUpdateTrigger]);

  // ============================================================================
  // EVENT HANDLERS FOR CHILD COMPONENTS
  // ============================================================================
  /**
   * Handle filter change from filter buttons
   */
  const handleFilterChange = React.useCallback(
    (newFilter: "all" | "completed" | "incomplete") => {
      setFilter(newFilter);
    },
    []
  );

  /**
   * Handle sheet being cleared - refetch sheets using React Query
   */
  const handleSheetCleared = React.useCallback(() => {
    refetchSheets();
  }, [refetchSheets]);

  /**
   * Handle new sheet being imported - just trigger re-filtering without reloading
   */
  const handleSheetImported = React.useCallback(() => {
    // Force re-filtering by incrementing the trigger (triggers useEffect)
    setFilterUpdateTrigger((prev) => prev + 1);
  }, []);

  /**
   * Handle problem completion toggle - re-trigger filtering
   */
  const handleProblemToggle = React.useCallback(() => {
    // Force re-filtering by incrementing the trigger (triggers useEffect)
    setFilterUpdateTrigger((prev) => prev + 1);
  }, []);

  // ============================================================================
  // RENDER: LOADING STATE
  // ============================================================================
  /**
   * LOADING UI: Shows spinner while fetching sheet data
   */
  if (isLoading) {
    return (
      <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
        <StableBackground />
        <div className="relative z-10 min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading your coding sheets...</p>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // RENDER: ERROR STATE
  // ============================================================================
  /**
   * ERROR UI: Shows error message with option to go back
   */
  if (error) {
    return (
      <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
        <StableBackground />
        <div className="relative z-10 min-h-screen flex items-center justify-center">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">
              Sheets Not Found
            </h2>
            <p className="text-gray-300 mb-6">{error}</p>
            <button
              onClick={handleGoBack}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // RENDER: MAIN SHEET DISPLAY UI
  // ============================================================================
  /**
   * MAIN UI: Complete sheet management interface
   *
   * Structure:
   * 1. Header with title and back button
   * 2. Sheet selection grid (existing sheets + empty slots + purchase option)
   * 3. Sheet management component (for selected sheet)
   * 4. Problem display component (filtered problems)
   * 5. Empty states for no sheets or no selection
   */
  return (
    <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      <StableBackground />

      {/* Load Razorpay script for payments */}
      <Script
        src="https://checkout.razorpay.com/v1/checkout.js"
        strategy="afterInteractive"
      />

      {/* Main content area */}
      <div className="relative z-10 min-h-screen mt-16">
        <div className="container mx-auto px-4 py-8">
          {/* ============================================================================ */}
          {/* HEADER SECTION: Title, stats, and navigation */}
          {/* ============================================================================ */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">
                My Coding Sheets
              </h1>
              {/* Dynamic stats showing slots usage */}
              <p className="text-gray-300">
                {allSheets.length === 0
                  ? `No sheets created yet - ${maxSheetSlots} slots available`
                  : `${allSheets.length} of ${maxSheetSlots} sheet slots used`}
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={handleGoBack}
                className="px-4 py-2 bg-gray-800/50 text-gray-200 rounded-lg hover:bg-gray-700/50 transition-colors duration-200"
              >
                ← Back
              </button>
            </div>
          </div>

          {/* ============================================================================ */}
          {/* SHEET SELECTION GRID: Shows all sheets + empty slots + purchase option */}
          {/* ============================================================================ */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">
              Your Sheets ({allSheets.length}/{maxSheetSlots} slots used):
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* ===== EXISTING SHEETS: User's created sheets ===== */}
              {allSheets.map((sheet: any, index: any) => {
                const isSelected = index === selectedSheetIndex;
                const problemCount =
                  typeof sheet.problems === "string"
                    ? JSON.parse(sheet.problems).length
                    : sheet.problems?.length || 0;

                return (
                  <div
                    key={sheet.id}
                    onClick={() => handleSheetSelect(index)}
                    className={`relative p-6 rounded-lg border cursor-pointer transition-all duration-200 ${
                      isSelected
                        ? "bg-blue-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20"
                        : "bg-gray-800/30 border-gray-700/50 hover:bg-gray-700/30 hover:border-gray-600/50"
                    }`}
                  >
                    {/* Selected indicator */}
                    {isSelected && (
                      <div className="absolute top-3 right-3">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      </div>
                    )}

                    {/* Sheet info */}
                    <h4 className="text-white font-semibold mb-2 truncate">
                      {sheet.name || "Unnamed Sheet"}
                    </h4>

                    <div className="text-sm text-gray-300 space-y-1">
                      <p>{problemCount} problems</p>
                      <p>
                        Created:{" "}
                        {new Date(
                          sheet.createdAt || Date.now()
                        ).toLocaleDateString()}
                      </p>
                    </div>

                    {/* Status indicator */}
                    <div className="mt-3 text-xs font-medium h-4">
                      {isSelected && (
                        <span className="text-blue-400">Currently viewing</span>
                      )}
                    </div>
                  </div>
                );
              })}

              {/* ===== EMPTY SLOTS: Available slots for new sheets ===== */}
              {Array.from(
                { length: maxSheetSlots - allSheets.length },
                (_, index) => (
                  <div
                    key={`empty-${index}`}
                    onClick={handleCreateNewSheet}
                    className="relative p-6 rounded-lg border border-dashed border-green-500/60 cursor-pointer transition-all duration-200 bg-gray-900/20 hover:bg-gray-800/30 hover:border-green-400/80"
                  >
                    <h4 className="text-gray-300 font-semibold mb-2 truncate">
                      Create New Sheet
                    </h4>

                    <div className="text-sm text-gray-300 space-y-1">
                      <p>Click to add new sheet</p>
                      <p>&nbsp;</p>
                    </div>

                    <div className="mt-3 text-xs font-medium h-4">
                      <span className="text-gray-400 flex items-center gap-1">
                        <Gift className="w-3 h-3" />
                        Available slot
                      </span>
                    </div>
                  </div>
                )
              )}

              {/* ===== PURCHASE SLOT: Option to buy more sheet slots ===== */}
              <div
                className={`relative p-6 rounded-lg border cursor-pointer transition-all duration-200 border-dashed ${
                  isProcessingPayment || isCheckingVerification
                    ? "bg-gray-900/50 border-red-400/70 cursor-not-allowed"
                    : "bg-gray-900/30 border-red-500/60 hover:bg-gray-800/40 hover:border-red-400/80"
                }`}
                onClick={
                  isProcessingPayment || isCheckingVerification
                    ? undefined
                    : handlePurchaseSlotClick
                }
              >
                {/* Payment status indicator */}
                <div className="absolute top-3 right-3">
                  {isProcessingPayment ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  ) : (
                    <svg
                      className="w-4 h-4 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  )}
                </div>

                <h4
                  className={`font-semibold mb-2 truncate ${
                    isProcessingPayment ? "text-blue-300" : "text-gray-300"
                  }`}
                >
                  {isProcessingPayment
                    ? "Processing payment..."
                    : "Buy more sheet slots"}
                </h4>

                <div className="text-sm text-gray-300 space-y-1">
                  <p className="text-gray-500">
                    {isProcessingPayment
                      ? "Please complete payment"
                      : isCheckingVerification
                      ? "Checking verification..."
                      : "₹40 - Expand your collection"}
                  </p>
                  {!isProcessingPayment && !isCheckingVerification && (
                    <p className="text-xs text-green-400">
                      100% Goes towards development
                    </p>
                  )}
                </div>

                {/* Payment status */}
                <div className="mt-3 text-xs font-medium h-4">
                  <span
                    className={`flex items-center gap-1 ${
                      isProcessingPayment
                        ? "text-blue-400"
                        : isCheckingVerification
                        ? "text-orange-400"
                        : "text-gray-400"
                    }`}
                  >
                    {isProcessingPayment ? (
                      <>
                        <Loader2 className="w-3 h-3 animate-spin" />
                        Do not close this tab
                      </>
                    ) : isCheckingVerification ? (
                      <>
                        <Loader2 className="w-3 h-3 animate-spin" />
                        Checking verification...
                      </>
                    ) : (
                      <>
                        <Shield className="w-3 h-3" />
                        Verify & pay securely with Razorpay. UPI / Card Options
                      </>
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* ============================================================================ */}
          {/* SHEET MANAGEMENT: Controls for selected sheet (filters, import, clear) */}
          {/* ============================================================================ */}
          {sheetData && (
            <div className="mb-8">
              <div className="bg-gray-800/30 border border-gray-700/50 rounded-lg overflow-hidden">
                {/* Sheet header with name and stats */}
                <div className="p-6 border-b border-gray-700/50">
                  <h2 className="text-2xl font-bold text-white mb-2">
                    {sheetData.sheetName}
                  </h2>
                  <p className="text-gray-300">
                    Created on{" "}
                    {new Date(sheetData.createdAt).toLocaleDateString()} •{" "}
                    {filteredProblems.length} of {sheetData.totalProblems}{" "}
                    problems shown
                  </p>
                </div>

                {/* Sheet management controls component */}
                <div className="p-6">
                  {React.createElement(
                    require("@/components/sheets/SheetManagement")
                      .SheetManagement,
                    {
                      onFilterChange: handleFilterChange,
                      onSheetCleared: handleSheetCleared,
                      onSheetImported: handleSheetImported,
                      currentFilter: filter,
                      sheetId: sheetData?.id,
                      problems: sheetData?.problems || [],
                    }
                  )}
                </div>
              </div>
            </div>
          )}

          {/* ============================================================================ */}
          {/* MAIN CONTENT AREA: Problem display or empty states */}
          {/* ============================================================================ */}
          <div className="space-y-8">
            {sheetData ? (
              // CASE 1: Sheet selected - show problems organized by rating
              React.createElement(
                require("@/components/sheets/RatingBasedProblemOrganizer")
                  .RatingBasedProblemOrganizer,
                {
                  problems: filteredProblems,
                  onProblemToggle: handleProblemToggle,
                  refreshTrigger: filterUpdateTrigger, // Pass trigger to force re-reading localStorage
                }
              )
            ) : allSheets.length === 0 ? (
              // CASE 2: No sheets exist - encourage creation
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  No sheets created yet
                </h3>
                <p className="text-gray-300 mb-6 max-w-md mx-auto">
                  Create your first coding sheet to start organizing and
                  tracking your problem-solving progress.
                </p>
                <button
                  onClick={() => router.push("/sheetscope")}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2 mx-auto"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    />
                  </svg>
                  Create Your First Sheet
                </button>
              </div>
            ) : (
              // CASE 3: Sheets exist but none selected
              <div className="text-center py-16">
                <div className="w-24 h-24 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  Select a sheet to view
                </h3>
                <p className="text-gray-300">
                  Choose a sheet from above to view and manage your problems.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Codeforces Verification Modal */}
      <TwoPartModal
        isOpen={isVerificationModalOpen}
        onClose={() => setIsVerificationModalOpen(false)}
        title="Codeforces Verification Required"
        description="Please verify your Codeforces account to unlock additional sheet slots"
        onComplete={handleVerificationComplete}
        onVerificationSuccess={handleVerificationSuccess}
        onVerificationFailed={handleVerificationFailed}
      />
    </div>
  );
};

export default CustomSheetPage;
