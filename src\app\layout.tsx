import ChunkError<PERSON>and<PERSON> from "@/components/ChunkErrorHandler/ChunkErrorHandler";
import Navbar from "@/components/layout/navbar";
import { AuthProvider } from "@/lib/auth-context";
import ReactQueryProvider from "@/lib/ReactQueryProvider";

import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  weight: ["300", "400", "500", "600", "700", "800", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "MyCPTrainer",
  description: "Your AI-powered CP Trainer",
  icons: {
    icon: "/mycptrainer.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning={true}>
      <body
        className={`${inter.variable} ${poppins.variable} font-inter antialiased`}
      >
        <ReactQueryProvider>
          <AuthProvider>
            <Navbar />
            <ChunkErrorHandler />
            {children}
          </AuthProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
