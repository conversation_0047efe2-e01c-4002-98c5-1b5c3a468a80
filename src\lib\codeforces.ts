import crypto from "crypto";

//This is basically for Codeforces API setup , hashing the keys so that we can be safe

// ============================================================================
// CODEFORCES API TYPE DEFINITIONS
// ============================================================================
// These interfaces match the exact structure returned by Codeforces API
// Documentation: https://codeforces.com/apiHelp/objects

// Represents a single problem from Codeforces
export interface CodeforcesProblem {
  contestId?: number; // Contest ID (e.g., 1234) - optional for gym problems
  problemsetName?: string; // Name of problemset if not from a contest
  index: string; // Problem index (e.g., "A", "B", "C1")
  name: string; // Problem title (e.g., "Two Sum")
  type: string; // Problem type (usually "PROGRAMMING")
  points?: number; // Points for the problem (optional)
  rating?: number; // Difficulty rating (e.g., 1200, 1500)
  tags: string[]; // Problem tags (e.g., ["math", "implementation"])
}

// Represents a party (team or individual) participating in a contest
export interface CodeforcesParty {
  contestId?: number; // Contest ID if applicable
  members: Array<{
    // Team members (usually just one for individual contests)
    handle: string; // Codeforces username
    name?: string; // Real name (optional)
  }>;
  participantType: string; // Type: CONTESTANT, PRACTICE, VIRTUAL, etc.
  teamId?: number; // Team ID for team contests
  teamName?: string; // Team name if applicable
  ghost: boolean; // Whether this is a ghost participant
  room?: number; // Contest room number
  startTimeSeconds?: number; // When the party started the contest
}

// Represents a single submission to a problem
export interface CodeforcesSubmission {
  id: number; // Unique submission ID
  contestId?: number; // Contest ID (optional for practice)
  creationTimeSeconds: number; // Unix timestamp when submitted
  relativeTimeSeconds: number; // Seconds from contest start
  problem: CodeforcesProblem; // The problem that was submitted to
  author: CodeforcesParty; // Who submitted this solution
  programmingLanguage: string; // Language used (e.g., "C++20 (GCC 13-64)")
  verdict?: string; // Result: "OK", "WRONG_ANSWER", "TIME_LIMIT_EXCEEDED", etc.
  testset: string; // Which testset was used for judging
  passedTestCount: number; // Number of test cases passed
  timeConsumedMillis: number; // Execution time in milliseconds
  memoryConsumedBytes: number; // Memory used in bytes
  points?: number; // Points scored (for IOI-style contests)
}

// Represents a rating change after a contest (used for rating graphs)
export interface CodeforcesRatingChange {
  contestId: number; // ID of the contest
  contestName: string; // Name of the contest (e.g., "Codeforces Round 912")
  handle: string; // User's handle
  rank: number; // User's rank in this contest
  ratingUpdateTimeSeconds: number; // Unix timestamp when rating was updated
  oldRating: number; // Rating before the contest
  newRating: number; // Rating after the contest
}

// Represents a Codeforces user profile (from user.info API)
export interface CodeforcesUser {
  handle: string; // Username (e.g., "tourist")
  email?: string; // Email address (optional, usually not public)
  vkId?: string; // VK social network ID (optional)
  openId?: string; // OpenID (optional)
  firstName?: string; // First name (optional)
  lastName?: string; // Last name (optional)
  country?: string; // Country (optional)
  city?: string; // City (optional)
  organization?: string; // Organization/University (optional)
  contribution: number; // Contribution points (can be negative)
  rank?: string; // Current rank title (e.g., "grandmaster", "expert")
  rating?: number; // Current rating (e.g., 2400)
  maxRank?: string; // Highest rank ever achieved
  maxRating?: number; // Highest rating ever achieved
  lastOnlineTimeSeconds: number; // Unix timestamp of last activity
  registrationTimeSeconds: number; // Unix timestamp when user registered
  friendOfCount: number; // Number of users who added this user as friend
  avatar: string; // URL to user's avatar image
  titlePhoto: string; // URL to user's title photo
}

// Generic wrapper for all Codeforces API responses
export interface CodeforcesApiResponse<T> {
  status: "OK" | "FAILED"; // Whether the API call succeeded
  comment?: string; // Error message if status is "FAILED"
  result?: T; // The actual data if status is "OK"
}

// ============================================================================
// CODEFORCES API AUTHENTICATION AND CALLING LOGIC
// ============================================================================
// Load API credentials from environment variables
const CF_API_KEY = process.env.CODEFORCES_API_KEY!;
const CF_API_SECRET = process.env.CODEFORCES_API_SECRET!;

// Main function to call any Codeforces API method with authentication
// This handles the complex authentication signature required by Codeforces
export const callCodeforcesApi = async (
  method: string, // API method name (e.g., "user.rating", "user.status")
  params: Record<string, string> // Parameters for the API call
) => {
  // Generate current timestamp for API authentication
  const time = Math.floor(Date.now() / 1000);

  // Sort parameters alphabetically and create query string
  // Codeforces requires parameters to be sorted for signature generation
  const paramString = Object.entries(params)
    .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
    .map(([key, value]) => `${key}=${value}`)
    .join("&");

  // Generate random 6-character prefix for API signature
  const apiSigPrefix = `${Math.random().toString(36).substring(2, 8)}`;

  // Create the string that needs to be hashed for authentication
  // Format: {random}/{method}?apiKey={key}&{sorted_params}&time={time}#{secret}
  const hashString = `${apiSigPrefix}/${method}?apiKey=${CF_API_KEY}&${paramString}&time=${time}#${CF_API_SECRET}`;

  // Generate SHA-512 hash of the authentication string
  const apiSig = crypto.createHash("sha512").update(hashString).digest("hex");

  // Build the final API URL with all required parameters
  const url = `https://codeforces.com/api/${method}?apiKey=${CF_API_KEY}&${paramString}&time=${time}&apiSig=${apiSigPrefix}${apiSig}`;

  // Make the HTTP request to Codeforces API
  const response = await fetch(url);
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.comment || "Failed to call Codeforces API");
  }

  // Return the JSON response
  return response.json();
};
