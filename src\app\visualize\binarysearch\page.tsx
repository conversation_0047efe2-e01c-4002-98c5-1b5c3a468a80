"use client";

import BinarySearchVisualizer from "@/components/visualizations/BinarySearchVisualizer";
import {
  ArrowUpDown,
  Binary,
  Code2,
  GitCompare,
  Search,
  Target,
  TrendingUp,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";

// Floating particles interface
interface Particle {
  id: number;
  x: number;
  y: number;
  delay: number;
  duration: number;
}

// Floating icons interface
interface FloatingIcon {
  id: number;
  icon: any;
  x: number;
  y: number;
  delay: number;
  size: number;
}

// Animated search bars interface
interface SearchBar {
  id: number;
  height: number;
  x: number;
  delay: number;
  isHighlighted: boolean;
}

// Background array interface
interface BackgroundArray {
  id: number;
  elements: number[];
  x: number;
  y: number;
  rotation: number;
  state: "searching" | "found" | "notfound";
  delay: number;
}

const page = () => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [floatingIcons, setFloatingIcons] = useState<FloatingIcon[]>([]);
  const [searchBars, setSearchBars] = useState<SearchBar[]>([]);
  const [backgroundArrays, setBackgroundArrays] = useState<BackgroundArray[]>(
    []
  );

  // Array of binary search/programming related icons
  const iconSet = [
    ArrowUpDown,
    Binary,
    Code2,
    GitCompare,
    Search,
    Target,
    TrendingUp,
    Zap,
  ];

  useEffect(() => {
    // Initialize floating particles
    setParticles(
      Array.from({ length: 50 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 10,
        duration: 15 + Math.random() * 10,
      }))
    );

    // Initialize floating icons
    setFloatingIcons(
      Array.from({ length: 12 }, (_, i) => ({
        id: i,
        icon: iconSet[Math.floor(Math.random() * iconSet.length)],
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 8,
        size: 16 + Math.random() * 8,
      }))
    );

    // Initialize animated search bars
    setSearchBars(
      Array.from({ length: 12 }, (_, i) => ({
        id: i,
        height: 20 + Math.random() * 40,
        x: i * 8,
        delay: Math.random() * 3,
        isHighlighted: Math.random() > 0.7,
      }))
    );

    // Initialize background arrays
    const arrayStates: ("searching" | "found" | "notfound")[] = [
      "searching",
      "found",
      "notfound",
    ];
    setBackgroundArrays(
      Array.from({ length: 6 }, (_, i) => ({
        id: i,
        elements: Array.from(
          { length: 5 + Math.floor(Math.random() * 3) },
          (_, j) => j * 2 + 1 + Math.floor(Math.random() * 3)
        ).sort((a, b) => a - b),
        x: 10 + Math.random() * 80,
        y: 10 + Math.random() * 80,
        rotation: Math.random() * 30 - 15, // -15 to 15 degrees
        state: arrayStates[Math.floor(Math.random() * arrayStates.length)],
        delay: Math.random() * 5,
      }))
    );
  }, []);

  const getArrayStateColor = (state: string) => {
    switch (state) {
      case "searching":
        return "text-blue-300/25";
      case "found":
        return "text-green-400/30";
      case "notfound":
        return "text-red-400/25";
      default:
        return "text-blue-300/25";
    }
  };

  return (
    <main className="min-h-screen bg-black/[0.96] antialiased bg-grid-white/[0.02] relative overflow-hidden mt-16">
      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move"
        style={{
          backgroundSize: "30px 30px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-1 h-1 bg-blue-400/30 rounded-full animate-float"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              animationDelay: `${particle.delay}s`,
              animationDuration: `${particle.duration}s`,
            }}
          />
        ))}
      </div>

      {/* Floating icons */}
      <div className="absolute inset-0 pointer-events-none">
        {floatingIcons.map((iconData) => {
          const IconComponent = iconData.icon;
          return (
            <div
              key={iconData.id}
              className="absolute text-blue-400/20 animate-float-slow"
              style={{
                left: `${iconData.x}%`,
                top: `${iconData.y}%`,
                animationDelay: `${iconData.delay}s`,
                fontSize: `${iconData.size}px`,
              }}
            >
              <IconComponent />
            </div>
          );
        })}
      </div>

      {/* Background arrays floating around */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {backgroundArrays.map((bgArray) => (
          <div
            key={bgArray.id}
            className={`absolute font-mono text-xs ${getArrayStateColor(
              bgArray.state
            )} animate-float-slow`}
            style={{
              left: `${bgArray.x}%`,
              top: `${bgArray.y}%`,
              transform: `rotate(${bgArray.rotation}deg)`,
              animationDelay: `${bgArray.delay}s`,
              animationDuration: "20s",
            }}
          >
            [{bgArray.elements.join(", ")}]
          </div>
        ))}
      </div>

      {/* Animated search bars in background */}
      <div className="absolute top-20 left-10 pointer-events-none opacity-10">
        <div className="flex items-end gap-1 h-20">
          {searchBars.map((bar) => (
            <div
              key={bar.id}
              className={`w-2 rounded-t animate-pulse ${
                bar.isHighlighted
                  ? "bg-gradient-to-t from-blue-600 via-blue-500 to-blue-400"
                  : "bg-gradient-to-t from-blue-700 via-blue-600 to-blue-500"
              }`}
              style={{
                height: `${bar.height}px`,
                animationDelay: `${bar.delay}s`,
                animationDuration: "3s",
              }}
            />
          ))}
        </div>
      </div>

      {/* Another set of search bars in different corner */}
      <div className="absolute bottom-20 right-10 pointer-events-none opacity-10">
        <div className="flex items-end gap-1 h-16">
          {searchBars.slice(0, 8).map((bar) => (
            <div
              key={`right-${bar.id}`}
              className={`w-2 rounded-t animate-pulse ${
                bar.isHighlighted
                  ? "bg-gradient-to-t from-blue-700 via-blue-600 to-blue-500"
                  : "bg-gradient-to-t from-blue-800 via-blue-700 to-blue-600"
              }`}
              style={{
                height: `${bar.height * 0.8}px`,
                animationDelay: `${bar.delay + 1}s`,
                animationDuration: "2.5s",
              }}
            />
          ))}
        </div>
      </div>

      {/* Decorative search patterns */}
      <div className="absolute inset-0 pointer-events-none opacity-5">
        {/* Search target indicators */}
        <div
          className="absolute top-1/4 left-1/4 w-8 h-8 border-2 border-blue-500 rounded-full animate-ping"
          style={{ animationDelay: "0s", animationDuration: "4s" }}
        />
        <div
          className="absolute top-3/4 right-1/4 w-6 h-6 border-2 border-blue-600 rounded-full animate-ping"
          style={{ animationDelay: "2s", animationDuration: "4s" }}
        />
        <div
          className="absolute bottom-1/4 left-3/4 w-4 h-4 border-2 border-blue-400 rounded-full animate-ping"
          style={{ animationDelay: "1s", animationDuration: "4s" }}
        />

        {/* Binary search tree-like patterns */}
        <div className="absolute top-1/3 right-1/3 opacity-20">
          <div className="relative">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <div className="absolute top-4 -left-4 w-1 h-1 bg-blue-400 rounded-full"></div>
            <div className="absolute top-4 left-4 w-1 h-1 bg-blue-400 rounded-full"></div>
            <div className="absolute top-8 -left-6 w-0.5 h-0.5 bg-blue-300 rounded-full"></div>
            <div className="absolute top-8 -left-2 w-0.5 h-0.5 bg-blue-300 rounded-full"></div>
            <div className="absolute top-8 left-2 w-0.5 h-0.5 bg-blue-300 rounded-full"></div>
            <div className="absolute top-8 left-6 w-0.5 h-0.5 bg-blue-300 rounded-full"></div>
          </div>
        </div>

        {/* Vertical lines representing comparisons */}
        <div
          className="absolute left-1/5 top-0 w-px h-1/3 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse"
          style={{ animationDelay: "0.5s" }}
        />
        <div
          className="absolute right-1/5 bottom-0 w-px h-1/4 bg-gradient-to-t from-transparent via-blue-600/20 to-transparent animate-pulse"
          style={{ animationDelay: "1.5s" }}
        />
      </div>

      {/* Binary Search Algorithms Visualization Section */}
      <div className="relative py-20 z-10">
        {/* Custom glowing background */}
        <div
          aria-hidden="true"
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-[400px] w-[400px] rounded-full bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 opacity-25 blur-3xl -z-10"
        />

        {/* Content */}
        <div className="relative text-center mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Visualize Binary Search Algorithms
          </h1>
          <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
            Explore how binary search algorithms work step by step with
            interactive animations and clear comparisons between standard, lower
            bound, and upper bound searches.
          </p>

          {/* Binary Search Visualizer Container */}
          <div className="w-[95%] mx-auto">
            <BinarySearchVisualizer />
          </div>
        </div>
      </div>
    </main>
  );
};

export default page;
