"use client";

import ProfessionalH<PERSON>Background from "@/components/layout/ProfessionalHeroBackground";
import { TwoPartModal } from "@/components/ui/reusable-modal";
import { useAuth } from "@/lib/auth-context";
import { signInWithPopup } from "@/lib/auth-popup";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Activity, BarChart3 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

// Import tab components (we'll create these)
import { AnalyticsTab } from "@/components/dashboard/analytics";
// import InsightsTab from "@/components/dashboard/InsightsTab";
import { OverviewTab } from "@/components/dashboard/overview";
// import PracticeTab from "@/components/dashboard/PracticeTab";
// import ToolsTab from "@/components/dashboard/ToolsTab";

// Tab configuration
const tabs = [
  {
    id: "overview",
    label: "Overview",
    icon: Activity,
    component: OverviewTab,
  },
  {
    id: "analytics",
    label: "Analytics",
    icon: BarChart3,
    component: AnalyticsTab,
  },
  // {
  //   id: "practice",
  //   label: "Practice",
  //   icon: BookOpen,
  //   component: PracticeTab,
  // },
  // {
  //   id: "insights",
  //   label: "Insights",
  //   icon: Lightbulb,
  //   component: InsightsTab,
  // },
  // {
  //   id: "tools",
  //   label: "Tools",
  //   icon: Settings,
  //   component: ToolsTab,
  // },
];

// Component for rendering authentication content
const AuthenticationContent = ({
  user,
  authLoading,
  googleLoginError,
  isGoogleLoginLoading,
  handleGoogleSignIn,
}: {
  user: any;
  authLoading: boolean;
  googleLoginError: string | null;
  isGoogleLoginLoading: boolean;
  handleGoogleSignIn: () => void;
}) => {
  if (authLoading) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto">
          <p className="text-gray-400 mb-6">
            Please sign in with your Google account to access your dashboard
          </p>

          {/* Error message */}
          {googleLoginError && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-600/30 rounded-lg">
              <p className="text-red-400 text-sm">{googleLoginError}</p>
            </div>
          )}

          {/* Google Sign In Button */}
          <button
            onClick={handleGoogleSignIn}
            disabled={isGoogleLoginLoading}
            className="group relative w-full flex justify-center items-center py-3 px-4 border border-gray-600 text-sm font-medium rounded-md text-white bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200 hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGoogleLoginLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-700 mr-3"></div>
                <span className="text-gray-700">Signing in...</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                <span className="text-gray-700">Sign in with Google</span>
              </>
            )}
          </button>
        </div>
      </div>
    );
  }

  return null;
};

// Component for rendering verification content
const VerificationContent = ({
  user,
  verificationStatus,
  verificationLoading,
  isVerificationModalOpen,
  setIsVerificationModalOpen,
}: {
  user: any;
  verificationStatus: any;
  verificationLoading: boolean;
  isVerificationModalOpen: boolean;
  setIsVerificationModalOpen: (open: boolean) => void;
}) => {
  if (verificationLoading) {
    return (
      <div className="min-h-[400px] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Checking verification status...</p>
        </div>
      </div>
    );
  }

  if (user && verificationStatus && !verificationStatus.isVerified) {
    return (
      <>
        <div className="min-h-[400px] flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-3xl font-bold bg-gradient-to-r text-white bg-clip-text mb-4">
              Codeforces Verification Required
            </h2>
            <p className="text-gray-400 mb-6">
              Please verify your Codeforces account to access your dashboard
            </p>
            <button
              onClick={() => setIsVerificationModalOpen(true)}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium"
            >
              Verify Codeforces Profile
            </button>
          </div>
        </div>

        {/* Verification Modal */}
        <TwoPartModal
          isOpen={isVerificationModalOpen}
          onClose={() => setIsVerificationModalOpen(false)}
          title="Codeforces Verification"
          description="Please verify your Codeforces account to access the dashboard"
          onComplete={() => {
            setIsVerificationModalOpen(false);
            // Refetch verification status
            window.location.reload();
          }}
          onVerificationSuccess={() => {
            setIsVerificationModalOpen(false);
            // Refetch verification status
            window.location.reload();
          }}
        />
      </>
    );
  }

  return null;
};

const DashboardPage = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [isGoogleLoginLoading, setIsGoogleLoginLoading] = useState(false);
  const [googleLoginError, setGoogleLoginError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();

  // Ensure we only render dynamic content on the client to prevent hydration mismatches
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check if user has completed Codeforces verification
  const { data: verificationStatus, isLoading: verificationLoading } = useQuery(
    {
      queryKey: ["codeforcesVerification", user?.email],
      queryFn: async () => {
        const response = await fetch("/api/codeforces/verifyHandle");
        if (response.status === 404) {
          // User not found means not verified
          return { isVerified: false };
        }
        if (!response.ok) {
          throw new Error("Failed to check verification status");
        }
        const data = await response.json();
        return { isVerified: !!data.handle };
      },
      enabled: !!user?.email,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    }
  );

  // Handle Google authentication with popup
  const handleGoogleSignIn = async () => {
    if (isGoogleLoginLoading) return;

    setIsGoogleLoginLoading(true);
    setGoogleLoginError(null);

    try {
      const result = await signInWithPopup("google", {
        width: 500,
        height: 600,
        timeout: 300000,
      });

      if (result.success) {
        // Invalidate queries to refresh user data
        queryClient.invalidateQueries({ queryKey: ["userInfo"] });
        queryClient.invalidateQueries({ queryKey: ["codeforcesVerification"] });
      } else {
        setGoogleLoginError(result.error || "Authentication failed");
      }
    } catch (error) {
      console.error("Login failed:", error);
      setGoogleLoginError("An unexpected error occurred");
    } finally {
      setIsGoogleLoginLoading(false);
    }
  };

  // Find the active tab component
  const ActiveTabComponent = tabs.find(
    (tab) => tab.id === activeTab
  )?.component;

  // Determine if we should show the dashboard content
  const shouldShowDashboard = user && verificationStatus?.isVerified;

  return (
    <ProfessionalHeroBackground>
      <div className="container mx-auto p-6 max-w-7xl mt-20 relative z-10">
        {/* Premium Tab Navigation - Always render for CDN caching */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2 p-2 bg-slate-900/60 backdrop-blur-lg border border-slate-600/30 rounded-2xl shadow-xl">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;

                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      relative flex items-center gap-2 px-6 py-3 rounded-xl font-medium text-sm
                      transition-all duration-300 ease-out
                      ${
                        isActive
                          ? "bg-blue-600 text-white shadow-lg shadow-blue-500/25 scale-105"
                          : "text-gray-400 hover:text-gray-200 hover:bg-slate-800/50"
                      }
                    `}
                    disabled={!isClient || !shouldShowDashboard}
                  >
                    {/* Active tab glow effect */}
                    {isActive && (
                      <div className="absolute inset-0 bg-blue-600 rounded-xl blur-sm opacity-50 -z-10" />
                    )}

                    <Icon
                      className={`w-4 h-4 ${
                        isActive ? "text-white" : "text-gray-400"
                      }`}
                    />
                    <span className="whitespace-nowrap">{tab.label}</span>

                    {/* Active indicator */}
                    {isActive && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full opacity-80" />
                    )}
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Tab Content Container - Always render for CDN caching */}
        <div className="min-h-[600px]">
          {/* Show loading state during hydration to prevent mismatch */}
          {!isClient ? (
            <div className="min-h-[400px] flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-400">Loading dashboard...</p>
              </div>
            </div>
          ) : (
            <>
              {/* Dynamic Content - Conditionally rendered based on auth/verification state */}
              <AuthenticationContent
                user={user}
                authLoading={authLoading}
                googleLoginError={googleLoginError}
                isGoogleLoginLoading={isGoogleLoginLoading}
                handleGoogleSignIn={handleGoogleSignIn}
              />

              <VerificationContent
                user={user}
                verificationStatus={verificationStatus}
                verificationLoading={verificationLoading}
                isVerificationModalOpen={isVerificationModalOpen}
                setIsVerificationModalOpen={setIsVerificationModalOpen}
              />

              {/* Dashboard Content - Only render when authenticated and verified */}
              {shouldShowDashboard && ActiveTabComponent && (
                <ActiveTabComponent />
              )}
            </>
          )}
        </div>
      </div>
    </ProfessionalHeroBackground>
  );
};

export default DashboardPage;
