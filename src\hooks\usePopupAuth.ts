"use client";

import {
  AUTH_ERROR_CODES,
  isPopupSupported,
  signInWithPopup,
  signInWithPopupRetry,
  type PopupAuthOptions,
  type PopupAuthResult,
} from "@/lib/auth-popup";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";

interface UsePopupAuthReturn {
  signIn: (
    provider?: "google" | "github" | "facebook" | "twitter",
    options?: PopupAuthOptions
  ) => Promise<PopupAuthResult>;
  signInWithRetry: (
    provider?: "google" | "github" | "facebook" | "twitter",
    options?: PopupAuthOptions
  ) => Promise<PopupAuthResult>;
  isLoading: boolean;
  error: string | null;
  errorCode: string | null;
  clearError: () => void;
  isMounted: boolean;
  canRetry: boolean;
  retryCount: number;
}

/**
 * Custom hook for popup-based authentication with enhanced error handling
 *
 * @param redirectTo - URL to redirect to after successful authentication
 * @returns Object with signIn functions, loading state, error state, and utility functions
 */
export function usePopupAuth(redirectTo: string = "/"): UsePopupAuthReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errorCode, setErrorCode] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const router = useRouter();

  // Ref to prevent multiple concurrent authentication attempts
  const isAuthenticating = useRef(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
    setErrorCode(null);
  }, []);

  const canRetry = useCallback(() => {
    if (!errorCode) return false;

    // Errors that should not be retried
    const nonRetryableErrors = [
      AUTH_ERROR_CODES.POPUP_BLOCKED,
      AUTH_ERROR_CODES.USER_CANCELLED,
      AUTH_ERROR_CODES.CONFIG_ERROR,
      AUTH_ERROR_CODES.BROADCAST_ERROR,
    ];

    return !nonRetryableErrors.includes(errorCode as any);
  }, [errorCode]);

  const handleAuthResult = useCallback(
    (result: PopupAuthResult) => {
      if (result.success) {
        // Clear any previous errors on success
        setError(null);
        setErrorCode(null);
        setRetryCount(0);

        // Authentication successful, redirect to specified page
        router.push(redirectTo);
      } else {
        setError(result.error || "Authentication failed");
        setErrorCode(result.errorCode || AUTH_ERROR_CODES.UNKNOWN_ERROR);
      }
    },
    [router, redirectTo]
  );

  const signIn = useCallback(
    async (
      provider: "google" | "github" | "facebook" | "twitter" = "google",
      options: PopupAuthOptions = {}
    ): Promise<PopupAuthResult> => {
      // Prevent multiple concurrent authentication attempts
      if (isAuthenticating.current) {
        const result = {
          success: false,
          error: "Authentication already in progress",
          errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
        };
        return result;
      }

      // Check if popups are supported
      if (!isPopupSupported()) {
        const result = {
          success: false,
          error:
            "Popup windows are blocked. Please enable popups for this site and try again.",
          errorCode: AUTH_ERROR_CODES.POPUP_BLOCKED,
        };
        setError(result.error);
        setErrorCode(result.errorCode);
        return result;
      }

      setIsLoading(true);
      setError(null);
      setErrorCode(null);
      isAuthenticating.current = true;

      try {
        const result = await signInWithPopup(provider, {
          width: 500,
          height: 600,
          timeout: 300000, // 5 minutes
          ...options,
        });

        handleAuthResult(result);
        return result;
      } catch (err) {
        const result = {
          success: false,
          error:
            err instanceof Error ? err.message : "An unexpected error occurred",
          errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
        };
        handleAuthResult(result);
        return result;
      } finally {
        setIsLoading(false);
        isAuthenticating.current = false;
      }
    },
    [handleAuthResult]
  );

  const signInWithRetry = useCallback(
    async (
      provider: "google" | "github" | "facebook" | "twitter" = "google",
      options: PopupAuthOptions = {}
    ): Promise<PopupAuthResult> => {
      // Prevent multiple concurrent authentication attempts
      if (isAuthenticating.current) {
        const result = {
          success: false,
          error: "Authentication already in progress",
          errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
        };
        return result;
      }

      // Check if popups are supported
      if (!isPopupSupported()) {
        const result = {
          success: false,
          error:
            "Popup windows are blocked. Please enable popups for this site and try again.",
          errorCode: AUTH_ERROR_CODES.POPUP_BLOCKED,
        };
        setError(result.error);
        setErrorCode(result.errorCode);
        return result;
      }

      setIsLoading(true);
      setError(null);
      setErrorCode(null);
      isAuthenticating.current = true;

      try {
        const maxRetries = 2;
        setRetryCount(0);

        const result = await signInWithPopupRetry(provider, {
          width: 500,
          height: 600,
          timeout: 300000, // 5 minutes
          retryAttempts: maxRetries,
          ...options,
        });

        // Track retry count for user feedback
        if (!result.success && result.errorCode) {
          const nonRetryableErrors = [
            AUTH_ERROR_CODES.POPUP_BLOCKED,
            AUTH_ERROR_CODES.USER_CANCELLED,
            AUTH_ERROR_CODES.CONFIG_ERROR,
            AUTH_ERROR_CODES.BROADCAST_ERROR,
          ];

          if (!nonRetryableErrors.includes(result.errorCode as any)) {
            setRetryCount(maxRetries);
          }
        }

        handleAuthResult(result);
        return result;
      } catch (err) {
        const result = {
          success: false,
          error:
            err instanceof Error ? err.message : "An unexpected error occurred",
          errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
        };
        handleAuthResult(result);
        return result;
      } finally {
        setIsLoading(false);
        isAuthenticating.current = false;
      }
    },
    [handleAuthResult]
  );

  return {
    signIn,
    signInWithRetry,
    isLoading,
    error,
    errorCode,
    clearError,
    isMounted,
    canRetry: canRetry(),
    retryCount,
  };
}
