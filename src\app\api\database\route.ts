import {
  dbUtils,
  handlePrismaError,
  prisma,
  safeDbOperation,
} from "@/lib/database";
import { NextResponse } from "next/server";

/**
 * GET /api/database - Database health check and user lookup
 * Enhanced with production-ready error handling and monitoring
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get("email");
    const healthCheck = searchParams.get("health");

    // Health check endpoint
    if (healthCheck === "true") {
      const status = await dbUtils.getStatus();
      return NextResponse.json({
        status: "ok",
        database: status,
        timestamp: new Date().toISOString(),
      });
    }

    // User lookup with enhanced error handling
    const result = await safeDbOperation(async () => {
      return await prisma.users.findFirst({
        where: {
          email: email || "<EMAIL>",
        },
        select: {
          id: true,
          email: true,
          handle: true,
          rating: true,
          rank: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              sheets: true,
            },
          },
        },
      });
    }, "GET /api/database - user lookup");

    if (!result.success) {
      return NextResponse.json(
        {
          error: "Database operation failed",
          message: result.error.userMessage,
          code: result.error.code,
          retryable: result.error.isRetryable,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      user: result.data,
      sheetCount: result.data?._count?.sheets || 0,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Database GET error:", error);
    const dbError = handlePrismaError(error);

    return NextResponse.json(
      {
        error: "Unexpected database error",
        message: dbError.userMessage,
        code: dbError.code,
      },
      { status: 500 }
    );
  }
}
