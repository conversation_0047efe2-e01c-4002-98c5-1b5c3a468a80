"use client";

import { ArrayElement, SortingAlgorithm } from "@/lib/types";
import { AnimatePresence, motion } from "framer-motion";

interface ArrayDisplayProps {
  array: ArrayElement[];
  isSorting: boolean;
  selectedAlgorithm: SortingAlgorithm;
}

const ArrayDisplay = ({
  array,
  isSorting,
  selectedAlgorithm,
}: ArrayDisplayProps) => {
  if (array.length === 0) return null;

  return (
    <div className="mt-8">
      <h3 className="text-xl font-semibold text-white mb-4 text-center">
        Array Visualization
      </h3>
      <div className="flex flex-wrap justify-center gap-4 p-8 bg-gray-800/30 rounded-xl border border-gray-700/30">
        <AnimatePresence mode="wait">
          {array.map((element, index) => (
            <motion.div
              key={element.id}
              layout
              initial={{ opacity: 0, scale: 0.8, y: -20 }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                backgroundColor: element.isSwapping
                  ? "#ef4444" // red-500
                  : element.isSorted
                  ? "#10b981" // green-500
                  : element.isPivot
                  ? "#ec4899" // pink-500
                  : element.isMinimum
                  ? "#a855f7" // purple-500
                  : element.isCurrent
                  ? "#14b8a6" // teal-500
                  : element.isComparing
                  ? "#f59e0b" // amber-500
                  : "#3b82f6", // blue-500
                borderColor:
                  element.isComparing ||
                  element.isSwapping ||
                  element.isMinimum ||
                  element.isCurrent ||
                  element.isPivot
                    ? "#ffffff"
                    : "transparent",
              }}
              exit={{ opacity: 0, scale: 0.8, y: 20 }}
              transition={{
                layout: { duration: 0.3, ease: "easeInOut" },
                backgroundColor: { duration: 0.2 },
                borderColor: { duration: 0.2 },
                scale: { duration: 0.2 },
                opacity: { duration: 0.2 },
              }}
              className="relative flex items-center justify-center w-20 h-20 rounded-lg border-2 font-bold text-white shadow-lg cursor-pointer hover:scale-105"
              whileHover={!isSorting ? { scale: 1.1 } : {}}
              whileTap={!isSorting ? { scale: 0.95 } : {}}
            >
              <span className="text-xl font-mono">{element.value}</span>

              {/* Index indicator */}
              <motion.div
                className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-400 font-mono"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                [{index}]
              </motion.div>

              {/* Status indicators */}
              <AnimatePresence mode="wait">
                {(() => {
                  if (element.isSwapping) {
                    return (
                      <motion.div
                        key="swapping"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-red-400 font-semibold bg-red-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Swapping
                      </motion.div>
                    );
                  }
                  if (element.isPivot) {
                    return (
                      <motion.div
                        key="pivot"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-pink-400 font-semibold bg-pink-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Pivot
                      </motion.div>
                    );
                  }
                  if (element.isComparing) {
                    return (
                      <motion.div
                        key="comparing"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-amber-400 font-semibold bg-amber-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Comparing
                      </motion.div>
                    );
                  }
                  if (element.isMinimum) {
                    return (
                      <motion.div
                        key="min"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-purple-400 font-semibold bg-purple-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Min
                      </motion.div>
                    );
                  }
                  if (element.isCurrent) {
                    return (
                      <motion.div
                        key="current"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-teal-400 font-semibold bg-teal-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Current
                      </motion.div>
                    );
                  }
                  if (element.isSorted) {
                    return (
                      <motion.div
                        key="sorted"
                        className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs text-green-400 font-semibold bg-green-900/50 px-2 py-1 rounded"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                      >
                        Sorted
                      </motion.div>
                    );
                  }
                  return null;
                })()}
              </AnimatePresence>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Array Information */}
      <div className="mt-4 text-center text-sm text-gray-400">
        <p>
          Array Length: {array.length} | Algorithm:{" "}
          {selectedAlgorithm.charAt(0).toUpperCase() +
            selectedAlgorithm.slice(1)}{" "}
          Sort
        </p>
      </div>
    </div>
  );
};

export default ArrayDisplay;
