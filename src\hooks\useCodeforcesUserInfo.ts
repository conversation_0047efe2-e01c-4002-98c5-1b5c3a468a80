import type { CodeforcesUser } from "@/lib/codeforces";
import { fetchCodeforcesUserInfo } from "@/lib/codeforces-client";
import { useQuery } from "@tanstack/react-query";

// ============================================================================
// CODEFORCES USER INFO REACT QUERY HOOK (CLIENT-SIDE)
// ============================================================================
// This hook fetches Codeforces user profile information directly from the
// Codeforces API on the client side, avoiding backend API calls and
// distributing requests across different user IPs to prevent rate limiting.

// Parameters for the user info hook
interface UseCodeforcesUserInfoParams {
  handle: string; // Codeforces username
  enabled?: boolean; // Whether to enable the query (default: true)
}

// Response structure for user info
interface UserInfoResponse {
  status: "OK" | "FAILED";
  comment?: string;
  result?: CodeforcesUser[];
}

/**
 * React Query hook for fetching Codeforces user profile information
 * Makes direct API calls to Codeforces from the client side
 * @param handle - Codeforces username to fetch info for
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with user profile data
 */
export const useCodeforcesUserInfo = ({
  handle,
  enabled = true,
}: UseCodeforcesUserInfoParams) => {
  return useQuery({
    // Unique query key for caching - includes handle to cache per user
    queryKey: ["codeforces-user-info-client", handle],

    // Function that actually fetches the data from Codeforces API
    queryFn: async (): Promise<CodeforcesUser> => {
      // Validate input before making API call
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      try {
        // Call Codeforces user.info API directly from client
        const response = await fetchCodeforcesUserInfo(handle.trim());

        // Check if the API call failed
        if (response.status === "FAILED") {
          throw new Error(response.comment || "Failed to fetch user info");
        }

        // Check if user was found
        if (!response.result || response.result.length === 0) {
          throw new Error("User not found");
        }

        // Return the first (and only) user from the result
        return response.result[0];
      } catch (error: any) {
        console.error("Error fetching Codeforces user info:", error);
        throw error;
      }
    },

    // Only run the query if enabled and handle is provided
    enabled: enabled && !!handle.trim(),

    // Cache configuration - user info changes infrequently
    staleTime: 30 * 60 * 1000, // Consider data fresh for 30 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour

    // Don't refetch on window focus for user profile data
    refetchOnWindowFocus: false,

    // Don't refetch on reconnect for static user data
    refetchOnReconnect: false,

    // Smart retry logic
    retry: (failureCount, error: any) => {
      // Don't retry on user not found errors
      if (error.message?.includes("User not found")) {
        return false;
      }

      // Don't retry on API errors that indicate permanent failures
      if (error.message?.includes("FAILED")) {
        return false;
      }

      // Retry up to 2 times for network issues (reduced from 3)
      return failureCount < 2;
    },

    // Exponential backoff for retries (1s, 2s, 4s, max 30s)
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching multiple users' info at once
 * @param handles - Array of Codeforces usernames (max 10000)
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with array of user profile data
 */
export const useCodeforcesMultipleUserInfo = ({
  handles,
  enabled = true,
}: {
  handles: string[];
  enabled?: boolean;
}) => {
  return useQuery({
    // Unique query key for caching - includes all handles
    queryKey: ["codeforces-multiple-user-info-client", handles.sort()],

    // Function that fetches data for multiple users
    queryFn: async (): Promise<CodeforcesUser[]> => {
      // Validate input
      if (!handles.length) {
        throw new Error("At least one handle is required");
      }

      // Filter out empty handles
      const validHandles = handles.filter((handle) => handle.trim());

      if (!validHandles.length) {
        throw new Error("No valid handles provided");
      }

      try {
        // Call Codeforces user.info API with multiple handles
        const response = await fetchCodeforcesUserInfo(validHandles);

        // Check if the API call failed
        if (response.status === "FAILED") {
          throw new Error(response.comment || "Failed to fetch user info");
        }

        // Return the users array (may be empty if no users found)
        return response.result || [];
      } catch (error: any) {
        console.error("Error fetching multiple Codeforces user info:", error);
        throw error;
      }
    },

    // Only run the query if enabled and handles are provided
    enabled: enabled && handles.length > 0,

    // Cache configuration
    staleTime: 30 * 60 * 1000, // Consider data fresh for 30 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour after component unmounts

    // Retry logic
    retry: (failureCount, error: any) => {
      // Don't retry on API errors that indicate permanent failures
      if (error.message?.includes("FAILED")) {
        return false;
      }

      // Retry up to 3 times for network issues
      return failureCount < 3;
    },

    // Exponential backoff for retries
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
