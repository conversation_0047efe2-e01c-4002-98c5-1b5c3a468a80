"use client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/lib/auth-context";
import { createClient } from "@/lib/supabase/client";
import {
  Cctv,
  ChevronDown,
  Dumbbell,
  LogOut,
  LucideLayoutDashboard,
  Menu,
  ScrollText,
  Telescope,
  User as UserIcon,
  X,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { memo, useCallback, useEffect, useMemo, useState } from "react";

const Navbar = memo(() => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { user, loading } = useAuth();
  const pathname = usePathname();

  // Memoize the Supabase client to avoid recreating on every render
  const supabase = useMemo(() => createClient(), []);

  // Helper function to determine if a link is active
  const isActiveLink = useCallback(
    (href: string) => {
      return pathname === href;
    },
    [pathname]
  );

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isMenuOpen]);

  const closeMenu = useCallback(() => setIsMenuOpen(false), []);
  const toggleMenu = useCallback(
    () => setIsMenuOpen(!isMenuOpen),
    [isMenuOpen]
  );

  const handleSignOut = useCallback(async () => {
    await supabase.auth.signOut();
    closeMenu();
  }, [supabase.auth, closeMenu]);

  const handleNavigation = useCallback(
    (href: string) => {
      window.location.href = href;
      closeMenu();
    },
    [closeMenu]
  );

  return (
    <>
      <nav className="fixed top-0 left-0 w-full z-40 bg-black/80 backdrop-blur-lg border-b border-gray-900">
        <div className="container mx-auto px-6">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link href="/" prefetch={false} className="flex items-center">
                <span className="text-white text-2xl font-thin">
                  MyCPTrainer
                </span>
              </Link>
            </div>
            <div className="hidden md:flex md:items-center md:space-x-2">
              <Link
                href="/sheetscope"
                prefetch={false}
                className={`transition-all duration-300 flex items-center px-3 py-2 rounded-lg border ${
                  isActiveLink("/sheetscope")
                    ? "text-white bg-gray-800/50 border-gray-700/50"
                    : "text-gray-200 hover:text-gray-300 bg-transparent border-transparent hover:bg-gray-800/20"
                }`}
              >
                <Telescope className="w-4 h-4 mr-2" />
                SheetScope
              </Link>

              <Link
                href="/customsheet"
                prefetch={false}
                className={`transition-all duration-300 flex items-center px-3 py-2 rounded-lg border ${
                  isActiveLink("/customsheet")
                    ? "text-white bg-gray-800/50 border-gray-700/50"
                    : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
                }`}
              >
                <ScrollText className="w-4 h-4 mr-2" />
                Custom Sheet
              </Link>
              <Link
                href="/dashboard"
                prefetch={false}
                className={`transition-all duration-300 flex items-center px-3 py-2 rounded-lg border ${
                  isActiveLink("/dashboard")
                    ? "text-white bg-gray-800/50 border-gray-700/50"
                    : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
                }`}
              >
                <LucideLayoutDashboard className="w-4 h-4 mr-2" />
                Dashboard
              </Link>

              <Link
                href="/visualize"
                prefetch={false}
                className={`transition-all duration-300 flex items-center px-3 py-2 rounded-lg border ${
                  isActiveLink("/visualize")
                    ? "text-white bg-gray-800/50 border-gray-700/50"
                    : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
                }`}
              >
                <Cctv className="w-4 h-4 mr-2" />
                Visualize
              </Link>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className="bg-gray-800/50 hover:bg-gray-700/60 border border-gray-700/50 text-white font-medium py-2 px-3 rounded-lg transition-all duration-300 flex items-center">
                    {isMounted &&
                    !loading &&
                    user?.user_metadata?.avatar_url ? (
                      <Image
                        src={user.user_metadata.avatar_url}
                        alt={user.email ?? "User avatar"}
                        width={24}
                        height={24}
                        className="rounded-full"
                      />
                    ) : (
                      <UserIcon className="w-5 h-5" />
                    )}
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-64 bg-black/95 border border-gray-800/50 shadow-2xl"
                  align="end"
                >
                  {isMounted && !loading && user ? (
                    <>
                      <DropdownMenuLabel className="px-4 py-3 border-b border-gray-700">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none text-white">
                            {user.email}
                          </p>
                          <p className="text-xs text-gray-400">
                            {user.user_metadata?.full_name || "User"}
                          </p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => handleNavigation("/profile")}
                        className="text-gray-200 hover:bg-gray-700/60 cursor-pointer"
                      >
                        <UserIcon className="w-4 h-4 mr-2" />
                        Profile
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleNavigation("/trainer")}
                        className="text-gray-200 hover:bg-gray-700/60 cursor-pointer"
                      >
                        <Dumbbell className="w-4 h-4 mr-2" />
                        Trainer
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="border-gray-700" />
                      <DropdownMenuItem
                        onClick={handleSignOut}
                        className="text-red-400 hover:bg-red-900/30 cursor-pointer"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                      </DropdownMenuItem>
                    </>
                  ) : !isMounted || loading ? (
                    <DropdownMenuLabel className="px-4 py-3">
                      <div className="flex items-center justify-center">
                        <div className="w-5 h-5 border-2 border-gray-600 border-t-white rounded-full animate-spin"></div>
                      </div>
                    </DropdownMenuLabel>
                  ) : (
                    <>
                      <DropdownMenuLabel className="px-4 py-3 border-b border-gray-700">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none text-white">
                            Welcome to MyCPTrainer
                          </p>
                          <p className="text-xs text-gray-400">
                            Sign in to access your account
                          </p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => handleNavigation("/login")}
                        className="text-blue-400 hover:bg-blue-900/30 cursor-pointer"
                      >
                        <UserIcon className="w-4 h-4 mr-2" />
                        Sign In
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="md:hidden flex items-center">
              <button
                onClick={toggleMenu}
                className="text-gray-300 hover:text-white focus:outline-none"
                aria-label="Toggle menu"
              >
                <Menu className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <div
        className={`md:hidden fixed inset-0 bg-black z-50 transition-transform duration-300 ease-in-out ${
          isMenuOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="container mx-auto px-6 h-full flex flex-col">
          <div className="flex justify-between items-center h-16">
            <div className="flex-shrink-0">
              <Link
                href="/"
                prefetch={false}
                className="flex items-center"
                onClick={closeMenu}
              >
                <span className="text-white text-2xl font-bold">
                  MyCPTrainer
                </span>
              </Link>
            </div>
            <button
              onClick={closeMenu}
              className="text-gray-300 hover:text-white focus:outline-none"
              aria-label="Close menu"
            >
              <X className="h-8 w-8" />
            </button>
          </div>

          <div className="flex flex-col items-start mt-12 space-y-4 text-2xl font-light">
            <Link
              href="/sheetscope"
              prefetch={false}
              onClick={closeMenu}
              className={`transition-all duration-300 flex items-center px-4 py-3 rounded-lg border w-full ${
                isActiveLink("/sheetscope")
                  ? "text-white bg-gray-800/50 border-gray-700/50"
                  : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
              }`}
            >
              <Telescope className="w-6 h-6 mr-4" />
              SheetScope
            </Link>

            <Link
              href="/customsheet"
              prefetch={false}
              onClick={closeMenu}
              className={`transition-all duration-300 flex items-center px-4 py-3 rounded-lg border w-full ${
                isActiveLink("/customsheet")
                  ? "text-white bg-gray-800/50 border-gray-700/50"
                  : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
              }`}
            >
              <ScrollText className="w-6 h-6 mr-4" />
              Custom Sheet
            </Link>
            <Link
              href="/dashboard"
              prefetch={false}
              onClick={closeMenu}
              className={`transition-all duration-300 flex items-center px-4 py-3 rounded-lg border w-full ${
                isActiveLink("/dashboard")
                  ? "text-white bg-gray-800/50 border-gray-700/50"
                  : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
              }`}
            >
              <LucideLayoutDashboard className="w-6 h-6 mr-4" />
              Dashboard
            </Link>
            <Link
              href="/visualize"
              prefetch={false}
              onClick={closeMenu}
              className={`transition-all duration-300 flex items-center px-4 py-3 rounded-lg border w-full ${
                isActiveLink("/visualize")
                  ? "text-white bg-gray-800/50 border-gray-700/50"
                  : "text-gray-300 hover:text-white bg-transparent border-transparent hover:bg-gray-800/20"
              }`}
            >
              <Cctv className="w-6 h-6 mr-4" />
              Visualize
            </Link>
          </div>

          <div className="mt-auto border-t border-gray-700 py-8 flex flex-col space-y-6">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className="w-full bg-gray-800/50 hover:bg-gray-700/60 border border-gray-700/50 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-between">
                  <div className="flex items-center gap-x-3">
                    {isMounted &&
                    !loading &&
                    user?.user_metadata?.avatar_url ? (
                      <Image
                        src={user.user_metadata.avatar_url}
                        alt={user.email ?? "User avatar"}
                        width={28}
                        height={28}
                        className="rounded-full"
                      />
                    ) : (
                      <UserIcon className="w-6 h-6" />
                    )}
                    <span className="truncate text-lg">
                      {isMounted && !loading && user
                        ? user.user_metadata?.full_name || "User"
                        : loading
                        ? "Loading..."
                        : "Guest"}
                    </span>
                  </div>
                  <ChevronDown className="w-5 h-5" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-full bg-black/95 border border-gray-800/50 shadow-2xl"
                side="top"
                align="start"
              >
                {isMounted && !loading && user ? (
                  <>
                    <DropdownMenuLabel className="px-4 py-3 border-b border-gray-700">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none text-white">
                          {user.email}
                        </p>
                        <p className="text-xs text-gray-400">
                          {user.user_metadata?.full_name || "User"}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => handleNavigation("/profile")}
                      className="text-gray-200 hover:bg-gray-700/60 cursor-pointer"
                    >
                      <UserIcon className="w-4 h-4 mr-2" />
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleNavigation("/trainer")}
                      className="text-gray-200 hover:bg-gray-700/60 cursor-pointer"
                    >
                      <Dumbbell className="w-4 h-4 mr-2" />
                      Trainer
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="border-gray-700" />
                    <DropdownMenuItem
                      onClick={handleSignOut}
                      className="text-red-400 hover:bg-red-900/30 cursor-pointer"
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </DropdownMenuItem>
                  </>
                ) : !isMounted || loading ? (
                  <DropdownMenuLabel className="px-4 py-3">
                    <div className="flex items-center justify-center">
                      <div className="w-5 h-5 border-2 border-gray-600 border-t-white rounded-full animate-spin"></div>
                    </div>
                  </DropdownMenuLabel>
                ) : (
                  <>
                    <DropdownMenuLabel className="px-4 py-3 border-b border-gray-700">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none text-white">
                          Welcome to MyCPTrainer
                        </p>
                        <p className="text-xs text-gray-400">
                          Sign in to access your account
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuItem
                      onClick={() => handleNavigation("/login")}
                      className="text-blue-400 hover:bg-blue-900/30 cursor-pointer"
                    >
                      <UserIcon className="w-4 h-4 mr-2" />
                      Sign In
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </>
  );
});

export default Navbar;
