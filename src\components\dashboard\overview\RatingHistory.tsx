"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useCodeforcesRatingClient } from "@/hooks/useCodeforcesRatingClient";
import { TrendingUp } from "lucide-react";
import { useMemo } from "react";
import {
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

interface RatingHistoryProps {
  handle: string;
}

// Get the color associated with a rating (matches official Codeforces colors)
const getRatingColor = (rating: number): string => {
  if (rating >= 3000) return "#8B0000"; // Legendary Grandmaster (Maroon)
  if (rating >= 2600) return "#ff0000"; // International Grandmaster (red)
  if (rating >= 2400) return "#ff0000"; // Grandmaster (red)
  if (rating >= 2300) return "#ff8c00"; // International Master (orange)
  if (rating >= 2100) return "#ff8c00"; // Master (orange)
  if (rating >= 1900) return "#aa00aa"; // Candidate Master (purple)
  if (rating >= 1600) return "#0000ff"; // Expert (blue)
  if (rating >= 1400) return "#00aaaa"; // Specialist (cyan)
  if (rating >= 1200) return "#008000"; // Pupil (green)
  return "#808080"; // Newbie (gray)
};

// Format date for display
const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString("en-US", {
    month: "short",
    year: "2-digit",
  });
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-600/50 rounded-lg p-3 shadow-xl">
        <p className="text-white font-medium">
          {formatDate(data.ratingUpdateTimeSeconds)}
        </p>
        <p className="text-blue-400">
          Rating:{" "}
          <span
            className="font-bold"
            style={{ color: getRatingColor(data.newRating) }}
          >
            {data.newRating}
          </span>
        </p>
        <p className="text-gray-300 text-sm">{data.contestName}</p>
        <p
          className={`text-sm font-medium ${
            data.ratingDelta >= 0 ? "text-green-400" : "text-red-400"
          }`}
        >
          {data.ratingDelta >= 0 ? "+" : ""}
          {data.ratingDelta}
        </p>
      </div>
    );
  }
  return null;
};

const RatingHistory: React.FC<RatingHistoryProps> = ({ handle }) => {
  // Fetch rating data
  const { data, isLoading, error } = useCodeforcesRatingClient({
    handle,
    enabled: !!handle.trim(),
  });

  // Process data for the chart
  const chartData = useMemo(() => {
    if (!data?.result?.ratingHistory) return [];

    return data.result.ratingHistory.map((contest) => ({
      ...contest,
      displayDate: formatDate(contest.ratingUpdateTimeSeconds),
    }));
  }, [data]);

  // Calculate interval for X-axis labels to show only evenly spaced dates
  const xAxisInterval = useMemo(() => {
    if (!chartData.length) return 0;
    const totalContests = chartData.length;
    const targetLabels = Math.max(1, Math.round(totalContests / 5));
    return Math.max(0, Math.ceil(totalContests / targetLabels) - 1);
  }, [chartData]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!data?.result) return null;

    const { ratingHistory, currentRating, maxRating, totalContests } =
      data.result;

    // Calculate best rank (lowest rank number is best)
    const bestRank = ratingHistory.reduce((best, contest) => {
      return contest.rank < best ? contest.rank : best;
    }, Infinity);

    // Calculate average rating change
    const totalChange = ratingHistory.reduce(
      (sum, contest) => sum + contest.ratingDelta,
      0
    );
    const avgChange =
      totalContests > 0 ? Math.round(totalChange / totalContests) : 0;

    return {
      contests: totalContests,
      bestRank: bestRank === Infinity ? 0 : bestRank,
      avgChange,
      currentRating,
      maxRating,
    };
  }, [data]);

  if (isLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <TrendingUp className="w-6 h-6 text-blue-400" />
            Rating History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data?.result?.ratingHistory?.length) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <TrendingUp className="w-6 h-6 text-blue-400" />
            Rating History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-400">No rating history available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
          <TrendingUp className="w-6 h-6 text-blue-400" />
          Rating History
        </CardTitle>
        <div className="text-center">
          <h3 className="text-lg text-gray-300 mb-4">
            <span style={{ color: getRatingColor(stats?.currentRating || 0) }}>
              {handle}
            </span>
            's Rating Progress
          </h3>
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-4 h-3 bg-blue-500 rounded-sm"></div>
            <span className="text-sm text-gray-400">Rating</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Chart */}
        <div className="h-64 mb-6">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <XAxis
                dataKey="displayDate"
                stroke="#9ca3af"
                fontSize={12}
                interval={xAxisInterval}
              />
              <YAxis
                stroke="#9ca3af"
                fontSize={12}
                domain={["dataMin - 50", "dataMax + 50"]}
                tick={false}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="newRating"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: "#3b82f6", strokeWidth: 2 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Statistics */}
        {stats && (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-white">
                {stats.contests}
              </div>
              <div className="text-sm text-gray-400">Contests</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white">
                {stats.bestRank}
              </div>
              <div className="text-sm text-gray-400">Best Rank</div>
            </div>
            <div>
              <div
                className={`text-2xl font-bold ${
                  stats.avgChange >= 0 ? "text-green-400" : "text-red-400"
                }`}
              >
                {stats.avgChange >= 0 ? "+" : ""}
                {stats.avgChange}
              </div>
              <div className="text-sm text-gray-400">Avg Change</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RatingHistory;
