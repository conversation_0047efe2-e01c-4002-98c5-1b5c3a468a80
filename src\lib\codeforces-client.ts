// ============================================================================
// CLIENT-SIDE CODEFORCES API UTILITIES
// ============================================================================
// This module provides utilities for making direct Codeforces API calls from the frontend
// without authentication. Uses public endpoints that don't require API keys.
// This approach distributes API calls across different user IPs to avoid rate limiting.

import type {
  CodeforcesApiResponse,
  CodeforcesRatingChange,
  CodeforcesSubmission,
} from "@/lib/codeforces";

// ============================================================================
// CODEFORCES USER PROFILE TYPES
// ============================================================================
// Interface for Codeforces user profile information from user.info API
export interface CodeforcesUser {
  handle: string; // Username (e.g., "tourist")
  email?: string; // Email address (optional, usually not public)
  vkId?: string; // VK social network ID (optional)
  openId?: string; // OpenID (optional)
  firstName?: string; // First name (optional)
  lastName?: string; // Last name (optional)
  country?: string; // Country (optional)
  city?: string; // City (optional)
  organization?: string; // Organization/University (optional)
  contribution: number; // Contribution points (can be negative)
  rank?: string; // Current rank title (e.g., "grandmaster", "expert")
  rating?: number; // Current rating (e.g., 2400)
  maxRank?: string; // Highest rank ever achieved
  maxRating?: number; // Highest rating ever achieved
  lastOnlineTimeSeconds: number; // Unix timestamp of last activity
  registrationTimeSeconds: number; // Unix timestamp when user registered
  friendOfCount: number; // Number of users who added this user as friend
  avatar: string; // URL to user's avatar image
  titlePhoto: string; // URL to user's title photo
}

// ============================================================================
// CLIENT-SIDE API CALLING FUNCTIONS
// ============================================================================

/**
 * Makes a direct call to Codeforces API from the client side (no authentication)
 * This function calls public Codeforces endpoints that don't require API keys
 * @param method - API method name (e.g., "user.info", "user.status")
 * @param params - Parameters for the API call
 * @returns Promise with the API response
 */
export const callCodeforcesApiClient = async (
  method: string,
  params: Record<string, string>
): Promise<CodeforcesApiResponse<any>> => {
  // Build query string from parameters
  const queryParams = new URLSearchParams(params);

  // Construct the public API URL (no authentication required)
  const url = `https://codeforces.com/api/${method}?${queryParams.toString()}`;

  try {
    // Make the HTTP request to Codeforces public API
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Parse the JSON response
    const data = await response.json();

    return data;
  } catch (error) {
    console.error(`Error calling Codeforces API (${method}):`, error);
    throw error;
  }
};

/**
 * Fetches user profile information from Codeforces user.info API
 * @param handles - Single handle or array of handles (max 10000)
 * @returns Promise with user profile data
 */
export const fetchCodeforcesUserInfo = async (
  handles: string | string[]
): Promise<CodeforcesApiResponse<CodeforcesUser[]>> => {
  // Convert single handle to array format
  const handlesArray = Array.isArray(handles) ? handles : [handles];

  // Join handles with semicolon as required by Codeforces API
  const handlesParam = handlesArray.join(";");

  return callCodeforcesApiClient("user.info", {
    handles: handlesParam,
  });
};

/**
 * Fetches user submissions from Codeforces user.status API
 * @param handle - Codeforces username
 * @param from - 1-based index of the first submission to return (optional)
 * @param count - Number of submissions to return (optional, max 100000)
 * @returns Promise with submissions data
 */
export const fetchCodeforcesSubmissions = async (
  handle: string,
  from?: number,
  count?: number
): Promise<CodeforcesApiResponse<CodeforcesSubmission[]>> => {
  const params: Record<string, string> = {
    handle: handle,
  };

  // Add optional parameters if provided
  if (from !== undefined) {
    params.from = from.toString();
  }

  if (count !== undefined) {
    params.count = count.toString();
  }

  return callCodeforcesApiClient("user.status", params);
};

/**
 * Fetches user rating history from Codeforces user.rating API
 * @param handle - Codeforces username
 * @returns Promise with rating history data
 */
export const fetchCodeforcesRating = async (
  handle: string
): Promise<CodeforcesApiResponse<CodeforcesRatingChange[]>> => {
  return callCodeforcesApiClient("user.rating", {
    handle: handle,
  });
};

// ============================================================================
// UTILITY FUNCTIONS FOR DATA PROCESSING
// ============================================================================

/**
 * Filters submissions to only include solved problems (verdict "OK")
 * and removes duplicates based on problem ID
 * @param submissions - Array of submissions
 * @returns Array of unique solved submissions
 */
export const filterSolvedSubmissions = (
  submissions: CodeforcesSubmission[]
): CodeforcesSubmission[] => {
  // Filter for solved submissions only
  const solvedSubmissions = submissions.filter(
    (submission) => submission.verdict === "OK"
  );

  // Remove duplicates - keep the first occurrence of each problem
  const uniqueSubmissions = new Map<string, CodeforcesSubmission>();

  for (const submission of solvedSubmissions) {
    // Create unique key for each problem
    const problemKey = submission.contestId
      ? `${submission.contestId}-${submission.problem.index}`
      : `${submission.problem.name}-${submission.problem.index}`;

    // Keep only the first (earliest) submission for each problem
    if (!uniqueSubmissions.has(problemKey)) {
      uniqueSubmissions.set(problemKey, submission);
    }
  }

  return Array.from(uniqueSubmissions.values());
};

/**
 * Filters submissions by date range
 * @param submissions - Array of submissions
 * @param startDate - Start date (YYYY-MM-DD format, optional)
 * @param endDate - End date (YYYY-MM-DD format, optional)
 * @returns Filtered submissions within the date range
 */
export const filterSubmissionsByDate = (
  submissions: CodeforcesSubmission[],
  startDate?: string,
  endDate?: string
): CodeforcesSubmission[] => {
  if (!startDate && !endDate) {
    return submissions;
  }

  return submissions.filter((submission) => {
    const submissionDate = new Date(submission.creationTimeSeconds * 1000);
    const submissionDateStr = submissionDate.toISOString().split("T")[0];

    // Check start date constraint
    if (startDate && submissionDateStr < startDate) {
      return false;
    }

    // Check end date constraint
    if (endDate && submissionDateStr > endDate) {
      return false;
    }

    return true;
  });
};
