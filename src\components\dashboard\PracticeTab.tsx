// "use client";

// import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
// import { BookOpen, Target, Clock, Zap, Code, Trophy, Play, CheckCircle } from "lucide-react";

// TODO: Practice tab will be implemented in the future
const PracticeTab = () => {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-400 mb-4">Practice Tab</h2>
        <p className="text-gray-500">Coming Soon...</p>
      </div>
    </div>
  );
};

// export default PracticeTab;

// TODO: Remove all the commented code below when implementing the Practice tab

/*
      {/* Quick Actions */ /*
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <button className="p-4 bg-gradient-to-br from-blue-600/20 to-blue-800/20 border border-blue-500/30 rounded-xl hover:from-blue-600/30 hover:to-blue-800/30 transition-all duration-300 group">
          <Play className="w-8 h-8 text-blue-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
          <p className="text-white font-medium">Quick Practice</p>
          <p className="text-gray-400 text-sm">Start solving now</p>
        </button>

        <button className="p-4 bg-gradient-to-br from-purple-600/20 to-purple-800/20 border border-purple-500/30 rounded-xl hover:from-purple-600/30 hover:to-purple-800/30 transition-all duration-300 group">
          <Target className="w-8 h-8 text-purple-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
          <p className="text-white font-medium">Focused Practice</p>
          <p className="text-gray-400 text-sm">Topic-based training</p>
        </button>

        <button className="p-4 bg-gradient-to-br from-green-600/20 to-green-800/20 border border-green-500/30 rounded-xl hover:from-green-600/30 hover:to-green-800/30 transition-all duration-300 group">
          <Clock className="w-8 h-8 text-green-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
          <p className="text-white font-medium">Timed Practice</p>
          <p className="text-gray-400 text-sm">Contest simulation</p>
        </button>

        <button className="p-4 bg-gradient-to-br from-yellow-600/20 to-yellow-800/20 border border-yellow-500/30 rounded-xl hover:from-yellow-600/30 hover:to-yellow-800/30 transition-all duration-300 group">
          <Trophy className="w-8 h-8 text-yellow-400 mx-auto mb-2 group-hover:scale-110 transition-transform" />
          <p className="text-white font-medium">Contest Mode</p>
          <p className="text-gray-400 text-sm">Virtual contests</p>
        </button>
      </div>

      {/* Practice Sessions */ /*
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Current Session */ /*
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-blue-400" />
              Current Session
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-slate-800/50 rounded-lg border-l-4 border-blue-400">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-white font-medium">Dynamic Programming Mastery</h3>
                  <span className="text-xs bg-blue-400/20 text-blue-400 px-2 py-1 rounded">In Progress</span>
                </div>
                <p className="text-gray-400 text-sm mb-3">Focus on classic DP patterns and optimization techniques</p>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">Progress</span>
                    <span className="text-blue-400">7/12 problems</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div className="bg-blue-400 h-2 rounded-full" style={{ width: '58%' }}></div>
                  </div>
                </div>
                <div className="flex justify-between items-center mt-4">
                  <span className="text-gray-400 text-sm">Time spent: 2h 34m</span>
                  <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors">
                    Continue
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recommended Practice */ /*
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              Recommended for You
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="text-white font-medium text-sm">Graph Algorithms</h4>
                    <p className="text-gray-400 text-xs">Based on your weak areas</p>
                  </div>
                  <div className="text-right">
                    <p className="text-yellow-400 text-sm font-semibold">15 problems</p>
                    <p className="text-gray-400 text-xs">~3h</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="text-white font-medium text-sm">Binary Search</h4>
                    <p className="text-gray-400 text-xs">Strengthen fundamentals</p>
                  </div>
                  <div className="text-right">
                    <p className="text-green-400 text-sm font-semibold">8 problems</p>
                    <p className="text-gray-400 text-xs">~1.5h</p>
                  </div>
                </div>
              </div>

              <div className="p-3 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="text-white font-medium text-sm">Number Theory</h4>
                    <p className="text-gray-400 text-xs">Expand your toolkit</p>
                  </div>
                  <div className="text-right">
                    <p className="text-purple-400 text-sm font-semibold">12 problems</p>
                    <p className="text-gray-400 text-xs">~2.5h</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Practice Statistics */ /*
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Code className="w-5 h-5 text-purple-400" />
            Practice Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-400/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Target className="w-8 h-8 text-blue-400" />
              </div>
              <p className="text-2xl font-bold text-white">156</p>
              <p className="text-gray-400 text-sm">Problems Solved</p>
              <p className="text-blue-400 text-xs">This month</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-400/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
              <p className="text-2xl font-bold text-white">89%</p>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-green-400 text-xs">Last 30 days</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-400/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
              <p className="text-2xl font-bold text-white">47h</p>
              <p className="text-gray-400 text-sm">Practice Time</p>
              <p className="text-yellow-400 text-xs">This month</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-400/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <Trophy className="w-8 h-8 text-purple-400" />
              </div>
              <p className="text-2xl font-bold text-white">12</p>
              <p className="text-gray-400 text-sm">Streak Days</p>
              <p className="text-purple-400 text-xs">Current</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Practice Sessions */ /*
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Clock className="w-5 h-5 text-cyan-400" />
            Recent Sessions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <div>
                  <p className="text-white font-medium text-sm">Array Manipulation</p>
                  <p className="text-gray-400 text-xs">5 problems solved</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-gray-300 text-sm">1h 23m</p>
                <p className="text-gray-400 text-xs">2 hours ago</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <div>
                  <p className="text-white font-medium text-sm">Tree Traversal</p>
                  <p className="text-gray-400 text-xs">3 problems solved</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-gray-300 text-sm">45m</p>
                <p className="text-gray-400 text-xs">Yesterday</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <div>
                  <p className="text-white font-medium text-sm">Contest Simulation</p>
                  <p className="text-gray-400 text-xs">Virtual contest</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-gray-300 text-sm">2h 30m</p>
                <p className="text-gray-400 text-xs">2 days ago</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
*/

export default PracticeTab;
