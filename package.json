{"name": "tracestack", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:clean": "rm -rf .next && next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@google/genai": "^1.7.0", "@monaco-editor/react": "^4.7.0", "@prisma/client": "^6.11.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^5.81.5", "@types/d3": "^7.4.3", "@types/prismjs": "^1.26.5", "axios": "^1.10.0", "babel-plugin-react-compiler": "19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "framer-motion": "^12.18.1", "lucide-react": "^0.516.0", "next": "15.3.3", "prismjs": "^1.30.0", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-simple-code-editor": "^0.14.1", "reactflow": "^11.11.4", "recharts": "^3.0.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "prisma": "^6.11.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}