import { ArrayElement } from "../types";

export function* bubbleSort(
  array: ArrayElement[]
): Generator<ArrayElement[], void, void> {
  const arr = [...array];
  const n = arr.length;

  for (let i = 0; i < n - 1; i++) {
    for (let j = 0; j < n - i - 1; j++) {
      // Highlight elements being compared
      arr[j].isComparing = true;
      arr[j + 1].isComparing = true;
      yield [...arr];
      arr[j].isComparing = false;
      arr[j + 1].isComparing = false;

      // Check if swap is needed
      if (arr[j].value > arr[j + 1].value) {
        // Highlight elements being swapped
        arr[j].isSwapping = true;
        arr[j + 1].isSwapping = true;
        yield [...arr];

        // Perform the swap
        [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];
        yield [...arr];

        // Clear swap indicator
        arr[j].isSwapping = false;
        arr[j + 1].isSwapping = false;
      }
    }
    // Mark the last element of this pass as sorted
    arr[n - 1 - i].isSorted = true;
    yield [...arr];
  }

  // Mark the first element as sorted if not already
  if (n > 0) {
    arr[0].isSorted = true;
  }

  // Final yield to show the fully sorted array
  yield [...arr];
}
