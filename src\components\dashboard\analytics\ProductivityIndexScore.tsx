"use client";

import { useCodeforcesRatingClient } from "@/hooks/useCodeforcesRatingClient";
import {
  useAllSolvedQuestionsClient,
  useAllSubmissionsClient,
} from "@/hooks/useCodeforcesSubmissionsClient";
import { useAuth } from "@/lib/auth-context";
import { useQuery } from "@tanstack/react-query";
import { Target } from "lucide-react";
import { useMemo } from "react";

// Types for user data
interface UserProfile {
  id: number;
  handle: string;
  rating?: number;
  rank?: string;
  streak?: number;
  training_streak: number;
  max_training_streak: number;
  maxSheetSlots?: number;
  createdAt: string;
  updatedAt: string;
  email: string;
  totalSheets: number;
  availableSheetSlots: number;
  sheets: Array<{
    id: number;
    name: string;
    createdAt: string;
  }>;
  supabaseUser: {
    id: string;
    email: string;
    created_at: string;
    user_metadata: any;
  };
}

// Productivity Index calculation utilities
const calculateProductivityIndex = (
  solvedSubmissions: any[],
  allSubmissions: any[],
  contestHistory: any[]
) => {
  // Get the most recent week (current day to 6 days ago)
  const endDate = new Date();
  endDate.setHours(23, 59, 59, 999); // End of current day

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 6); // 6 days ago
  startDate.setHours(0, 0, 0, 0); // Start of that day

  // Filter submissions for the recent week
  const weekSubmissions = allSubmissions.filter((submission) => {
    const submissionDate = new Date(submission.creationTimeSeconds * 1000);
    return submissionDate >= startDate && submissionDate <= endDate;
  });

  const weekSolvedSubmissions = solvedSubmissions.filter((submission) => {
    const submissionDate = new Date(submission.creationTimeSeconds * 1000);
    return submissionDate >= startDate && submissionDate <= endDate;
  });

  // 1. Number of Problems Solved (Max: 30)
  const uniqueSolvedProblems = new Map();
  weekSolvedSubmissions.forEach((submission) => {
    const problemId = `${submission.problem.contestId || "unknown"}-${
      submission.problem.index
    }`;
    if (!uniqueSolvedProblems.has(problemId)) {
      uniqueSolvedProblems.set(problemId, submission.problem);
    }
  });
  const problemsSolved = uniqueSolvedProblems.size;
  const problemsScore = Math.min(30, problemsSolved * 1.2);

  // 2. Activity Rate (Max: 14) - 2 points per day with submissions
  const activeDays = new Set();
  weekSubmissions.forEach((submission) => {
    const submissionDate = new Date(submission.creationTimeSeconds * 1000);
    const dateStr = submissionDate.toISOString().split("T")[0];
    activeDays.add(dateStr);
  });
  const activityScore = activeDays.size * 2;

  // 3. Difficulty Level (Max: 20)
  let difficultyScore = 0;
  if (uniqueSolvedProblems.size > 0) {
    let totalWeightedScore = 0;
    let maxPossibleScore = 0;

    uniqueSolvedProblems.forEach((problem) => {
      const rating = problem.rating || 0;
      let weight = 1; // easy
      if (rating >= 1200 && rating <= 1599) weight = 2; // medium
      else if (rating >= 1600) weight = 3; // hard

      totalWeightedScore += weight;
      maxPossibleScore += 3; // max weight
    });

    difficultyScore =
      maxPossibleScore > 0 ? (totalWeightedScore / maxPossibleScore) * 20 : 0;
  }

  // 4. Tag Variety (Max: 15)
  const uniqueTags = new Set();
  uniqueSolvedProblems.forEach((problem) => {
    if (problem.tags) {
      problem.tags.forEach((tag: string) => uniqueTags.add(tag));
    }
  });
  const tagCount = uniqueTags.size;
  let tagScore = 5; // ≤3 tags
  if (tagCount >= 4 && tagCount <= 6) tagScore = 10;
  else if (tagCount >= 7) tagScore = 15;

  // 5. Contest Participation (Max: 15)
  const weekContests = contestHistory.filter((contest) => {
    const contestDate = new Date(contest.ratingUpdateTimeSeconds * 1000);
    return contestDate >= startDate && contestDate <= endDate;
  });
  let contestScore = 0;
  if (weekContests.length === 1) contestScore = 10;
  else if (weekContests.length >= 2) contestScore = 15;

  const totalScore = Math.round(
    problemsScore + activityScore + difficultyScore + tagScore + contestScore
  );

  return {
    totalScore: Math.min(100, totalScore),
    breakdown: {
      problems: Math.round(problemsScore),
      activity: activityScore,
      difficulty: Math.round(difficultyScore),
      tags: tagScore,
      contests: contestScore,
    },
    metrics: {
      problemsSolved,
      activeDays: activeDays.size,
      uniqueTags: tagCount,
      contestsParticipated: weekContests.length,
    },
  };
};

const getScoreFeedback = (score: number) => {
  if (score >= 90)
    return {
      emoji: "🚀",
      text: "Elite Productivity",
      color: "text-yellow-400",
    };
  if (score >= 75)
    return { emoji: "🧠", text: "Great Consistency", color: "text-green-400" };
  if (score >= 50)
    return { emoji: "🔄", text: "Steady Progress", color: "text-blue-400" };
  return { emoji: "⚡", text: "Let's Bounce Back", color: "text-orange-400" };
};

// Helper function to get progress bar color based on percentage
const getProgressBarColor = (percentage: number) => {
  if (percentage >= 70) {
    return "bg-gradient-to-r from-green-500 to-green-400"; // High progress - Green
  } else if (percentage >= 40) {
    return "bg-gradient-to-r from-yellow-500 to-yellow-400"; // Moderate progress - Yellow
  } else {
    return "bg-gradient-to-r from-red-500 to-red-400"; // Low progress - Red
  }
};

const ProductivityIndexScore = () => {
  const { user, loading: authLoading } = useAuth();

  // Memoize user email to prevent unnecessary query key changes
  const userEmail = useMemo(() => user?.email, [user?.email]);

  // Fetch user profile data from our API with optimized configuration
  const { data: userProfile, isLoading: profileLoading } =
    useQuery<UserProfile>({
      queryKey: ["userProfile", userEmail],
      queryFn: async () => {
        const response = await fetch("/api/userInfo");
        if (!response.ok) {
          throw new Error("Failed to fetch user profile");
        }
        return response.json();
      },
      enabled: !!userEmail && !authLoading,
      staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
      refetchOnWindowFocus: false,
    });

  // Get Codeforces handle from user profile
  const codeforcesHandle = userProfile?.handle || "";

  // Fetch all solved questions for the user
  const { data: allSolvedData, isLoading: allSolvedLoading } =
    useAllSolvedQuestionsClient({
      handle: codeforcesHandle,
      enabled: !!codeforcesHandle.trim(),
    });

  // Fetch all submissions (including failed ones) for activity tracking
  const { data: allSubmissionsData, isLoading: allSubmissionsLoading } =
    useAllSubmissionsClient({
      handle: codeforcesHandle,
      enabled: !!codeforcesHandle.trim(),
    });

  // Fetch rating history for contest participation
  const { data: ratingData, isLoading: ratingLoading } =
    useCodeforcesRatingClient({
      handle: codeforcesHandle,
      enabled: !!codeforcesHandle.trim(),
    });

  // Calculate productivity index
  const productivityData = useMemo(() => {
    if (
      !allSolvedData?.result?.submissions ||
      !allSubmissionsData?.result?.submissions ||
      !ratingData?.result?.ratingHistory
    ) {
      return null;
    }

    return calculateProductivityIndex(
      allSolvedData.result.submissions,
      allSubmissionsData.result.submissions,
      ratingData.result.ratingHistory
    );
  }, [allSolvedData, allSubmissionsData, ratingData]);

  const isLoading =
    profileLoading ||
    allSolvedLoading ||
    allSubmissionsLoading ||
    ratingLoading;
  const feedback = productivityData
    ? getScoreFeedback(productivityData.totalScore)
    : null;

  return (
    <div className="relative w-full">
      <div className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl rounded-lg overflow-hidden">
        {/* Content */}
        <div className="relative z-10 p-8">
          {/* Header */}
          <div className="flex items-center gap-3 mb-6">
            <Target className="w-6 h-6 text-blue-400" />
            <div>
              <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                Productivity Index Score
              </h3>
              <p className="text-gray-400 text-sm font-medium">
                Last 7 Days (Including Today)
              </p>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : productivityData && feedback ? (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
              {/* Left: Main Score Display */}
              <div className="text-center lg:text-left">
                <div className="relative inline-block">
                  <div className="text-6xl font-black text-white mb-2">
                    {productivityData.totalScore}
                  </div>
                  <div className="text-lg text-gray-400 font-medium -mt-2">
                    / 100 points
                  </div>
                </div>
                <div
                  className={`mt-4 text-xl font-semibold ${feedback.color} flex items-center justify-center lg:justify-start gap-3`}
                >
                  <span className="text-2xl">{feedback.emoji}</span>
                  <span className="text-white">{feedback.text}</span>
                </div>
              </div>

              {/* Right: Metrics with Progress Bars */}
              <div className="lg:col-span-2 space-y-4">
                {/* Problems Solved */}
                <div className="group">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-200 font-medium">
                      Problems Solved
                    </span>
                    <span className="text-white font-bold text-lg">
                      {productivityData.breakdown.problems}
                      <span className="text-gray-400">/30</span>
                    </span>
                  </div>
                  <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden border border-gray-600">
                    <div
                      className={`h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColor(
                        (productivityData.breakdown.problems / 30) * 100
                      )}`}
                      style={{
                        width: `${
                          (productivityData.breakdown.problems / 30) * 100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Activity Rate */}
                <div className="group">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-200 font-medium">
                      Activity Rate
                    </span>
                    <span className="text-white font-bold text-lg">
                      {productivityData.breakdown.activity}
                      <span className="text-gray-400">/14</span>
                    </span>
                  </div>
                  <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden border border-gray-600">
                    <div
                      className={`h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColor(
                        (productivityData.breakdown.activity / 14) * 100
                      )}`}
                      style={{
                        width: `${
                          (productivityData.breakdown.activity / 14) * 100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Difficulty Level */}
                <div className="group">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-200 font-medium">
                      Difficulty Level
                    </span>
                    <span className="text-white font-bold text-lg">
                      {productivityData.breakdown.difficulty}
                      <span className="text-gray-400">/20</span>
                    </span>
                  </div>
                  <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden border border-gray-600">
                    <div
                      className={`h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColor(
                        (productivityData.breakdown.difficulty / 20) * 100
                      )}`}
                      style={{
                        width: `${
                          (productivityData.breakdown.difficulty / 20) * 100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Tag Variety */}
                <div className="group">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-200 font-medium">
                      Tag Variety
                    </span>
                    <span className="text-white font-bold text-lg">
                      {productivityData.breakdown.tags}
                      <span className="text-gray-400">/15</span>
                    </span>
                  </div>
                  <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden border border-gray-600">
                    <div
                      className={`h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColor(
                        (productivityData.breakdown.tags / 15) * 100
                      )}`}
                      style={{
                        width: `${
                          (productivityData.breakdown.tags / 15) * 100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Contest Participation */}
                <div className="group">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-200 font-medium">
                      Contest Participation
                    </span>
                    <span className="text-white font-bold text-lg">
                      {productivityData.breakdown.contests}
                      <span className="text-gray-400">/15</span>
                    </span>
                  </div>
                  <div className="relative h-3 bg-gray-800 rounded-full overflow-hidden border border-gray-600">
                    <div
                      className={`h-full rounded-full transition-all duration-500 ease-out ${getProgressBarColor(
                        (productivityData.breakdown.contests / 15) * 100
                      )}`}
                      style={{
                        width: `${
                          (productivityData.breakdown.contests / 15) * 100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-400 py-12">
              <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600 inline-block">
                <p className="text-lg font-medium text-white">
                  No Codeforces handle found
                </p>
                <p className="text-sm text-gray-400 mt-1">
                  Please set up your profile to see analytics
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductivityIndexScore;
