"use client";

import { Clock, Dumbbell } from "lucide-react";

const TrainerPage = () => {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-8">
      <div className="text-center max-w-2xl mx-auto">
        <div className="mb-8">
          <div className="w-24 h-24 bg-gradient-to-br from-cyan-600 to-blue-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <Dumbbell className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            Trainer
          </h1>
          <p className="text-xl text-gray-300 mb-8">
            Advanced competitive programming training features
          </p>
        </div>

        <div className="bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-900/40 backdrop-blur-lg border border-slate-600/30 rounded-2xl p-8 shadow-xl">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Clock className="w-6 h-6 text-cyan-400" />
            <h2 className="text-2xl font-semibold text-white">Coming Soon</h2>
          </div>
          <p className="text-gray-400 text-lg leading-relaxed">
            We're working hard to bring you an amazing training experience with
            personalized problem recommendations, skill assessments, and more to
            help you improve your competitive programming skills.
          </p>
          <div className="mt-6 text-sm text-gray-500">
            Stay tuned for updates!
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainerPage;
