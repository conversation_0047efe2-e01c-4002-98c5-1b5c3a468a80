// ============================================================================
// POPUP OAUTH CALLBACK HANDLER - AUTHENTICATION CODE EXCHANGE
// ============================================================================
//
// 🎯 PURPOSE:
// This page runs inside the popup window during Google OAuth authentication.
// It receives the authorization code from Google and handles the secure exchange
// for a Supabase session, then communicates the result back to the main window.
//
// 🚨 CRITICAL AUTHENTICATION FIXES IMPLEMENTED:
//
// 1. PKCE CODE EXCHANGE LOCATION (Major Fix):
//    - Problem: Main window tried to exchange auth code without PKCE verifier
//    - Root Cause: PKCE verifier stored in popup's session, not accessible to main window
//    - Solution: Exchange code HERE in popup context where verifier is available
//    - Result: Eliminates "invalid request: both auth code and code verifier should be non-empty"
//
// 2. DUAL CODE PROCESSING PREVENTION (Major Fix):
//    - Problem: Same auth code processed by multiple mechanisms simultaneously
//    - Causes: Race conditions, "code already used" errors, false failures
//    - Solution: Check for existing session before attempting code exchange
//    - Fallback: If code fails but session exists, treat as success
//
// 3. ROBUST ERROR HANDLING (UX Fix):
//    - Problem: Real auth successes reported as failures due to technical errors
//    - Solution: Always check session state when code exchange fails
//    - Benefit: Users see success even if underlying exchange has issues
//
// 🔄 POPUP AUTHENTICATION FLOW (THIS FILE'S ROLE):
//
// Step 1: POPUP WINDOW OPENS
//    - User clicks login in main window
//    - Popup navigates to Google OAuth
//    - Google redirects to THIS page with auth code
//    ↓
// Step 2: URL PARAMETER PROCESSING
//    - Extract 'code' (success) or 'error' (failure) from URL
//    - Handle OAuth provider errors (access_denied, etc.)
//    ↓
// Step 3: SESSION CHECK (DUAL PROCESSING PREVENTION)
//    - Check if we already have a valid session
//    - If yes: Skip code exchange, send existing session to main window
//    - If no: Proceed with code exchange
//    ↓
// Step 4: CODE EXCHANGE (CORE AUTHENTICATION)
//    - Call supabase.auth.exchangeCodeForSession(code)
//    - PKCE verifier available in this context (popup's session storage)
//    - Handle both success and various failure scenarios
//    ↓
// Step 5: ERROR RECOVERY (ROBUST HANDLING)
//    - If code exchange fails with PKCE error, check session again
//    - Code might have been used by another process but auth succeeded
//    - Prioritize session existence over code exchange success
//    ↓
// Step 6: COMMUNICATION WITH MAIN WINDOW
//    - Send success/error message via BroadcastChannel
//    - Include session data for successful authentications
//    - Close popup window after message delivery
//
// 🛡️ ANTI-PATTERN SOLUTIONS:
//
// 1. PREVENTING DUPLICATE CODE EXCHANGE:
//    ```typescript
//    // Check existing session first
//    const { data: { session: existingSession } } = await supabase.auth.getSession();
//    if (existingSession?.user) {
//      // Already authenticated, no need to exchange code
//      return sendSuccessToMainWindow(existingSession);
//    }
//    ```
//
// 2. HANDLING "CODE ALREADY USED" GRACEFULLY:
//    ```typescript
//    if (exchangeError.message?.includes("auth code and code verifier")) {
//      // Check if session exists despite error
//      const { data: { session } } = await supabase.auth.getSession();
//      if (session?.user) {
//        return sendSuccessToMainWindow(session); // Auth actually succeeded
//      }
//    }
//    ```
//
// 3. PRIORITIZING SESSION STATE OVER CODE EXCHANGE:
//    - Philosophy: "If user has valid session, authentication succeeded"
//    - Implementation: Always check session state when code exchange fails
//    - Benefit: Handles edge cases and timing issues gracefully
//
// 📡 COMMUNICATION PROTOCOL:
//
// Messages sent to main window via BroadcastChannel:
//
// 1. SUCCESS:
//    ```typescript
//    {
//      type: "OAUTH_SESSION_SUCCESS",
//      session: validSessionObject
//    }
//    ```
//
// 2. ERROR:
//    ```typescript
//    {
//      type: "OAUTH_ERROR",
//      error: "session_exchange_failed",
//      errorDescription: detailedErrorMessage,
//      errorCode: standardizedErrorCode
//    }
//    ```
//
// 🔍 DEBUGGING AUTHENTICATION ISSUES:
//
// 1. Check console logs for:
//    - "Session check failed, proceeding with code exchange"
//    - "Second session check failed"
//    - "Authentication successful! Session already established"
//
// 2. Common scenarios:
//    - Code exchange succeeds: Normal flow
//    - Code already used but session exists: Recovery flow (success)
//    - Code exchange fails and no session: True failure
//
// 3. Network tab checks:
//    - Look for exchangeCodeForSession API calls
//    - Verify session establishment requests
//    - Check for multiple attempts (indicates race condition)
//
// 🏗️ ARCHITECTURE BENEFITS:
//
// 1. SECURITY: Code exchange happens where PKCE verifier is available
// 2. RELIABILITY: Multiple fallback mechanisms for edge cases
// 3. UX: Users see success even when technical details fail
// 4. MAINTAINABILITY: Clear separation of concerns between windows
//
// 📚 RELATED FILES:
// - /lib/auth-popup.ts: Main window authentication controller
// - /lib/auth-context.tsx: Global authentication state management
// - /components/codeforces/ProblemsDisplay.tsx: Authentication trigger and result handling
//
// ⚠️ IMPORTANT NOTES:
//
// 1. This page MUST run in popup context for PKCE to work correctly
// 2. BroadcastChannel communication is essential for cross-window messaging
// 3. Always close popup after sending message to prevent memory leaks
// 4. Session checks are critical for handling race conditions
//
// ============================================================================

"use client";

import { AUTH_ERROR_CODES } from "@/lib/auth-popup";
import { createClient } from "@/lib/supabase/client";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

/**
 * Loading component displayed while Suspense is resolving
 */
function LoadingSpinner() {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <div className="text-center max-w-md">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-sm text-gray-400">Initializing authentication...</p>
      </div>
    </div>
  );
}

/**
 * Popup Callback Page Component that uses useSearchParams
 *
 * This component handles the OAuth callback for popup-based authentication.
 * It receives the authorization code from the OAuth provider and sends it
 * back to the parent window using BroadcastChannel API, then closes the popup.
 */
function PopupCallbackContent() {
  const [mounted, setMounted] = useState(false);
  const [status, setStatus] = useState<{
    type: "loading" | "success" | "error";
    message: string;
  }>({ type: "loading", message: "Processing authentication..." });

  const searchParams = useSearchParams();

  const code = searchParams?.get("code");
  const error = searchParams?.get("error");
  const errorDescription = searchParams?.get("error_description");

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    const handleCallback = async () => {
      let channel: BroadcastChannel | null = null;

      try {
        // Check if BroadcastChannel is supported
        if (!("BroadcastChannel" in window)) {
          setStatus({
            type: "error",
            message:
              "Your browser doesn't support required features. Please update your browser.",
          });
          // Try closing after a delay to show the error message
          setTimeout(() => window.close(), 3000);
          return;
        }

        // Create a BroadcastChannel to communicate with the parent window
        channel = new BroadcastChannel("supabase-auth-popup");

        // Handle OAuth errors from the provider (highest priority)
        if (error) {
          setStatus({
            type: "error",
            message: errorDescription || "Authentication failed",
          });

          // Categorize the error
          let errorCode: string = AUTH_ERROR_CODES.OAUTH_ERROR;
          if (error === "access_denied") {
            errorCode = AUTH_ERROR_CODES.USER_CANCELLED;
          }

          // Send error back to parent window
          channel.postMessage({
            type: "OAUTH_ERROR",
            error,
            errorDescription: errorDescription || "Authentication failed",
            errorCode,
          });

          // Wait a bit to ensure the message is processed before closing
          setTimeout(() => window.close(), 500);
          return;
        }

        // Handle authorization code (second priority)
        if (code) {
          setStatus({
            type: "loading",
            message: "Exchanging authorization code...",
          });

          // First check if we already have a session (to prevent dual processing)
          const supabase = createClient();
          try {
            const {
              data: { session: existingSession },
            } = await supabase.auth.getSession();

            if (existingSession && existingSession.user) {
              // We already have a valid session, no need to exchange code again
              setStatus({
                type: "success",
                message:
                  "Authentication successful! Session already established.",
              });

              // Authentication successful - session already exists
              // Database user record creation has been removed

              // Send success with existing session data to parent
              channel.postMessage({
                type: "OAUTH_SESSION_SUCCESS",
                session: existingSession,
              });

              setTimeout(() => window.close(), 500);
              return;
            }
          } catch (sessionCheckError) {
            console.log(
              "Session check failed, proceeding with code exchange:",
              sessionCheckError
            );
          }

          // Exchange the code for a session HERE where the PKCE verifier is available
          try {
            const { data: session, error: exchangeError } =
              await supabase.auth.exchangeCodeForSession(code);

            if (exchangeError) {
              // Check if error is due to code already being used
              const isCodeAlreadyUsed =
                exchangeError.message?.includes(
                  "auth code and code verifier"
                ) ||
                exchangeError.message?.includes("invalid request") ||
                exchangeError.message?.includes("already been used");

              if (isCodeAlreadyUsed) {
                // Code might have been used by another process, check for session again
                try {
                  const {
                    data: { session: laterSession },
                  } = await supabase.auth.getSession();
                  if (laterSession && laterSession.user) {
                    // Authentication actually succeeded, just the code was already used
                    setStatus({
                      type: "success",
                      message:
                        "Authentication successful! Session established.",
                    });

                    // Authentication successful - session recovered
                    // Database user record creation has been removed

                    channel.postMessage({
                      type: "OAUTH_SESSION_SUCCESS",
                      session: laterSession,
                    });

                    setTimeout(() => window.close(), 500);
                    return;
                  }
                } catch (secondSessionCheck) {
                  console.error(
                    "Second session check failed:",
                    secondSessionCheck
                  );
                }
              }

              setStatus({
                type: "error",
                message: "Failed to establish session. Please try again.",
              });

              // Send error back to parent window
              channel.postMessage({
                type: "OAUTH_ERROR",
                error: "session_exchange_failed",
                errorDescription: exchangeError.message,
                errorCode: AUTH_ERROR_CODES.SESSION_ERROR,
              });

              setTimeout(() => window.close(), 500);
              return;
            }

            // Successfully exchanged code for session
            setStatus({
              type: "success",
              message: "Authentication successful! Completing sign-in...",
            });

            // Authentication successful - code exchange completed
            // Database user record creation has been removed

            // Send success with session data to parent
            channel.postMessage({
              type: "OAUTH_SESSION_SUCCESS",
              session: session.session,
            });

            // Wait a bit to ensure the message is processed before closing
            setTimeout(() => window.close(), 500);
            return;
          } catch (error) {
            setStatus({
              type: "error",
              message: "An error occurred during authentication.",
            });

            // Send error back to parent window
            channel.postMessage({
              type: "OAUTH_ERROR",
              error: "code_exchange_exception",
              errorDescription:
                error instanceof Error ? error.message : "Unknown error",
              errorCode: AUTH_ERROR_CODES.SESSION_ERROR,
            });

            setTimeout(() => window.close(), 500);
            return;
          }
        }

        // If no code or error, check for existing session (fallback)
        setStatus({
          type: "loading",
          message: "Checking authentication status...",
        });

        const supabase = createClient();

        // Simplified session check with fewer attempts to reduce timing issues
        let attempts = 0;
        const maxAttempts = 5; // Reduced from 10
        const checkInterval = 800; // Increased from 500ms for more stability

        const checkSession = async (): Promise<boolean> => {
          try {
            const {
              data: { session },
              error: sessionError,
            } = await supabase.auth.getSession();

            if (sessionError) {
              console.error("Session check error:", sessionError);

              // If session errors persist after a few attempts, give up
              if (attempts >= 2) {
                setStatus({
                  type: "error",
                  message: "Failed to verify authentication. Please try again.",
                });

                channel?.postMessage({
                  type: "OAUTH_ERROR",
                  error: "session_error",
                  errorDescription: sessionError.message,
                  errorCode: AUTH_ERROR_CODES.SESSION_ERROR,
                });

                setTimeout(() => window.close(), 500);
                return false;
              }

              // Try again
              attempts++;
              setTimeout(() => checkSession(), checkInterval);
              return false;
            }

            if (session && session.user) {
              // We have a valid session, authentication was successful
              setStatus({
                type: "success",
                message: "Authentication successful! Completing sign-in...",
              });

              // Authentication successful - session check completed
              // Database user record creation has been removed

              channel?.postMessage({
                type: "OAUTH_SESSION_SUCCESS",
                session,
              });

              // Wait a bit to ensure the message is processed before closing
              setTimeout(() => window.close(), 500);
              return true;
            }

            attempts++;
            if (attempts < maxAttempts) {
              // Update status to show we're still trying
              setStatus({
                type: "loading",
                message: `Checking authentication status... (${attempts}/${maxAttempts})`,
              });

              // Wait and try again
              setTimeout(() => checkSession(), checkInterval);
              return false;
            } else {
              // Max attempts reached, session might not be available yet
              // This is often normal - the main window auth state listener will catch it
              setStatus({
                type: "success", // Changed from error to success
                message: "Authentication initiated. Completing sign-in...",
              });

              // Don't send an error - let the main window handle session detection
              // Just close the popup gracefully
              setTimeout(() => window.close(), 500);
              return false;
            }
          } catch (err) {
            console.error("Session check exception:", err);
            attempts++;

            if (attempts < maxAttempts) {
              // Continue trying on exceptions
              setTimeout(() => checkSession(), checkInterval);
              return false;
            } else {
              // Too many exceptions - close gracefully and let main window handle it
              setStatus({
                type: "success", // Changed from error to success
                message: "Authentication initiated. Completing sign-in...",
              });

              // Don't send error - let main window auth state listener handle it
              setTimeout(() => window.close(), 500);
              return false;
            }
          }
        };

        await checkSession();
      } catch (err) {
        console.error("Popup callback - Critical error:", err);

        setStatus({
          type: "error",
          message: "An unexpected error occurred. Please try again.",
        });

        // Handle any errors during the process
        try {
          if (!channel && "BroadcastChannel" in window) {
            channel = new BroadcastChannel("supabase-auth-popup");
          }

          channel?.postMessage({
            type: "OAUTH_ERROR",
            error: "callback_error",
            errorDescription:
              err instanceof Error ? err.message : "Unknown error occurred",
            errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
          });
        } catch (channelError) {
          console.error("Failed to send error message:", channelError);
        }

        setTimeout(() => window.close(), 500);
      } finally {
        // Clean up the channel
        try {
          channel?.close();
        } catch (cleanupError) {
          console.error("Channel cleanup error:", cleanupError);
        }
      }
    };

    handleCallback();
  }, [mounted, code, error, errorDescription]);

  if (!mounted) {
    return null;
  }

  const getStatusIcon = () => {
    switch (status.type) {
      case "loading":
        return (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        );
      case "success":
        return (
          <div className="rounded-full h-8 w-8 bg-green-500 flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-5 h-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
        );
      case "error":
        return (
          <div className="rounded-full h-8 w-8 bg-red-500 flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-5 h-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <div className="text-center max-w-md">
        {getStatusIcon()}
        <p
          className={`text-sm ${
            status.type === "error" ? "text-red-400" : "text-gray-400"
          }`}
        >
          {status.message}
        </p>
        {status.type === "error" && (
          <p className="text-xs text-gray-500 mt-2">
            This window will close automatically.
          </p>
        )}
      </div>
    </div>
  );
}

/**
 * Main Page Component with Suspense Wrapper
 *
 * Wraps the component that uses useSearchParams in a Suspense boundary
 * to satisfy Next.js static generation requirements.
 */
export default function PopupCallback() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <PopupCallbackContent />
    </Suspense>
  );
}
