# 🚀 Prisma Production Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying TraceStack with a production-ready Prisma setup, following official best practices and optimized for serverless environments.

## 🔧 Pre-Deployment Checklist

### ✅ Database Configuration

- [ ] **Database URL Optimization**: Using pooled connections for production
- [ ] **Connection Limits**: Set to 1 for serverless environments
- [ ] **SSL Configuration**: Enabled for production databases
- [ ] **Timeout Settings**: Configured for serverless cold starts
- [ ] **Environment Variables**: Properly configured and validated

### ✅ Prisma Client Setup

- [ ] **Global Instance**: Using singleton pattern to prevent multiple connections
- [ ] **Error Handling**: Comprehensive error handling implemented
- [ ] **Logging**: Production-appropriate log levels configured
- [ ] **Health Checks**: Database health monitoring enabled

### ✅ Performance Optimization

- [ ] **Connection Pooling**: Optimized for your deployment platform
- [ ] **Query Optimization**: Efficient queries with proper indexing
- [ ] **Caching Strategy**: Implemented where appropriate
- [ ] **Monitoring**: Performance monitoring configured

## 🌐 Environment-Specific Configuration

### Development Environment

```bash
# .env.local
NODE_ENV="development"
DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
DATABASE_LOG_LEVEL="query"
DATABASE_ENABLE_METRICS="true"
```

**Development Features:**
- Direct database connections (faster for single connections)
- Comprehensive query logging
- Hot-reload protection
- Enhanced error messages

### Production Environment

```bash
# .env (or environment variables in your deployment platform)
NODE_ENV="production"
DATABASE_URL="postgresql://postgres:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20&connect_timeout=15"
DATABASE_LOG_LEVEL="error"
DATABASE_ENABLE_METRICS="true"
```

**Production Features:**
- Pooled database connections
- Error-only logging
- Connection pool optimization
- Enhanced security

## 🏗️ Deployment Platforms

### Vercel Deployment

1. **Environment Variables Setup:**
   ```bash
   # In Vercel dashboard or via CLI
   vercel env add DATABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```

2. **Vercel Configuration:**
   ```json
   // vercel.json
   {
     "functions": {
       "app/api/**/*.js": {
         "maxDuration": 30
       }
     },
     "env": {
       "DATABASE_CONNECTION_LIMIT": "1",
       "DATABASE_POOL_TIMEOUT": "20"
     }
   }
   ```

3. **Build Configuration:**
   ```bash
   # Ensure Prisma client is generated during build
   npm run build
   npx prisma generate
   ```

### Netlify Deployment

1. **Build Settings:**
   ```toml
   # netlify.toml
   [build]
     command = "npm run build"
     publish = ".next"

   [build.environment]
     NODE_ENV = "production"
     DATABASE_CONNECTION_LIMIT = "1"

   [[plugins]]
     package = "@netlify/plugin-nextjs"
   ```

2. **Function Configuration:**
   ```javascript
   // netlify/functions/api.js
   import { withDatabase } from '../../src/lib/database';
   
   export const handler = withDatabase(async (event, context) => {
     // Your API logic here
   });
   ```

### Railway Deployment

1. **Railway Configuration:**
   ```bash
   # Railway automatically detects Next.js
   # Set environment variables in Railway dashboard
   ```

2. **Database Setup:**
   ```bash
   # If using Railway PostgreSQL
   railway add postgresql
   railway variables set DATABASE_URL=$DATABASE_URL
   ```

## 🔒 Security Best Practices

### Environment Variables Security

1. **Never commit sensitive data:**
   ```bash
   # .gitignore
   .env
   .env.local
   .env.production
   ```

2. **Use different credentials per environment:**
   ```bash
   # Development
   DATABASE_URL_DEV="postgresql://dev_user:dev_pass@..."
   
   # Production
   DATABASE_URL_PROD="postgresql://prod_user:prod_pass@..."
   ```

3. **Rotate credentials regularly:**
   - Change database passwords quarterly
   - Update API keys when team members leave
   - Monitor access logs for suspicious activity

### Database Security

1. **Connection Security:**
   - Always use SSL in production
   - Implement IP whitelisting where possible
   - Use connection pooling to prevent exhaustion attacks

2. **Query Security:**
   - Prisma provides built-in SQL injection protection
   - Validate all user inputs before database operations
   - Use Prisma's type-safe queries

## 📊 Monitoring and Observability

### Health Checks

```typescript
// app/api/health/route.ts
import { dbUtils } from '@/lib/database';

export async function GET() {
  try {
    const status = await dbUtils.getStatus();
    return Response.json(status);
  } catch (error) {
    return Response.json({ error: 'Health check failed' }, { status: 500 });
  }
}
```

### Performance Monitoring

1. **Database Metrics:**
   ```typescript
   // Monitor connection pool usage
   const poolStatus = await getConnectionPoolStatus();
   
   // Track query performance
   const healthCheck = await performHealthCheck();
   ```

2. **Error Tracking:**
   ```typescript
   // Implement error tracking
   import { handlePrismaError } from '@/lib/database';
   
   try {
     await prisma.users.create(data);
   } catch (error) {
     const dbError = handlePrismaError(error);
     // Send to error tracking service
   }
   ```

### Logging Configuration

```typescript
// Production logging setup
const prisma = new PrismaClient({
  log: [
    { level: 'error', emit: 'event' },
    { level: 'warn', emit: 'event' },
  ],
});

prisma.$on('error', (e) => {
  console.error('Database error:', e);
  // Send to logging service
});
```

## 🚨 Troubleshooting Common Issues

### Connection Pool Exhaustion

**Symptoms:**
- "Too many connections" errors
- Slow response times
- Timeout errors

**Solutions:**
```typescript
// Check connection pool status
const diagnostics = await dbUtils.diagnose();

// Optimize connection limits
DATABASE_URL="...?connection_limit=1&pool_timeout=20"

// Implement connection retry logic
const result = await withRetry(async () => {
  return await prisma.users.findMany();
});
```

### Slow Query Performance

**Symptoms:**
- High response times
- Database timeouts
- Poor user experience

**Solutions:**
```sql
-- Add database indexes
CREATE INDEX idx_users_email ON "Users"(email);
CREATE INDEX idx_users_handle ON "Users"(handle);
CREATE INDEX idx_sheets_user_id ON "Sheet"("userId");
```

```typescript
// Optimize queries with select/include
const users = await prisma.users.findMany({
  select: {
    id: true,
    email: true,
    handle: true,
    // Only select needed fields
  },
});
```

### SSL/TLS Connection Issues

**Symptoms:**
- "SSL connection failed" errors
- Certificate validation errors

**Solutions:**
```bash
# For Supabase
DATABASE_URL="postgresql://...?sslmode=require"

# For other providers
DATABASE_URL="postgresql://...?ssl=true&sslmode=require"
```

## 📈 Performance Optimization

### Query Optimization

1. **Use efficient queries:**
   ```typescript
   // Good: Single query with join
   const userWithSheets = await prisma.users.findFirst({
     where: { email },
     include: { sheets: true },
   });
   
   // Bad: Multiple queries
   const user = await prisma.users.findFirst({ where: { email } });
   const sheets = await prisma.sheet.findMany({ where: { userId: user.id } });
   ```

2. **Implement pagination:**
   ```typescript
   const users = await prisma.users.findMany({
     take: 20,
     skip: page * 20,
     orderBy: { createdAt: 'desc' },
   });
   ```

### Caching Strategy

```typescript
// Implement caching for frequently accessed data
const getCachedUser = async (email: string) => {
  const cacheKey = `user:${email}`;
  
  // Check cache first
  let user = await cache.get(cacheKey);
  
  if (!user) {
    user = await prisma.users.findFirst({ where: { email } });
    await cache.set(cacheKey, user, 300); // 5 minutes
  }
  
  return user;
};
```

## 🔄 Migration Strategy

### Production Migrations

1. **Safe migration practices:**
   ```bash
   # Generate migration
   npx prisma migrate dev --name add_new_feature
   
   # Review migration before applying
   cat prisma/migrations/*/migration.sql
   
   # Apply to production
   npx prisma migrate deploy
   ```

2. **Zero-downtime migrations:**
   - Add new columns as nullable first
   - Populate data in background
   - Make columns required in subsequent migration
   - Remove old columns last

### Backup Strategy

```bash
# Before major migrations
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backups (in CI/CD)
if [ "$NODE_ENV" = "production" ]; then
  pg_dump $DATABASE_URL > "backup_pre_deploy_$(date +%Y%m%d_%H%M%S).sql"
fi
```

## 📞 Support and Maintenance

### Regular Maintenance Tasks

1. **Weekly:**
   - Review error logs
   - Check performance metrics
   - Monitor connection pool usage

2. **Monthly:**
   - Update dependencies
   - Review and optimize slow queries
   - Check database storage usage

3. **Quarterly:**
   - Rotate database credentials
   - Review and update indexes
   - Performance audit

### Getting Help

- **Prisma Documentation**: https://www.prisma.io/docs
- **Supabase Support**: https://supabase.com/support
- **TraceStack Issues**: Create an issue in the repository

---

## 🎯 Quick Start Checklist

For immediate production deployment:

1. [ ] Copy `.env.example` to `.env`
2. [ ] Configure production `DATABASE_URL` with pooling
3. [ ] Set `NODE_ENV=production`
4. [ ] Run `npx prisma generate`
5. [ ] Run `npx prisma migrate deploy`
6. [ ] Test health check endpoint
7. [ ] Monitor initial deployment
8. [ ] Set up error tracking
9. [ ] Configure automated backups
10. [ ] Document rollback procedures

**Remember**: Always test in a staging environment before production deployment!
