import { useEffect, useState } from "react";

// ============================================================================
// DEBOUNCE HOOK FOR SMOOTH SLIDER INTERACTIONS
// ============================================================================
// Custom hook that debounces a value to prevent excessive API calls
// Useful for search inputs, sliders, and other frequently changing values

export const useDebounce = <T>(value: T, delay: number): T => {
  // State to store the debounced value
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set up a timer to update the debounced value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Clean up the timer if the value changes before the delay completes
    // This ensures only the latest value is used after the delay
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // Re-run effect when value or delay changes

  return debouncedValue;
};
