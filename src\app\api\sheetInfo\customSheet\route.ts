import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import { type NextRequest } from "next/server";

//this will check if the user has sheets and return all sheets with their ids, names, and problems
export async function GET(request: NextRequest) {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  try {
    // Optimized: Single query to get user with sheets instead of separate queries
    const userWithSheets = await prisma.users.findFirst({
      where: {
        email: email,
      },
      select: {
        id: true,
        maxSheetSlots: true,
        sheets: {
          select: {
            id: true,
            name: true,
            problems: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc", // Most recent first
          },
        },
      },
    });

    if (!userWithSheets) {
      return new Response("User not found", { status: 404 });
    }

    const sheets = userWithSheets.sheets || [];
    const maxSheetSlots = userWithSheets.maxSheetSlots ?? 0;

    // Always return sheet information, even if user has no sheets yet
    return new Response(
      JSON.stringify({
        sheets: sheets,
        count: sheets.length,
        maxSheetSlots,
        isAllowedToCreateMore: sheets.length < maxSheetSlots,
      }),
      {
        headers: {
          "Content-Type": "application/json",
          // No aggressive caching for dynamic user data
          "Cache-Control": "private, no-cache, no-store, must-revalidate",
        },
      }
    );
  } catch (error) {
    console.error("Database error: Failed to fetch sheets", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

//this will delete the sheet of the user with the given id of the sheet

export async function DELETE(request: NextRequest) {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  const body = await request.json();
  const deleteId = body.id;

  if (!deleteId) {
    return new Response("Bad Request", { status: 400 });
  }

  try {
    // Optimized: Delete with user verification in single query
    const deletedSheet = await prisma.sheet.deleteMany({
      where: {
        id: deleteId,
        user: {
          email: email, // Verify ownership in the same query
        },
      },
    });

    if (deletedSheet.count === 0) {
      return new Response("Sheet not found or not owned by user", {
        status: 404,
      });
    }

    // Return the same structure as before to maintain frontend compatibility
    return new Response(JSON.stringify(deletedSheet), {
      headers: {
        "Content-Type": "application/json",
        // Fix: Remove aggressive caching for dynamic operations
        "Cache-Control": "private, no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("Database error: Failed to delete sheet", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
