"use client";

import { useBinarySearch } from "@/hooks/useBinarySearch";
import ArrayDisplay from "./binarysearch/ArrayDisplay";
import Controls from "./binarysearch/Controls";
import StatusDisplay from "./binarysearch/StatusDisplay";

const BinarySearchVisualizer = () => {
  const {
    array,
    target,
    isSearching,
    isPaused,
    isComplete,
    selectedAlgorithm,
    speed,
    inputValue,
    targetValue,
    error,
    result,
    setInputValue,
    setTargetValue,
    setSelectedAlgorithm,
    setSpeed,
    startVisualization,
    pauseVisualization,
    stopVisualization,
    resetVisualization,
    setError,
  } = useBinarySearch();

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="flex flex-col gap-6">
        <Controls
          inputValue={inputValue}
          targetValue={targetValue}
          selectedAlgorithm={selectedAlgorithm}
          speed={speed}
          isSearching={isSearching}
          isPaused={isPaused}
          error={error}
          onInputChange={setInputValue}
          onTargetChange={setTargetValue}
          onAlgorithmChange={setSelectedAlgorithm}
          onSpeedChange={setSpeed}
          onStart={startVisualization}
          onPause={pauseVisualization}
          onStop={stopVisualization}
          onReset={resetVisualization}
          onErrorDismiss={() => setError(null)}
        />

        <StatusDisplay 
          isSearching={isSearching} 
          isComplete={isComplete} 
          result={result}
          target={target}
          selectedAlgorithm={selectedAlgorithm}
        />

        <main className="flex flex-col flex-grow items-center justify-center p-4 bg-black/40 rounded-xl border border-slate-800 min-h-[400px] scrollable-content">
          <ArrayDisplay
            array={array}
            target={target}
            isSearching={isSearching}
            selectedAlgorithm={selectedAlgorithm}
          />
        </main>
      </div>
    </div>
  );
};

export default BinarySearchVisualizer;
