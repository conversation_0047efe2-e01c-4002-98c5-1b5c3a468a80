// ============================================================================
// POPUP AUTHENTICATION HANDLER - COMPREHENSIVE AUTHENTICATION FLOW
// ============================================================================
//
// 🎯 PURPOSE:
// This module handles popup-based Google OAuth authentication with Supabase.
// It provides a seamless login experience without redirecting the main page.
//
// 🚨 AUTHENTICATION ISSUES THAT WERE FIXED:
//
// 1. DOUBLE LOGIN REQUIREMENT (ORIGINAL ISSUE):
//    - Problem: Users had to login twice for authentication to work
//    - Cause: Race condition between multiple authentication detection mechanisms
//    - Fix: Prioritized message-based communication over auth state changes
//
// 2. PKCE "LOGIN FAILED" ERROR ON SUCCESS (SECONDARY ISSUE):
//    - Problem: "invalid request: both auth code and code verifier should be non-empty"
//    - Cause: Authorization code being processed twice (popup + automatic Supabase handling)
//    - Fix: Removed manual code exchange from main window, handle only in popup context
//
// 🔄 AUTHENTICATION FLOW (CURRENT IMPLEMENTATION):
//
// Step 1: USER CLICKS LOGIN
//    ↓
// Step 2: MAIN WINDOW (this file)
//    - Creates popup with Google OAuth URL
//    - Sets up BroadcastChannel for communication
//    - Configures auth state change listener as BACKUP only
//    ↓
// Step 3: POPUP WINDOW (/auth/popup-callback)
//    - Receives authorization code from Google
//    - Exchanges code for session (PKCE verifier available here)
//    - Sends success/error message back to main window
//    - Closes itself
//    ↓
// Step 4: MAIN WINDOW RECEIVES MESSAGE
//    - PRIORITY: BroadcastChannel message (from popup)
//    - FALLBACK: Auth state change (after 1 second delay)
//    - Resolves authentication promise
//    - Updates UI accordingly
//
// 🛡️ RACE CONDITION PREVENTION:
//
// 1. MESSAGE-BASED COMMUNICATION (Primary):
//    - Popup exchanges code and sends "OAUTH_SESSION_SUCCESS"
//    - Main window waits for this specific message
//    - 500ms delay for session synchronization
//
// 2. AUTH STATE CHANGE LISTENER (Fallback):
//    - Only triggers after 1 second if message-based communication fails
//    - Prevents premature resolution during session establishment
//    - Provides backup if BroadcastChannel fails
//
// 3. PKCE HANDLING:
//    - Code exchange happens ONLY in popup context (where verifier exists)
//    - Main window never attempts manual code exchange
//    - Eliminates "code verifier" errors
//
// 🔧 KEY ARCHITECTURAL DECISIONS:
//
// 1. Why popup-based auth?
//    - Better UX: No main page redirect
//    - Faster: Users stay on current page
//    - Cleaner: Modal-like experience
//
// 2. Why BroadcastChannel?
//    - Cross-window communication
//    - Event-driven architecture
//    - Reliable message delivery
//
// 3. Why not direct code exchange in main window?
//    - PKCE verifier stored in popup's session storage
//    - Main window lacks access to verifier
//    - Would cause "invalid request" errors
//
// 📝 USAGE EXAMPLE:
//
// ```typescript
// const result = await signInWithPopup("google", {
//   width: 500,
//   height: 600,
//   timeout: 300000
// });
//
// if (result.success) {
//   console.log("Authentication successful!");
//   // User is now logged in, queries can be invalidated
// } else {
//   console.error("Authentication failed:", result.error);
// }
// ```
//
// 🔍 DEBUGGING TIPS:
//
// 1. Check browser console for "Session established successfully in popup"
// 2. Monitor BroadcastChannel messages in DevTools
// 3. Verify popup window actually opens (popup blockers)
// 4. Check Network tab for OAuth flow completion
//
// 📚 RELATED FILES:
// - /auth/popup-callback/page.tsx: Handles OAuth callback in popup
// - ProblemsDisplay.tsx: Triggers authentication and handles results
// - auth-context.tsx: Manages global authentication state
//
// ============================================================================

import { createClient } from "@/lib/supabase/client";

export interface PopupAuthResult {
  success: boolean;
  error?: string;
  errorDescription?: string;
  errorCode?: string;
}

export interface PopupAuthOptions {
  width?: number;
  height?: number;
  provider?: "google" | "github" | "facebook" | "twitter";
  timeout?: number;
  retryAttempts?: number;
}

// Error codes for better error handling
export const AUTH_ERROR_CODES = {
  POPUP_BLOCKED: "popup_blocked",
  NETWORK_ERROR: "network_error",
  TIMEOUT: "timeout",
  USER_CANCELLED: "user_cancelled",
  OAUTH_ERROR: "oauth_error",
  SESSION_ERROR: "session_error",
  BROADCAST_ERROR: "broadcast_error",
  PROVIDER_ERROR: "provider_error",
  CONFIG_ERROR: "config_error",
  UNKNOWN_ERROR: "unknown_error",
} as const;

// User-friendly error messages
const ERROR_MESSAGES = {
  [AUTH_ERROR_CODES.POPUP_BLOCKED]:
    "Popup windows are blocked. Please enable popups for this site and try again.",
  [AUTH_ERROR_CODES.NETWORK_ERROR]:
    "Network connection error. Please check your internet connection and try again.",
  [AUTH_ERROR_CODES.TIMEOUT]: "Authentication timed out. Please try again.",
  [AUTH_ERROR_CODES.USER_CANCELLED]:
    "Authentication was cancelled. Please try again if you want to sign in.",
  [AUTH_ERROR_CODES.OAUTH_ERROR]: "Authentication failed. Please try again.",
  [AUTH_ERROR_CODES.SESSION_ERROR]:
    "Failed to establish session. Please try again.",
  [AUTH_ERROR_CODES.BROADCAST_ERROR]:
    "Communication error occurred. Please refresh the page and try again.",
  [AUTH_ERROR_CODES.PROVIDER_ERROR]:
    "Authentication provider error. Please try again later.",
  [AUTH_ERROR_CODES.CONFIG_ERROR]:
    "Configuration error. Please contact support.",
  [AUTH_ERROR_CODES.UNKNOWN_ERROR]:
    "An unexpected error occurred. Please try again.",
};

/**
 * Check if we're in a browser environment
 */
function isBrowser(): boolean {
  return typeof window !== "undefined";
}

/**
 * Check if BroadcastChannel is supported
 */
function isBroadcastChannelSupported(): boolean {
  return isBrowser() && "BroadcastChannel" in window;
}

/**
 * Opens a popup window for OAuth authentication
 */
export function openAuthPopup(
  url: string,
  options: PopupAuthOptions = {}
): { popup: Window | null; error?: PopupAuthResult } {
  if (!isBrowser()) {
    return {
      popup: null,
      error: {
        success: false,
        error: ERROR_MESSAGES[AUTH_ERROR_CODES.CONFIG_ERROR],
        errorCode: AUTH_ERROR_CODES.CONFIG_ERROR,
      },
    };
  }

  const { width = 500, height = 600 } = options;

  try {
    // Calculate center position relative to the browser window, not the entire screen
    const left = window.screenX + window.outerWidth / 2 - width / 2;
    const top = window.screenY + window.outerHeight / 2 - height / 2;

    // Ensure popup stays within screen bounds
    const finalLeft = Math.max(0, Math.min(left, window.screen.width - width));
    const finalTop = Math.max(0, Math.min(top, window.screen.height - height));

    // Window features for popup
    const windowFeatures = [
      `width=${width}`,
      `height=${height}`,
      `top=${finalTop}`,
      `left=${finalLeft}`,
      "scrollbars=no",
      "resizable=no",
      "copyhistory=no",
      "toolbar=no",
      "menubar=no",
      "location=no",
      "directories=no",
      "status=no",
    ].join(",");

    const popup = window.open(url, "oauth-popup", windowFeatures);

    if (!popup) {
      return {
        popup: null,
        error: {
          success: false,
          error: ERROR_MESSAGES[AUTH_ERROR_CODES.POPUP_BLOCKED],
          errorCode: AUTH_ERROR_CODES.POPUP_BLOCKED,
        },
      };
    }

    // Focus the popup window
    popup.focus();
    return { popup };
  } catch (error) {
    console.error("Failed to open popup:", error);
    return {
      popup: null,
      error: {
        success: false,
        error: ERROR_MESSAGES[AUTH_ERROR_CODES.POPUP_BLOCKED],
        errorCode: AUTH_ERROR_CODES.POPUP_BLOCKED,
      },
    };
  }
}

/**
 * Handles the popup authentication flow with Supabase
 */
export async function signInWithPopup(
  provider: "google" | "github" | "facebook" | "twitter" = "google",
  options: PopupAuthOptions = {}
): Promise<PopupAuthResult> {
  const {
    timeout = 300000, // 5 minutes default timeout
    retryAttempts = 0,
  } = options;

  // Validate environment
  if (!isBrowser()) {
    return {
      success: false,
      error: ERROR_MESSAGES[AUTH_ERROR_CODES.CONFIG_ERROR],
      errorCode: AUTH_ERROR_CODES.CONFIG_ERROR,
    };
  }

  if (!isBroadcastChannelSupported()) {
    return {
      success: false,
      error:
        "Your browser doesn't support the required features. Please update your browser.",
      errorCode: AUTH_ERROR_CODES.BROADCAST_ERROR,
    };
  }

  return new Promise(async (resolve) => {
    let isResolved = false;
    let supabase: ReturnType<typeof createClient>;
    let channel: BroadcastChannel | null = null;
    let popup: Window | null = null;
    let checkClosedInterval: NodeJS.Timeout | null = null;
    let timeoutId: NodeJS.Timeout | null = null;
    let authListener: any = null;

    const safeResolve = (result: PopupAuthResult) => {
      if (isResolved) return;
      isResolved = true;
      resolve(result);
      cleanup();
    };

    // Cleanup function
    const cleanup = () => {
      try {
        if (channel) {
          channel.close();
          channel = null;
        }
        if (checkClosedInterval) {
          clearInterval(checkClosedInterval);
          checkClosedInterval = null;
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
        if (authListener) {
          authListener.subscription.unsubscribe();
          authListener = null;
        }
        if (popup && !popup.closed) {
          popup.close();
          popup = null;
        }
      } catch (error) {
        console.error("Cleanup error:", error);
      }
    };

    try {
      // Initialize Supabase client
      supabase = createClient();
      const origin = window.location.origin;

      // Get the OAuth URL from Supabase
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${origin}/auth/popup-callback`,
          queryParams: {
            prompt: "select_account", // Force account selection for Google
          },
          skipBrowserRedirect: true,
        },
      });

      if (error) {
        safeResolve({
          success: false,
          error:
            error.message || ERROR_MESSAGES[AUTH_ERROR_CODES.PROVIDER_ERROR],
          errorCode: AUTH_ERROR_CODES.PROVIDER_ERROR,
        });
        return;
      }

      if (!data?.url) {
        safeResolve({
          success: false,
          error: ERROR_MESSAGES[AUTH_ERROR_CODES.PROVIDER_ERROR],
          errorCode: AUTH_ERROR_CODES.PROVIDER_ERROR,
        });
        return;
      }

      // Open the popup window
      const popupResult = openAuthPopup(data.url, options);
      if (popupResult.error) {
        safeResolve(popupResult.error);
        return;
      }
      popup = popupResult.popup!;

      // Create BroadcastChannel for popup communication
      try {
        channel = new BroadcastChannel("supabase-auth-popup");
      } catch (error) {
        safeResolve({
          success: false,
          error: ERROR_MESSAGES[AUTH_ERROR_CODES.BROADCAST_ERROR],
          errorCode: AUTH_ERROR_CODES.BROADCAST_ERROR,
        });
        return;
      }

      // Set up message listener for popup communication - PRIORITY METHOD
      const handleMessage = async (event: MessageEvent) => {
        if (isResolved) return;

        try {
          const {
            type,
            error: msgError,
            errorDescription,
            session,
          } = event.data || {};

          if (type === "OAUTH_SESSION_SUCCESS" && session) {
            // Clear the interval immediately to prevent "popup closed" error
            if (checkClosedInterval) {
              clearInterval(checkClosedInterval);
              checkClosedInterval = null;
            }

            // Session was successfully established in popup
            console.log("Session established successfully in popup");

            // Wait a bit for the session to sync to main window
            setTimeout(() => {
              safeResolve({
                success: true,
              });
            }, 500); // Give 500ms for session to sync
          } else if (type === "OAUTH_ERROR") {
            const errorCode =
              msgError === "access_denied"
                ? AUTH_ERROR_CODES.USER_CANCELLED
                : AUTH_ERROR_CODES.OAUTH_ERROR;

            safeResolve({
              success: false,
              error: errorDescription || ERROR_MESSAGES[errorCode],
              errorCode,
            });
          }
        } catch (error) {
          console.error("Message handling error:", error);
          safeResolve({
            success: false,
            error: ERROR_MESSAGES[AUTH_ERROR_CODES.BROADCAST_ERROR],
            errorCode: AUTH_ERROR_CODES.BROADCAST_ERROR,
          });
        }
      };

      // Add event listener
      channel.addEventListener("message", handleMessage);

      // Fallback: Listen for auth state changes in the main window - ONLY AS BACKUP
      // This will only trigger if message-based communication fails
      let authStateBackupTimer: NodeJS.Timeout | null = null;

      try {
        const { data: authListenerData } = supabase.auth.onAuthStateChange(
          (event, session) => {
            if (event === "SIGNED_IN" && session && !isResolved) {
              // Only use this as a backup - wait 2 seconds to see if message-based resolution works first
              if (authStateBackupTimer) {
                clearTimeout(authStateBackupTimer);
              }

              authStateBackupTimer = setTimeout(() => {
                if (!isResolved) {
                  console.log("Using auth state change fallback");
                  safeResolve({
                    success: true,
                  });
                }
              }, 1000); // Wait 1 second before using this fallback (reduced since we fixed the main flow)
            }
          }
        );
        authListener = authListenerData;
      } catch (error) {
        console.error("Auth state listener error:", error);
      }

      // Set up popup close detection with a longer interval to reduce race conditions
      checkClosedInterval = setInterval(() => {
        if (popup && popup.closed && !isResolved) {
          safeResolve({
            success: false,
            error: ERROR_MESSAGES[AUTH_ERROR_CODES.USER_CANCELLED],
            errorCode: AUTH_ERROR_CODES.USER_CANCELLED,
          });
        }
      }, 2000); // Increased interval to 2 seconds to reduce race conditions

      // Set a timeout to prevent hanging
      timeoutId = setTimeout(() => {
        if (!isResolved) {
          safeResolve({
            success: false,
            error: ERROR_MESSAGES[AUTH_ERROR_CODES.TIMEOUT],
            errorCode: AUTH_ERROR_CODES.TIMEOUT,
          });
        }
      }, timeout);
    } catch (error) {
      console.error("SignInWithPopup error:", error);
      safeResolve({
        success: false,
        error:
          error instanceof Error
            ? error.message
            : ERROR_MESSAGES[AUTH_ERROR_CODES.UNKNOWN_ERROR],
        errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
      });
    }
  });
}

// Cache the popup support check result to avoid repeated popup creation
let popupSupportCache: boolean | null = null;

/**
 * Check if popups are supported/allowed (cached to prevent multiple test popups)
 */
export function isPopupSupported(): boolean {
  if (!isBrowser()) {
    return false;
  }

  // Return cached result if we've already checked
  if (popupSupportCache !== null) {
    return popupSupportCache;
  }

  try {
    // Create test popup with minimal visibility
    const popup = window.open(
      "",
      "_blank",
      "width=1,height=1,top=-1000,left=-1000"
    );
    if (popup) {
      popup.close();
      popupSupportCache = true;
      return true;
    }
    popupSupportCache = false;
    return false;
  } catch {
    popupSupportCache = false;
    return false;
  }
}

/**
 * Retry authentication with exponential backoff
 */
export async function signInWithPopupRetry(
  provider: "google" | "github" | "facebook" | "twitter" = "google",
  options: PopupAuthOptions = {}
): Promise<PopupAuthResult> {
  const { retryAttempts = 2, ...otherOptions } = options;
  let lastError: PopupAuthResult | null = null;

  for (let attempt = 0; attempt <= retryAttempts; attempt++) {
    const result = await signInWithPopup(provider, otherOptions);

    if (result.success) {
      return result;
    }

    lastError = result;

    // Don't retry for certain error types
    const nonRetryableErrors = [
      AUTH_ERROR_CODES.POPUP_BLOCKED,
      AUTH_ERROR_CODES.USER_CANCELLED,
      AUTH_ERROR_CODES.CONFIG_ERROR,
      AUTH_ERROR_CODES.BROADCAST_ERROR,
    ];

    if (nonRetryableErrors.includes(result.errorCode as any)) {
      break;
    }

    // Wait before retrying (exponential backoff)
    if (attempt < retryAttempts) {
      await new Promise((resolve) =>
        setTimeout(resolve, Math.pow(2, attempt) * 1000)
      );
    }
  }

  return (
    lastError || {
      success: false,
      error: ERROR_MESSAGES[AUTH_ERROR_CODES.UNKNOWN_ERROR],
      errorCode: AUTH_ERROR_CODES.UNKNOWN_ERROR,
    }
  );
}
