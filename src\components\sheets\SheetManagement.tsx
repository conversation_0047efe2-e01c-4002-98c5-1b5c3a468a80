"use client";

// ============================================================================
// IMPORTS & DEPENDENCIES
// ============================================================================
// Shadcn UI components for consistent design system
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Core types and API functions
import type { CodeforcesSubmission as CodeforcesSubmissionType } from "@/lib/codeforces";
import { fetchCodeforcesSubmissions } from "@/lib/codeforces-client";
import { clearCodingSheet, getCompletionStats } from "@/lib/codingSheet";
import axios from "axios";
import React from "react";

// ============================================================================
// SHEET MANAGEMENT COMPONENT
// ============================================================================
// MAIN PURPOSE: Comprehensive dashboard for managing coding practice sheets
//
// KEY FEATURES:
// 1. 📊 Statistics Dashboard - Shows total, completed, remaining problems + progress %
// 2. 🔍 Problem Filtering - Filter by All/Completed/Incomplete with live counts
// 3. ✅ Auto-Sync with Codeforces - Automatically checks solved problems on page load
// 4. 🔄 Manual Sync - "Check Solved" button for manual Codeforces API sync (no page refresh)
// 5. 🗑️ Sheet Deletion - "Clear Sheet" with confirmation dialogs
// 6. 🎨 Responsive UI - Works on mobile/desktop with shadcn modals
// 7. 💾 Local Storage Integration - Persists completion status locally
// 8. ⚡ Instant Updates - Stats update immediately without page reload when syncing
//
// TECHNICAL ARCHITECTURE:
// - Uses shadcn Dialog components for all modals (3 different modal types)
// - Auto-check runs once on component mount (prevents spam)
// - Integrates with Codeforces API to sync submission status
// - Real-time statistics calculation with useMemo optimization
// - Flexible props interface for parent component communication

interface SheetManagementProps {
  onFilterChange?: (filter: "all" | "completed" | "incomplete") => void; // Callback when user changes filter
  onSheetCleared?: () => void; // Callback when sheet is deleted (triggers parent refresh)
  onSheetImported?: () => void; // Callback when new problems are marked as completed
  currentFilter?: "all" | "completed" | "incomplete"; // Current active filter from parent
  sheetId?: string | null; // Database ID of the sheet (needed for deletion API call)
  problems?: CodeforcesSubmissionType[]; // Array of problems for stats calculation
}

export const SheetManagement: React.FC<SheetManagementProps> = ({
  onFilterChange,
  onSheetCleared,
  onSheetImported,
  currentFilter = "all",
  sheetId,
  problems,
}) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================

  // Core operation states
  const [isCheckingSolved, setIsCheckingSolved] = React.useState(false); // Prevents multiple simultaneous API calls
  const [showClearDialog, setShowClearDialog] = React.useState(false); // Controls clear confirmation modal
  const [hasAutoChecked, setHasAutoChecked] = React.useState(false); // Ensures auto-check runs only once per mount
  const [localStorageUpdateTrigger, setLocalStorageUpdateTrigger] =
    React.useState(0); // Forces stats recalculation when localStorage changes

  // Modal state for "Check Solved" functionality (5 different modal types)
  const [checkSolvedModal, setCheckSolvedModal] = React.useState<{
    show: boolean;
    type: "loading" | "success" | "error" | "info" | "no-handle"; // Different modal states for UX
    title: string;
    message: string;
    newCompletions?: number; // Used for success state to show how many problems were marked complete
  }>({
    show: false,
    type: "loading",
    title: "",
    message: "",
  });

  // Modal state for sheet deletion results (success/error feedback)
  const [clearResultModal, setClearResultModal] = React.useState<{
    show: boolean;
    type: "success" | "error";
    title: string;
    message: string;
  }>({
    show: false,
    type: "success",
    title: "",
    message: "",
  });

  // ============================================================================
  // STATISTICS CALCULATION (Real-time)
  // ============================================================================
  // Calculates completion statistics from problems array or localStorage fallback
  // Uses useMemo for performance optimization - recalculates when problems or localStorage changes
  const stats = React.useMemo(() => {
    if (problems && problems.length > 0) {
      // PRIMARY MODE: Calculate from provided problems array (more accurate)
      const total = problems.length;
      const completed = problems.filter((problem) => {
        // Generate unique problem ID (contestId + problemIndex)
        const problemId = `${problem.problem.contestId || "unknown"}${
          problem.problem.index
        }`;
        try {
          // Check if problem is marked as completed in localStorage
          const sheetData = localStorage.getItem("tracestack_coding_sheet");
          if (!sheetData) return false;
          const parsedData = JSON.parse(sheetData);
          const completedProblems = new Set(parsedData.completedProblems || []);
          return completedProblems.has(problemId);
        } catch {
          return false; // Fallback if localStorage is corrupted
        }
      }).length;
      const remaining = total - completed;
      const completionPercentage =
        total > 0 ? Math.round((completed / total) * 100) : 0;

      return { total, completed, remaining, completionPercentage };
    } else {
      // FALLBACK MODE: Use localStorage-based stats (when no problems provided)
      return getCompletionStats();
    }
  }, [problems, localStorageUpdateTrigger]);

  // ============================================================================
  // FILTER HANDLING
  // ============================================================================
  // Simple passthrough to parent component - actual filtering logic is in parent
  const handleFilterChange = (filter: "all" | "completed" | "incomplete") => {
    onFilterChange?.(filter);
  };

  // ============================================================================
  // CODEFORCES API INTEGRATION (Core Feature)
  // ============================================================================
  // MAIN FUNCTION: Syncs local completion status with Codeforces submissions
  //
  // HOW IT WORKS:
  // 1. Gets user's Codeforces handle from our API
  // 2. Fetches all their submissions from Codeforces API
  // 3. Filters for successful submissions (verdict = "OK")
  // 4. Compares with current sheet problems
  // 5. Updates localStorage with newly completed problems
  // 6. Shows appropriate feedback modal
  //
  // MODES:
  // - Auto Mode (isAutoCheck = true): Silent background sync on page load
  // - Manual Mode (isAutoCheck = false): User-triggered with full UI feedback
  const handleCheckSolved = React.useCallback(
    async (isAutoCheck = false) => {
      if (isCheckingSolved) return; // GUARD: Prevent multiple simultaneous requests

      setIsCheckingSolved(true);

      // Show loading modal for manual checks (not for auto-check to avoid annoying popups)
      if (!isAutoCheck) {
        setCheckSolvedModal({
          show: true,
          type: "loading",
          title: "Checking Solved Problems",
          message: "Fetching your submissions from Codeforces...",
        });
      }

      try {
        // STEP 1: Get user's Codeforces handle from our backend
        const { data: userInfo } = await axios.get("/api/userInfo");
        const handle = userInfo?.handle;

        if (!handle) {
          // Handle missing - only show modal for manual checks
          if (!isAutoCheck) {
            setCheckSolvedModal({
              show: true,
              type: "no-handle",
              title: "Codeforces Handle Required",
              message:
                "Codeforces handle not found in your profile. Please add it to check for solved problems.",
            });
          }
          return;
        }

        // STEP 2: Fetch user submissions from Codeforces API
        // Fetch fresh submissions for "Check Solved"
        const response = await fetchCodeforcesSubmissions(handle);
        if (response.status !== "OK" || !response.result) {
          throw new Error("Failed to fetch submissions from Codeforces API.");
        }
        const submissions = response.result;

        // STEP 3: Extract successfully solved problem IDs
        const solvedProblemIds = new Set(
          submissions
            .filter((sub: CodeforcesSubmissionType) => sub.verdict === "OK") // Only accepted solutions
            .map(
              (sub: CodeforcesSubmissionType) =>
                `${sub.problem.contestId}${sub.problem.index}` // Create problemId
            )
        );

        // STEP 4: Compare with current sheet and update localStorage
        const sheetDataStr = localStorage.getItem("tracestack_coding_sheet");
        const sheetData = sheetDataStr
          ? JSON.parse(sheetDataStr)
          : { completedProblems: [] };
        const completedProblems = new Set(sheetData.completedProblems || []);

        let newCompletions = 0;
        problems?.forEach((problem) => {
          const problemId = `${problem.problem.contestId}${problem.problem.index}`;
          // Mark as completed if: solved on Codeforces AND not already marked locally
          if (
            solvedProblemIds.has(problemId) &&
            !completedProblems.has(problemId)
          ) {
            completedProblems.add(problemId);
            newCompletions++;
          }
        });

        // STEP 5: Save updates and update local UI immediately
        if (newCompletions > 0) {
          sheetData.completedProblems = Array.from(completedProblems);
          localStorage.setItem(
            "tracestack_coding_sheet",
            JSON.stringify(sheetData)
          );
          // Trigger local stats recalculation without parent refresh
          setLocalStorageUpdateTrigger((prev) => prev + 1);

          // Notify parent component that problems were updated (for both auto and manual checks)
          // Add small delay to ensure localStorage changes are fully processed
          setTimeout(() => {
            onSheetImported?.();
          }, 50);

          // Show success modal for manual checks only
          if (!isAutoCheck) {
            setCheckSolvedModal({
              show: true,
              type: "success",
              title: "Problems Updated!",
              message: `Successfully marked ${newCompletions} new problem${
                newCompletions > 1 ? "s" : ""
              } as completed.`,
              newCompletions,
            });
          }
        } else {
          // No new completions - show info modal for manual checks
          if (!isAutoCheck) {
            setCheckSolvedModal({
              show: true,
              type: "info",
              title: "Already Up to Date",
              message:
                "Your sheet is already up-to-date with your Codeforces submissions.",
            });
          }
        }
      } catch (error) {
        console.error("Error checking solved problems:", error);
        // Show error modal for manual checks
        if (!isAutoCheck) {
          setCheckSolvedModal({
            show: true,
            type: "error",
            title: "Error Checking Problems",
            message:
              "An error occurred while checking for solved problems. Please try again.",
          });
        }
      } finally {
        setIsCheckingSolved(false);
      }
    },
    [isCheckingSolved, problems]
  );

  // ============================================================================
  // AUTO-CHECK FEATURE (Runs once on component mount)
  // ============================================================================
  // Automatically syncs with Codeforces when component loads
  // Only runs once per mount to avoid spam
  // Silent operation - no modal popups to avoid annoying UX
  React.useEffect(() => {
    if (!hasAutoChecked && problems && problems.length > 0) {
      setHasAutoChecked(true);
      // Add small delay to ensure parent component filtering is ready
      setTimeout(() => {
        handleCheckSolved(true); // true = isAutoCheck mode (silent)
      }, 100);
    }
  }, [problems, hasAutoChecked, handleCheckSolved]);

  // ============================================================================
  // USER ACTION HANDLERS
  // ============================================================================

  // Manual check solved button click (shows full UI feedback)
  const handleManualCheckSolved = () => {
    handleCheckSolved(false); // false = manual mode (with modals)
  };

  // Close the check solved modal
  const closeCheckSolvedModal = () => {
    setCheckSolvedModal({
      show: false,
      type: "loading",
      title: "",
      message: "",
    });
  };

  // Open sheet deletion confirmation dialog
  const handleClearSheet = () => {
    setShowClearDialog(true);
  };

  // ============================================================================
  // SHEET DELETION LOGIC
  // ============================================================================
  // Handles complete sheet deletion with API call and localStorage cleanup
  const handleClearConfirm = async () => {
    if (!sheetId) {
      setClearResultModal({
        show: true,
        type: "error",
        title: "Error",
        message: "No sheet ID found. Cannot delete sheet.",
      });
      return;
    }

    try {
      // Call backend API to delete sheet from database
      const response = await axios.delete("/api/sheetInfo/customSheet", {
        data: { id: sheetId },
      });

      if (response.status === 200) {
        // SUCCESS: Clean up localStorage and notify parent
        clearCodingSheet(); // Clear localStorage
        setShowClearDialog(false); // Close confirmation dialog
        onSheetCleared?.(); // Notify parent component
        setClearResultModal({
          show: true,
          type: "success",
          title: "Sheet Deleted",
          message: "Sheet deleted successfully!",
        });
      } else {
        setClearResultModal({
          show: true,
          type: "error",
          title: "Error",
          message: "Failed to delete sheet from server",
        });
      }
    } catch (error: any) {
      console.error("Error deleting sheet:", error);
      let errorMessage = "An error occurred while deleting the sheet";

      // Provide specific error messages based on HTTP status
      if (error.response?.status === 401) {
        errorMessage = "You are not authorized to delete this sheet";
      } else if (error.response?.status === 404) {
        errorMessage = "Sheet not found";
      }

      setClearResultModal({
        show: true,
        type: "error",
        title: "Error Deleting Sheet",
        message: errorMessage,
      });
    }
  };

  // ============================================================================
  // MODAL ICON HELPER FUNCTION
  // ============================================================================
  // Returns appropriate icon component based on modal type
  // Each icon has consistent styling with colored backgrounds
  const getModalIcon = (type: string) => {
    switch (type) {
      case "loading":
        return (
          <div className="w-10 h-10 bg-blue-600/20 rounded-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-400"></div>
          </div>
        );
      case "success":
        return (
          <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-green-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
      case "error":
        return (
          <div className="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
        );
      case "info":
        return (
          <div className="w-10 h-10 bg-blue-600/20 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
        );
      case "no-handle":
        return (
          <div className="w-10 h-10 bg-yellow-600/20 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-yellow-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  // ============================================================================
  // RENDER UI COMPONENTS
  // ============================================================================
  return (
    <>
      {/* =============================================
          STATISTICS DASHBOARD (Top Section)
          ============================================= */}
      {/* 4-card grid showing key metrics with color-coded styling */}
      {/* Responsive: 1 column on mobile, 4 columns on desktop */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {/* Total Problems Card (Blue) */}
        <div className="bg-gray-700/30 border border-gray-600/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-500">{stats.total}</div>
          <div className="text-sm text-gray-300">Total Problems</div>
        </div>

        {/* Completed Problems Card (Green) */}
        <div className="bg-gray-700/30 border border-gray-600/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-400">
            {stats.completed}
          </div>
          <div className="text-sm text-gray-300">Completed</div>
        </div>

        {/* Remaining Problems Card (Orange) */}
        <div className="bg-gray-700/30 border border-gray-600/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-400">
            {stats.remaining}
          </div>
          <div className="text-sm text-gray-300">Remaining</div>
        </div>

        {/* Progress Percentage Card (Purple) */}
        <div className="bg-gray-700/30 border border-gray-600/50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-400">
            {stats.completionPercentage}%
          </div>
          <div className="text-sm text-gray-300">Progress</div>
        </div>
      </div>

      {/* =============================================
          MAIN CONTROL SECTION (Filters + Actions)
          ============================================= */}
      {/* Flex layout: Filters on left, Actions on right */}
      {/* Responsive: Stacks vertically on mobile, side-by-side on desktop */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between lg:gap-8 mb-6">
        {/* LEFT SIDE: Problem Filtering Controls */}
        <div className="mb-6 lg:mb-0">
          <label className="block text-sm font-medium text-gray-200 mb-3">
            Filter Problems
          </label>
          {/* Filter buttons with live counts and active state styling */}
          <div className="flex flex-wrap gap-3">
            {/* All Problems Filter */}
            <button
              onClick={() => handleFilterChange("all")}
              className={`px-6 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                currentFilter === "all"
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-500/20" // Active state
                  : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-gray-600/50" // Inactive state
              }`}
            >
              All ({stats.total})
            </button>

            {/* Completed Problems Filter */}
            <button
              onClick={() => handleFilterChange("completed")}
              className={`px-6 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                currentFilter === "completed"
                  ? "bg-green-600 text-white shadow-lg shadow-green-500/20" // Active state
                  : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-gray-600/50" // Inactive state
              }`}
            >
              Completed ({stats.completed})
            </button>

            {/* Incomplete Problems Filter */}
            <button
              onClick={() => handleFilterChange("incomplete")}
              className={`px-6 py-2.5 rounded-lg font-medium transition-all duration-200 ${
                currentFilter === "incomplete"
                  ? "bg-orange-600 text-white shadow-lg shadow-orange-500/20" // Active state
                  : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 border border-gray-600/50" // Inactive state
              }`}
            >
              Incomplete ({stats.remaining})
            </button>
          </div>
        </div>

        {/* RIGHT SIDE: Action Buttons */}
        <div className="flex-shrink-0">
          <label className="block text-sm font-medium text-gray-200 mb-3 lg:text-right">
            Actions
          </label>
          {/* Action buttons with icons and disabled states */}
          <div className="flex flex-wrap lg:flex-nowrap gap-3 lg:justify-end">
            {/* Check Solved Button (Teal) - Syncs with Codeforces */}
            <button
              onClick={handleManualCheckSolved}
              disabled={isCheckingSolved || !problems || problems.length === 0}
              className="flex items-center justify-center gap-2 px-5 py-2.5 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-teal-500/20 font-medium"
            >
              {/* Checkmark Icon */}
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
              {/* Dynamic button text based on loading state */}
              {isCheckingSolved ? "Checking..." : "Check Solved"}
            </button>

            {/* Clear Sheet Button (Red) - Deletes entire sheet */}
            <button
              onClick={handleClearSheet}
              disabled={stats.total === 0}
              className="flex items-center justify-center gap-2 px-5 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-red-500/20 font-medium"
            >
              {/* Trash Icon */}
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
              Clear Sheet
            </button>
          </div>
        </div>
      </div>

      {/* =============================================
          MODAL SYSTEM (Shadcn Dialogs)
          ============================================= */}

      {/* CHECK SOLVED MODAL - Handles 5 different states */}
      <Dialog open={checkSolvedModal.show} onOpenChange={closeCheckSolvedModal}>
        <DialogContent
          className="sm:max-w-md"
          showCloseButton={checkSolvedModal.type !== "loading"}
        >
          <DialogHeader>
            {/* Icon + Title Row */}
            <div className="flex items-center gap-3 mb-2">
              {getModalIcon(checkSolvedModal.type)}
              <DialogTitle className="text-xl">
                {checkSolvedModal.title}
              </DialogTitle>
            </div>
            <DialogDescription className="text-left">
              {checkSolvedModal.message}
            </DialogDescription>
          </DialogHeader>

          {/* SUCCESS STATE: Special celebration panel */}
          {checkSolvedModal.type === "success" &&
            checkSolvedModal.newCompletions && (
              <div className="bg-green-600/10 border border-green-500/30 rounded-lg p-3">
                <div className="text-green-400 text-sm font-medium">
                  ✨ {checkSolvedModal.newCompletions} problem
                  {checkSolvedModal.newCompletions > 1 ? "s" : ""} marked as
                  completed
                </div>
              </div>
            )}

          {/* BUTTONS: Different for each modal type, hidden during loading */}
          {checkSolvedModal.type !== "loading" && (
            <DialogFooter>
              {checkSolvedModal.type === "success" && (
                <Button
                  onClick={closeCheckSolvedModal}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Great!
                </Button>
              )}
              {checkSolvedModal.type === "error" && (
                <Button onClick={closeCheckSolvedModal} variant="destructive">
                  Close
                </Button>
              )}
              {checkSolvedModal.type === "info" && (
                <Button onClick={closeCheckSolvedModal}>Got it</Button>
              )}
              {checkSolvedModal.type === "no-handle" && (
                <Button
                  onClick={closeCheckSolvedModal}
                  className="bg-yellow-600 hover:bg-yellow-700"
                >
                  Understand
                </Button>
              )}
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>

      {/* CLEAR CONFIRMATION MODAL - Warns before sheet deletion */}
      <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            {/* Warning Icon + Title */}
            <div className="flex items-center gap-3 mb-2">
              <div className="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
                <svg
                  className="w-5 h-5 text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <DialogTitle>Clear Coding Sheet</DialogTitle>
            </div>
            <DialogDescription className="text-left">
              This will permanently delete your current sheet and all progress.
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            {/* Cancel Button (Outline style) */}
            <Button variant="outline" onClick={() => setShowClearDialog(false)}>
              Cancel
            </Button>
            {/* Confirm Delete Button (Destructive style) */}
            <Button variant="destructive" onClick={handleClearConfirm}>
              Clear Sheet
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* CLEAR RESULT MODAL - Shows success/error after deletion attempt */}
      <Dialog
        open={clearResultModal.show}
        onOpenChange={() =>
          setClearResultModal({ ...clearResultModal, show: false })
        }
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            {/* Success/Error Icon + Title */}
            <div className="flex items-center gap-3 mb-2">
              {clearResultModal.type === "success" ? (
                // Success Icon (Green Checkmark)
                <div className="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-green-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
              ) : (
                // Error Icon (Red Warning)
                <div className="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-red-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                </div>
              )}
              <DialogTitle>{clearResultModal.title}</DialogTitle>
            </div>
            <DialogDescription className="text-left">
              {clearResultModal.message}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            {/* Simple close button */}
            <Button
              onClick={() =>
                setClearResultModal({ ...clearResultModal, show: false })
              }
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
