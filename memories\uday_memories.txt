# TraceStack Project

- User wants to implement Prisma for the TraceStack application as the database ORM/toolkit.
- User prefers simpler Prisma client implementations without BigInt serialization complexity for TraceStack.
- For TraceStack project authentication, user prefers to start with basic login/signup only and exclude OAuth implementation for now.
- User prefers to temporarily disable authentication and hardcode user details during development phases in TraceStack for easier testing and development.
- TraceStack project needs Codeforces API integration to fetch user submissions with OK verdict, with API credentials stored in environment variables.
- User wants date filtering functionality for Codeforces submissions to show problems solved on a specific day.
- User wants user search functionality to include fetching all solved questions/problems for the searched user in TraceStack.
- User prefers using React Query and Axios for data fetching instead of native fetch function for better reliability and consistency in the TraceStack project.
- User prefers using pnpm for package installation instead of npm for the TraceStack project.
- User prefers detailed comments explaining implementation logic to be added right before code declarations/implementations for better future understanding.
- User prefers storing all fetched problems in component state to avoid redundant API calls when filtering by date ranges, using local state filtering instead of new API requests.
- User prefers duplicate problems to be removed from Codeforces submissions data, ensuring each unique problem appears only once even if solved multiple times.
- User prefers showing all solved problems regardless of start/end date selection, with date range filtering only affecting the graph visualization range in TraceStack.
- User prefers start/end points and date filters to automatically reset to default when searching for a different user in TraceStack to avoid applying previous user's filter settings to new searches.
- User prefers a reset option that clears all start points, end points, and date range filters back to default state in TraceStack.
- User prefers all UI states (start/end range selections, zoom levels, filters) to be automatically cleared when searching for a different user in TraceStack, providing a fresh state like page refresh.
- User prefers AND operator for problem tag filtering in TraceStack, where selecting multiple tags (e.g., greedy and dp) should show problems that have ALL selected tags, not problems with any of the selected tags.
- User prefers showing all problem tags for selection instead of limiting to 20, and wants collapsible components for tag filtering to prevent layout shifts and unnecessary DOM re-rendering in TraceStack.
- User prefers display names in TraceStack to be colored based on player's rating and follow official Codeforces naming conventions for GM (Grandmaster) and LGM (Legendary Grandmaster) titles.
- User prefers removing rating text tags (keeping only colors for rating indication).
- User prefers buttons with hover effects and visual feedback on click to make interactions feel more responsive and indicate that actions are working, wants cursor-pointer on button hover, and button animations without changing existing colors or gradients in TraceStack UI. User prefers buttons to be less bright/vibrant in TraceStack for a more subtle and professional appearance. User prefers minimal, clean button styling that blends harmoniously with the page design rather than heavy container backgrounds, borders, and shadows that make elements stand out awkwardly.
- User wants a coding sheet feature in TraceStack where filtered problems are stored in local storage and displayed on a dedicated 'custom_sheet' route page with rating-based sorting functionality. User prefers coding sheet feature in TraceStack to have a maximum limit of 400 problems to prevent rendering performance issues.
- User prefers collapsible UI elements to be closed by default rather than open to avoid unnecessary rendering and improve performance.
- User prefers dark mode styling to be consistently applied across all pages in TraceStack to match the overall app UI.
- User prefers localStorage only for coding sheet functionality and authentication, but removed all localStorage caching for API responses and user data to avoid browser cache complexity.
- User prefers a beautiful themed UI explanation/guide for the /test feature that walks users through the workflow: search username → view user info and problems → select graph range as start/end points → zoom to see problems in range → create custom sheet → apply filters.
- User prefers professional-looking fonts and sophisticated step designs that match the website's quality level in TraceStack.
- User prefers UI elements that disappear when user performs searches in TraceStack.
- User prefers less blue and more simple yet eye-catching component styling in TraceStack, and wants consistent background styling across pages using the home page gradient pattern with specific blur and positioning effects.
- User prefers advanced performance analysis component in TraceStack to be less blue and more simple yet eye-catching, and wants consistent background styling across pages using the home page gradient pattern with specific blur and positioning effects.
- User prefers title styling with gradient text effects using white-to-gray gradients for main text and blue-to-fuchsia gradients with animation for accent text in TraceStack.
- User prefers static gradients over animated ones for headings and prefers smaller font sizes for main headings in TraceStack.
- User prefers Codeforces-inspired gradient colors (shiny yellow, blue, red) instead of pink/fuchsia for TraceStack UI elements to match the official Codeforces logo aesthetic.
- User prefers legitimate, good-looking headings instead of poor styling in TraceStack UI.
- User prefers Codeforces API calls (user.status and user.info endpoints) to be made from frontend/client-side instead of backend to avoid IP blocking issues, with users making direct requests to fetch their own data.
- User reports TraceStack performance issues with high-profile users like tourist and petr, and is concerned about scalability for 10k users, indicating need for performance optimization.
- User prefers well-organized component folder structure with meaningful folder names in TraceStack to improve code maintainability and future understanding.
- User prefers pagination for solved problems display in TraceStack, showing 100 problems initially with 'show more' button to load 50 more at a time to prevent performance issues with users who have solved many problems (like 10k+ problems).
- User prefers button-triggered filtering instead of real-time filtering while typing to avoid unnecessary performance impact in TraceStack.
- User prefers using images.remotePatterns instead of deprecated images.domains configuration in Next.js for better future compatibility.
- User prefers reusable modal components in TraceStack with input fields and timer functionality (specifically 2-minute timers) using chancn model.
- User prefers modal components in TraceStack to have two-part structure: first part for Codeforces handle input without time constraints, second part with 2-minute timer display.
- User prefers the second part of the two-part modal to display clickable links to Codeforces problems for easy access during timed sessions.
- User prefers two-part modal components in TraceStack to be triggered by button clicks on the /profile page instead of using separate modal pages, wanting simple implementation.
- User prefers container-style buttons for verification features with specific titles like 'verify codeforces Profile' positioned above existing elements in TraceStack. The buttons should be positioned above existing elements in TraceStack.
- User prefers to remove all scrollbars from TraceStack pages as they don't look good, wanting this applied to all current and future pages for a cleaner UI appearance.
- User prefers verification sections in TraceStack to be more visible and good-looking, with reduced timer space and increased space for other content like submission instructions.
- User prefers verification instructions to be written in 4 concise points rather than copying existing text, wanting original content around the same concept.
- User prefers the first screen of modal components in TraceStack to be bigger and better looking with improved visual design.
- User prefers smaller modal sizes in TraceStack when the first screen becomes too large.
- User prefers verified Codeforces profiles in TraceStack to display the same text as 'verify codeforces profile' but with a green tick indicator to show certification status.

# Codeforces Rating Graphs

- User prefers interactive rating graphs with zoom functionality that shows daily granularity between contest dates.
- User prefers daily interpolation for rating graphs instead of showing only contest day data points.
- User prefers static reference lines on Codeforces rating graphs with specific colors for each rating range (Gray for Newbie 0-1199, Green for Pupil 1200-1399, Cyan for Specialist 1400-1599, Blue for Expert 1600-1899, Violet for Candidate Master 1900-2099, Orange for Master 2100-2299, bold Orange for International Master 2300-2399, Red for Grandmaster 2400-2599, bold Red for International GM 2600-2999, Maroon/Black for Legendary GM 3000+) and wants numeric rating values displayed on the left axis instead of rating titles.
- Codeforces changed their rating system around May 2020 to start from 0, so TraceStack should use the user's actual maximum rating from their profile instead of assuming a fixed maximum like 1500 for rating graph scaling.
- User prefers rating graphs to dynamically scale the y-axis to focus on the user's performance range.
- User prefers moderate graph heights rather than very large ones for better UX.
- User prefers interactive graph tooltips on hover showing rating + date with clickable 'Set as Start' and 'Set as End' options, with validation that end date must be after start date and appropriate error messaging.
- User prefers tooltips/cards to be positioned in better areas (not overlapping chart) and charts should not rerender until both start and end points are selected for better UX.
- User prefers graphs to not rerender when setting start points to avoid visual animation repetition, and wants zoom-based range selection instead for better UX.
- User prefers removing quick zoom functionality from graphs and making range selection controls wider to avoid scrolling for better user-friendliness.
- User prefers tabs (selected point, selected range, date range, etc.) to be positioned horizontally below the graph rather than in other locations.
- User prefers Date Range Filter to take only 15% of page width and Selected Range section to be very wide for easy access to start/end point controls without scrolling in TraceStack rating graphs.
- User prefers cleaner UI for filter sections with a reset button instead of a refresh button that clears selected points (start/end) and resets date range filter to initial state, and balanced width distribution between selected point and selected range sections.
- User prefers hover tooltips on graphs to intelligently display problem lists without overwhelming the UI when many problems are solved, avoiding ugly/cluttered hover cards that fill the entire page.
- User prefers start/end point selection buttons to be positioned below headers and above graphs for better accessibility, following the same pattern as range selection buttons.
- User prefers graph interaction buttons (Set as Start/End) to be pre-rendered as semi-transparent placeholders rather than dynamically spawned to prevent page rerendering and UI disruption when clicking on graphs.

# Sorting Visualizations

- User prefers red color for highlighting elements being compared in sorting visualizations.
- User prefers smooth slider interactions with 0.5 second debouncing to reduce lag and improve performance.
- User prefers debounced search functionality to prevent lag in sliders and interactive elements.
- User prefers slider components to include text input fields alongside sliding functionality, specifically for date ranges with month/year inputs on left (start) and right (end) sides.
- User prefers no date range constraints (removing 6-month limit).
- User prefers sorting visualizations to have black backgrounds, headings positioned outside the canvas at the top, and CSS styling that follows patterns from the /visualize/tree page for consistency.