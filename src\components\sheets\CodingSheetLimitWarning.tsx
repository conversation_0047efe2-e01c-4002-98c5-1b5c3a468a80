"use client";

import { MAX_CODING_SHEET_PROBLEMS } from "@/lib/codingSheet";
import React from "react";

// ============================================================================
// CODING SHEET LIMIT WARNING COMPONENT
// ============================================================================
// Displays warnings and information about coding sheet problem limits
// Provides visual feedback when approaching or exceeding the maximum limit

interface CodingSheetLimitWarningProps {
  problemCount: number; // Current number of problems
  className?: string; // Optional additional CSS classes
}

export const CodingSheetLimitWarning: React.FC<
  CodingSheetLimitWarningProps
> = ({ problemCount, className = "" }) => {
  // Calculate warning thresholds
  const isOverLimit = problemCount > MAX_CODING_SHEET_PROBLEMS;
  const isApproachingLimit =
    problemCount > MAX_CODING_SHEET_PROBLEMS * 0.75 && !isOverLimit;
  const isNearLimit =
    problemCount > MAX_CODING_SHEET_PROBLEMS * 0.9 && !isOverLimit;

  // Don't render anything if we're well under the limit
  if (!isOverLimit && !isApproachingLimit && !isNearLimit) {
    return null;
  }

  return (
    <div className={`text-xs mt-1 ${className}`}>
      {isOverLimit && (
        <div className="flex items-center gap-1 text-red-400">
          <span>🚫</span>
          <span>
            Cannot create sheet: {MAX_CODING_SHEET_PROBLEMS} problems max
            (currently {problemCount})
          </span>
        </div>
      )}

      {isNearLimit && !isOverLimit && (
        <div className="flex items-center gap-1 text-red-400">
          <span>🔥</span>
          <span>
            Very close to limit: {MAX_CODING_SHEET_PROBLEMS} problems max
          </span>
        </div>
      )}

      {isApproachingLimit && !isNearLimit && (
        <div className="flex items-center gap-1 text-orange-400">
          <span>⚡</span>
          <span>
            Approaching limit: {MAX_CODING_SHEET_PROBLEMS} problems max
          </span>
        </div>
      )}

      {(isOverLimit || isApproachingLimit || isNearLimit) && (
        <div className="text-gray-400 mt-1">
          💡 Tip: Use filters (tags, rating range, date range) to reduce the
          number of problems before creating a sheet
        </div>
      )}
    </div>
  );
};

// ============================================================================
// PROBLEM COUNT DISPLAY COMPONENT
// ============================================================================
// Displays the problem count with appropriate color coding based on limits

interface ProblemCountDisplayProps {
  problemCount: number; // Current number of problems
  className?: string; // Optional additional CSS classes
}

export const ProblemCountDisplay: React.FC<ProblemCountDisplayProps> = ({
  problemCount,
  className = "",
}) => {
  // Determine color based on problem count relative to limit
  const getCountColor = () => {
    if (problemCount > MAX_CODING_SHEET_PROBLEMS) {
      return "text-red-400"; // Over limit - red (cannot create)
    }
    if (problemCount > MAX_CODING_SHEET_PROBLEMS * 0.9) {
      return "text-red-400"; // Very close to limit - red warning
    }
    if (problemCount > MAX_CODING_SHEET_PROBLEMS * 0.75) {
      return "text-orange-400"; // Approaching limit - orange warning
    }
    return "text-green-400"; // Safe range - green
  };

  return (
    <span className={`font-semibold ${getCountColor()} ${className}`}>
      {problemCount}
    </span>
  );
};

// ============================================================================
// COMBINED CODING SHEET INFO COMPONENT
// ============================================================================
// Combines problem count display and limit warning in a single component

interface CodingSheetInfoProps {
  problemCount: number; // Current number of problems
  className?: string; // Optional additional CSS classes
}

export const CodingSheetInfo: React.FC<CodingSheetInfoProps> = ({
  problemCount,
  className = "",
}) => {
  return (
    <div className={`text-sm ${className}`}>
      <div className="text-gray-400">
        Create a personalized coding sheet from these{" "}
        <ProblemCountDisplay problemCount={problemCount} /> problems
      </div>
      <CodingSheetLimitWarning problemCount={problemCount} />
    </div>
  );
};
