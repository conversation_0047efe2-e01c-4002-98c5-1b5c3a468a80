"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useCodeforcesUserInfo } from "@/hooks/useCodeforcesUserInfo";
import { useAuth } from "@/lib/auth-context";
import {
  formatLastOnline,
  formatMemberSince,
  formatNumber,
  formatRegistrationDate,
  getContributionColor,
  getRankColor,
  getRatingProgress,
} from "@/lib/profile-utils";
import { useQuery } from "@tanstack/react-query";
import {
  Activity,
  Award,
  Building,
  Calendar,
  Clock,
  Code,
  FileText,
  Heart,
  MapPin,
  Star,
  Target,
  TrendingUp,
  Trophy,
  User,
  Users,
} from "lucide-react";
import { useMemo } from "react";
import DailyTopRatedProblems from "./DailyTopRatedProblems";
import DifficultyAnalysis from "./DifficultyAnalysis";
import ProblemRatingDistribution from "./ProblemRatingDistribution";
import RatingHistory from "./RatingHistory";
import Recent<PERSON>ontests from "./RecentContests";
import SubmissionOverview from "./SubmissionOverview";

// Types for user data
interface UserProfile {
  id: number;
  handle: string;
  rating?: number;
  rank?: string;
  streak?: number;
  training_streak: number;
  max_training_streak: number;
  maxSheetSlots?: number;
  createdAt: string;
  updatedAt: string;
  email: string;
  totalSheets: number;
  availableSheetSlots: number;
  sheets: Array<{
    id: number;
    name: string;
    createdAt: string;
  }>;
  supabaseUser: {
    id: string;
    email: string;
    created_at: string;
    user_metadata: any;
  };
}

const OverviewTab = () => {
  const { user } = useAuth();

  // Memoize user email to prevent unnecessary query key changes
  const userEmail = useMemo(() => user?.email, [user?.email]);

  // Fetch user profile data from our API with optimized configuration
  const { data: userProfile, isLoading: profileLoading } =
    useQuery<UserProfile>({
      queryKey: ["userProfile", userEmail],
      queryFn: async () => {
        const response = await fetch("/api/userInfo");
        if (!response.ok) {
          throw new Error("Failed to fetch user profile");
        }
        return response.json();
      },
      enabled: !!userEmail,
      // Optimize caching for user profile data
      staleTime: 20 * 60 * 1000, // 20 minutes - profile changes very infrequently
      gcTime: 60 * 60 * 1000, // 1 hour cache
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false, // Don't refetch on mount if data is fresh
      retry: (failureCount, error: any) => {
        // Don't retry if user is not found (404)
        if (error?.response?.status === 404) {
          return false;
        }
        return failureCount < 1; // Reduced retries
      },
    });

  // Memoize handle to prevent unnecessary query key changes
  const codeforcesHandle = useMemo(
    () => userProfile?.handle || "",
    [userProfile?.handle]
  );

  // Fetch Codeforces profile data with optimized configuration
  const { data: codeforcesProfile } = useCodeforcesUserInfo({
    handle: codeforcesHandle,
    enabled: !!codeforcesHandle.trim(),
  });

  // Get the color associated with a rating (matches official Codeforces colors)
  const getRatingColor = (rating: number): string => {
    if (rating >= 3000) return "#8B0000"; // Legendary Grandmaster (Maroon)
    if (rating >= 2600) return "#ff0000"; // International Grandmaster (red)
    if (rating >= 2400) return "#ff0000"; // Grandmaster (red)
    if (rating >= 2300) return "#ff8c00"; // International Master (orange)
    if (rating >= 2100) return "#ff8c00"; // Master (orange)
    if (rating >= 1900) return "#aa00aa"; // Candidate Master (purple)
    if (rating >= 1600) return "#0000ff"; // Expert (blue)
    if (rating >= 1400) return "#00aaaa"; // Specialist (cyan)
    if (rating >= 1200) return "#008000"; // Pupil (green)
    return "#808080"; // Newbie (gray)
  };

  // Calculate rating progress using Codeforces API rating (preferred) or database rating as fallback
  const currentRating = codeforcesProfile?.rating || userProfile?.rating || 0;
  const ratingProgress =
    currentRating > 0
      ? getRatingProgress(currentRating)
      : { percentage: 0, pointsNeeded: 0, nextRank: "Unrated" };

  return (
    <div className="space-y-6">
      {/* Main Dashboard Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Profile Overview - Left Column */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile Card */}
          <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-6">
                {/* Dynamic Rating Visualization */}
                <div className="w-[140px] h-[140px] relative">
                  {/* Outer ring - Progress to next rank */}
                  <svg
                    className="w-full h-full transform -rotate-90"
                    viewBox="0 0 140 140"
                  >
                    {/* Background circle */}
                    <circle
                      cx="70"
                      cy="70"
                      r="60"
                      stroke="rgba(59, 130, 246, 0.15)"
                      strokeWidth="6"
                      fill="none"
                    />
                    {/* Progress circle */}
                    <circle
                      cx="70"
                      cy="70"
                      r="60"
                      stroke={getRatingColor(currentRating)}
                      strokeWidth="6"
                      fill="none"
                      strokeLinecap="round"
                      strokeDasharray={`${2 * Math.PI * 60}`}
                      strokeDashoffset={`${
                        2 * Math.PI * 60 * (1 - ratingProgress.percentage / 100)
                      }`}
                      className="transition-all duration-1000 ease-out"
                      style={{
                        filter: `drop-shadow(0 0 6px ${getRatingColor(
                          currentRating
                        )}30)`,
                      }}
                    />
                  </svg>

                  {/* Center content */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    {/* Rating badge */}
                    {profileLoading ? (
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    ) : (
                      <div
                        className="text-3xl font-bold mb-1"
                        style={{
                          color: getRatingColor(currentRating),
                        }}
                      >
                        {currentRating || "?"}
                      </div>
                    )}

                    {/* Rank with icon */}
                    {!profileLoading && (
                      <div className="flex items-center gap-1.5">
                        {currentRating >= 2400 ? (
                          <Trophy className="w-4 h-4 text-yellow-400" />
                        ) : currentRating >= 1900 ? (
                          <Award className="w-4 h-4 text-purple-400" />
                        ) : currentRating >= 1600 ? (
                          <Star className="w-4 h-4 text-blue-400" />
                        ) : (
                          <Target className="w-4 h-4 text-green-400" />
                        )}
                        <span className="text-sm text-gray-300 font-medium">
                          {codeforcesProfile?.rank?.split(" ").slice(-1)[0] ||
                            userProfile?.rank?.split(" ").slice(-1)[0] ||
                            "Unrated"}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Subtle floating particles */}
                  {!profileLoading && (
                    <div className="absolute inset-0 pointer-events-none">
                      {[...Array(4)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 rounded-full animate-pulse"
                          style={{
                            backgroundColor: getRatingColor(currentRating),
                            opacity: 0.4,
                            top: `${30 + Math.sin((i * Math.PI) / 2) * 35}%`,
                            left: `${50 + Math.cos((i * Math.PI) / 2) * 35}%`,
                            animationDelay: `${i * 0.5}s`,
                            animationDuration: "3s",
                          }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {profileLoading ? (
                <div className="space-y-2">
                  <div className="h-6 bg-slate-700/50 rounded animate-pulse"></div>
                  <div className="h-4 bg-slate-700/50 rounded animate-pulse w-3/4 mx-auto"></div>
                </div>
              ) : userProfile ? (
                <>
                  <CardTitle
                    className="text-2xl font-bold mb-2"
                    style={{ color: getRatingColor(currentRating) }}
                  >
                    {codeforcesProfile?.handle || userProfile.handle}
                  </CardTitle>

                  {(codeforcesProfile?.rank || userProfile.rank) && (
                    <div
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border"
                      style={{
                        color: getRankColor(
                          codeforcesProfile?.rank || userProfile.rank
                        ),
                        borderColor:
                          getRankColor(
                            codeforcesProfile?.rank || userProfile.rank
                          ) + "40",
                        backgroundColor:
                          getRankColor(
                            codeforcesProfile?.rank || userProfile.rank
                          ) + "10",
                      }}
                    >
                      <Award className="w-3 h-3 mr-1" />
                      {codeforcesProfile?.rank || userProfile.rank}
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center">
                  <p className="text-gray-400 mb-2">No profile data found</p>
                  <p className="text-sm text-gray-500">
                    Please verify your Codeforces handle.
                  </p>
                </div>
              )}
            </CardHeader>

            <CardContent className="space-y-4">
              {profileLoading ? (
                <div className="space-y-3">
                  <div className="h-4 bg-slate-700/50 rounded animate-pulse"></div>
                  <div className="h-4 bg-slate-700/50 rounded animate-pulse"></div>
                  <div className="h-4 bg-slate-700/50 rounded animate-pulse"></div>
                </div>
              ) : userProfile ? (
                <>
                  {/* Progress to next rank */}
                  {currentRating > 0 && ratingProgress.pointsNeeded > 0 && (
                    <div className="text-center">
                      <p className="text-xs text-gray-400 mb-2">
                        {ratingProgress.pointsNeeded} points to{" "}
                        {ratingProgress.nextRank}
                      </p>
                    </div>
                  )}

                  {/* Member Since */}
                  <div className="flex items-center gap-2 text-gray-300">
                    <Calendar className="w-4 h-4 text-blue-400" />
                    <span className="text-sm">
                      Member since {formatMemberSince(userProfile.createdAt)}
                    </span>
                  </div>

                  {/* Email */}
                  <div className="flex items-center gap-2 text-gray-300">
                    <User className="w-4 h-4 text-blue-400" />
                    <span className="text-sm">{userProfile.email}</span>
                  </div>
                </>
              ) : null}
            </CardContent>
          </Card>

          {/* Statistics Card */}
          <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
                <Activity className="w-5 h-5 text-blue-400" />
                Trainer Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {profileLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="h-4 bg-slate-700/50 rounded animate-pulse w-1/2"></div>
                      <div className="h-4 bg-slate-700/50 rounded animate-pulse w-8"></div>
                    </div>
                  ))}
                </div>
              ) : userProfile ? (
                <>
                  {/* Current Streak */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-gray-300">
                        Current Streak
                      </span>
                    </div>
                    <span className="text-lg font-bold text-green-400">
                      {userProfile.streak || 0}
                    </span>
                  </div>

                  {/* Training Streak */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-gray-300">
                        Training Streak
                      </span>
                    </div>
                    <span className="text-lg font-bold text-blue-400">
                      {userProfile.training_streak}
                    </span>
                  </div>

                  {/* Max Training Streak */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Star className="w-4 h-4 text-yellow-400" />
                      <span className="text-sm text-gray-300">
                        Best Training Streak
                      </span>
                    </div>
                    <span className="text-lg font-bold text-yellow-400">
                      {userProfile.max_training_streak}
                    </span>
                  </div>

                  {/* Total Sheets */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4 text-purple-400" />
                      <span className="text-sm text-gray-300">
                        Custom Sheets
                      </span>
                    </div>
                    <span className="text-lg font-bold text-purple-400">
                      {userProfile.totalSheets}
                    </span>
                  </div>

                  {/* Available Sheet Slots */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Code className="w-4 h-4 text-cyan-400" />
                      <span className="text-sm text-gray-300">
                        Available Slots
                      </span>
                    </div>
                    <span className="text-lg font-bold text-cyan-400">
                      {userProfile.availableSheetSlots}
                    </span>
                  </div>
                </>
              ) : null}
            </CardContent>
          </Card>
        </div>

        {/* Main Content - Right Columns */}
        <div className="lg:col-span-2 flex flex-col gap-6">
          {/* Codeforces Profile Information */}
          <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl flex-grow">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
                <Trophy className="w-6 h-6 text-yellow-400" />
                Codeforces Profile
              </CardTitle>
            </CardHeader>
            <CardContent>
              {!codeforcesHandle ? (
                <div className="text-center py-8">
                  <p className="text-gray-400">Loading Codeforces profile...</p>
                </div>
              ) : !codeforcesProfile ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Loading skeleton */}
                  <div className="space-y-4">
                    {[...Array(4)].map((_, i) => (
                      <div key={i} className="p-3 bg-slate-800/50 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="h-4 bg-slate-700/50 rounded animate-pulse w-1/2"></div>
                          <div className="h-6 bg-slate-700/50 rounded animate-pulse w-16"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="p-3 bg-slate-800/50 rounded-lg">
                        <div className="h-4 bg-slate-700/50 rounded animate-pulse w-1/3 mb-2"></div>
                        <div className="h-4 bg-slate-700/50 rounded animate-pulse w-2/3"></div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="space-y-4">
                    {/* Current Rating */}
                    {codeforcesProfile.rating && (
                      <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="w-4 h-4 text-blue-400" />
                          <span className="text-sm text-gray-300">
                            Current Rating
                          </span>
                        </div>
                        <span
                          className="text-xl font-bold"
                          style={{
                            color: getRatingColor(codeforcesProfile.rating),
                          }}
                        >
                          {codeforcesProfile.rating}
                        </span>
                      </div>
                    )}

                    {/* Max Rating */}
                    {codeforcesProfile.maxRating && (
                      <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Star className="w-4 h-4 text-yellow-400" />
                          <span className="text-sm text-gray-300">
                            Max Rating
                          </span>
                        </div>
                        <span
                          className="text-xl font-bold"
                          style={{
                            color: getRatingColor(codeforcesProfile.maxRating),
                          }}
                        >
                          {codeforcesProfile.maxRating}
                        </span>
                      </div>
                    )}

                    {/* Contribution */}
                    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Heart className="w-4 h-4 text-red-400" />
                        <span className="text-sm text-gray-300">
                          Contribution
                        </span>
                      </div>
                      <span
                        className="text-xl font-bold"
                        style={{
                          color: getContributionColor(
                            codeforcesProfile.contribution
                          ),
                        }}
                      >
                        {codeforcesProfile.contribution > 0 ? "+" : ""}
                        {codeforcesProfile.contribution}
                      </span>
                    </div>

                    {/* Friends */}
                    <div className="flex items-center justify-between p-3 bg-slate-800/50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-green-400" />
                        <span className="text-sm text-gray-300">Friends</span>
                      </div>
                      <span className="text-xl font-bold text-green-400">
                        {formatNumber(codeforcesProfile.friendOfCount)}
                      </span>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-4">
                    {/* Registration Date */}
                    <div className="p-3 bg-slate-800/50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Calendar className="w-4 h-4 text-blue-400" />
                        <span className="text-sm text-gray-300">
                          Registered
                        </span>
                      </div>
                      <span className="text-white font-medium">
                        {formatRegistrationDate(
                          codeforcesProfile.registrationTimeSeconds
                        )}
                      </span>
                    </div>

                    {/* Last Online */}
                    <div className="p-3 bg-slate-800/50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="w-4 h-4 text-green-400" />
                        <span className="text-sm text-gray-300">
                          Last Online
                        </span>
                      </div>
                      <span className="text-white font-medium">
                        {formatLastOnline(
                          codeforcesProfile.lastOnlineTimeSeconds
                        )}
                      </span>
                    </div>

                    {/* Location */}
                    {(codeforcesProfile.country || codeforcesProfile.city) && (
                      <div className="p-3 bg-slate-800/50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <MapPin className="w-4 h-4 text-purple-400" />
                          <span className="text-sm text-gray-300">
                            Location
                          </span>
                        </div>
                        <span className="text-white font-medium">
                          {[codeforcesProfile.city, codeforcesProfile.country]
                            .filter(Boolean)
                            .join(", ")}
                        </span>
                      </div>
                    )}

                    {/* Organization */}
                    {codeforcesProfile.organization && (
                      <div className="p-3 bg-slate-800/50 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Building className="w-4 h-4 text-orange-400" />
                          <span className="text-sm text-gray-300">
                            Organization
                          </span>
                        </div>
                        <span className="text-white font-medium">
                          {codeforcesProfile.organization}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Daily Top-Rated Problems Solved Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <DailyTopRatedProblems handle={codeforcesHandle} />
        </div>
      )}

      {/* Rating History Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <RatingHistory handle={codeforcesHandle} />
        </div>
      )}

      {/* Recent Contests Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <RecentContests handle={codeforcesHandle} />
        </div>
      )}

      {/* Problem Rating Distribution Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <ProblemRatingDistribution
            handle={codeforcesHandle}
            userRating={currentRating}
          />
        </div>
      )}

      {/* Difficulty Category Analysis Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <DifficultyAnalysis handle={codeforcesHandle} />
        </div>
      )}

      {/* Submission Overview Section */}
      {codeforcesHandle && (
        <div className="mt-6">
          <SubmissionOverview handle={codeforcesHandle} />
        </div>
      )}
    </div>
  );
};

export default OverviewTab;
