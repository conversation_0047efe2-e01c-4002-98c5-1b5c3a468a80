"use client";

import { createClient } from "@/lib/supabase/client";
import type { User } from "@supabase/supabase-js";
import { createContext, useContext, useEffect, useMemo, useState } from "react";

type AuthContextType = {
  user: User | null;
  loading: boolean;
};

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Memoize the Supabase client to prevent recreation on every render
  // This is critical to prevent cascading refetches in dependent components
  const supabase = useMemo(() => createClient(), []);

  useEffect(() => {
    // TEMPORARY: Hardcode user for development
    // if (process.env.NODE_ENV === "development") {
    //   const mockUser = {
    //     id: "dev-user-123",
    //     email: "<EMAIL>",
    //     created_at: new Date().toISOString(),
    //     user_metadata: {},
    //     app_metadata: {},
    //     aud: "authenticated",
    //     role: "authenticated",
    //   } as User;

    //   setUser(mockUser);
    //   setLoading(false);
    //   return;
    // }

    // Get initial session
    const getInitialSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, [supabase.auth]);

  return (
    <AuthContext.Provider value={{ user, loading }}>
      {children}
    </AuthContext.Provider>
  );
}
