"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useAllSubmissionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import { Activity, Code, Target } from "lucide-react";
import { useMemo } from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts";

// Types for submission overview data
interface VerdictData {
  name: string;
  value: number;
  color: string;
}

interface LanguageData {
  name: string;
  value: number;
  color: string;
}

interface SubmissionStats {
  totalSubmissions: number;
  acceptedSubmissions: number;
  successRate: number;
  languageCount: number;
}

interface SubmissionOverviewProps {
  handle: string;
}

// Verdict colors matching Codeforces theme
const VERDICT_COLORS = {
  OK: "#22c55e", // Green for accepted
  WRONG_ANSWER: "#ef4444", // Red for wrong answer
  TIME_LIMIT_EXCEEDED: "#f97316", // Orange for TLE
  MEMORY_LIMIT_EXCEEDED: "#8b5cf6", // Purple for MLE
  COMPILATION_ERROR: "#ec4899", // Pink for CE
  RUNTIME_ERROR: "#f59e0b", // Amber for RE
  PRESENTATION_ERROR: "#06b6d4", // Cyan for PE
  IDLENESS_LIMIT_EXCEEDED: "#64748b", // Slate for ILE
  SECURITY_VIOLATED: "#dc2626", // Dark red for security
  CRASHED: "#991b1b", // Very dark red for crashed
  INPUT_PREPARATION_CRASHED: "#7c2d12", // Brown for input crash
  CHALLENGED: "#0ea5e9", // Sky blue for challenged
  SKIPPED: "#6b7280", // Gray for skipped
  TESTING: "#fbbf24", // Yellow for testing
  REJECTED: "#9333ea", // Violet for rejected
  PARTIAL: "#14b8a6", // Teal for partial
  OTHER: "#71717a", // Neutral gray for others
};

// Language colors - vibrant colors for popular languages
const LANGUAGE_COLORS = [
  "#3b82f6", // Blue
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Violet
  "#06b6d4", // Cyan
  "#f97316", // Orange
  "#ec4899", // Pink
  "#84cc16", // Lime
  "#6366f1", // Indigo
  "#14b8a6", // Teal
  "#f43f5e", // Rose
];

const SubmissionOverview = ({ handle }: SubmissionOverviewProps) => {
  // Fetch all submissions data (not just solved ones)
  const {
    data: submissionsData,
    isLoading,
    error,
  } = useAllSubmissionsClient({
    handle,
    enabled: !!handle.trim(),
  });

  // Process submissions data
  const { stats, verdictData, languageData } = useMemo(() => {
    if (!submissionsData?.result?.submissions) {
      return {
        stats: {
          totalSubmissions: 0,
          acceptedSubmissions: 0,
          successRate: 0,
          languageCount: 0,
        },
        verdictData: [],
        languageData: [],
      };
    }

    const submissions = submissionsData.result.submissions;

    // Calculate basic stats
    const totalSubmissions = submissions.length;
    const acceptedSubmissions = submissions.filter(
      (s) => s.verdict === "OK"
    ).length;
    const successRate =
      totalSubmissions > 0 ? (acceptedSubmissions / totalSubmissions) * 100 : 0;

    // Process verdict distribution
    const verdictCounts = new Map<string, number>();
    submissions.forEach((submission) => {
      const verdict = submission.verdict || "OTHER";
      verdictCounts.set(verdict, (verdictCounts.get(verdict) || 0) + 1);
    });

    const verdictData: VerdictData[] = Array.from(verdictCounts.entries())
      .map(([verdict, count]) => ({
        name: verdict.replace(/_/g, " "),
        value: count,
        color:
          VERDICT_COLORS[verdict as keyof typeof VERDICT_COLORS] ||
          VERDICT_COLORS.OTHER,
      }))
      .sort((a, b) => b.value - a.value);

    // Process language distribution
    const languageCounts = new Map<string, number>();
    submissions.forEach((submission) => {
      const language = submission.programmingLanguage;
      // Simplify language names (remove version numbers and extra info)
      const simplifiedLanguage = language
        .replace(/\s*\([^)]*\)/g, "") // Remove parentheses and content
        .replace(/\d+/g, "") // Remove numbers
        .replace(/\s+/g, " ") // Normalize spaces
        .trim();

      languageCounts.set(
        simplifiedLanguage,
        (languageCounts.get(simplifiedLanguage) || 0) + 1
      );
    });

    const languageData: LanguageData[] = Array.from(languageCounts.entries())
      .map(([language, count], index) => ({
        name: language,
        value: count,
        color: LANGUAGE_COLORS[index % LANGUAGE_COLORS.length],
      }))
      .sort((a, b) => b.value - a.value);

    const languageCount = languageData.length;

    return {
      stats: {
        totalSubmissions,
        acceptedSubmissions,
        successRate,
        languageCount,
      },
      verdictData,
      languageData,
    };
  }, [submissionsData]);

  if (isLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-gray-400">Loading submission overview...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !submissionsData) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-gray-400">Failed to load submission data</p>
        </CardContent>
      </Card>
    );
  }

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const percentage = ((data.value / stats.totalSubmissions) * 100).toFixed(
        1
      );
      return (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg">
          <p className="text-white font-medium">{data.payload.name}</p>
          <p className="text-gray-300">
            Count: <span className="text-white font-bold">{data.value}</span>
          </p>
          <p className="text-gray-300">
            Percentage:{" "}
            <span className="text-white font-bold">{percentage}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
          <Activity className="w-6 h-6 text-blue-400" />
          Submission Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-green-400 mb-1">
              {stats.acceptedSubmissions}
            </div>
            <div className="text-sm text-gray-300">Accepted</div>
          </div>

          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-blue-400 mb-1">
              {stats.totalSubmissions}
            </div>
            <div className="text-sm text-gray-300">Total</div>
          </div>

          <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-purple-400 mb-1">
              {stats.successRate.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-300">Success Rate</div>
          </div>

          <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-orange-400 mb-1">
              {stats.languageCount}
            </div>
            <div className="text-sm text-gray-300">Languages</div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Verdict Distribution */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <Target className="w-5 h-5 text-red-400" />
              Verdict Distribution
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={verdictData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {verdictData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            {/* Verdict Legend */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              {verdictData.slice(0, 6).map((entry, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-gray-300 truncate">
                    {entry.name} ({entry.value})
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Programming Languages */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white flex items-center gap-2">
              <Code className="w-5 h-5 text-blue-400" />
              Programming Languages
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={languageData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={120}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {languageData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
            </div>
            {/* Language Legend */}
            <div className="grid grid-cols-2 gap-2 text-xs">
              {languageData.slice(0, 6).map((entry, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-gray-300 truncate">
                    {entry.name} ({entry.value})
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubmissionOverview;
