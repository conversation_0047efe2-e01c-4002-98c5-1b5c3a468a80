"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Significantly increase staleTime to reduce unnecessary refetches
            // Most data doesn't change frequently, especially user profiles and codeforces data
            staleTime: 15 * 60 * 1000, // 15 minutes - much more aggressive caching

            // Increase cache time to keep data in cache much longer
            gcTime: 60 * 60 * 1000, // 1 hour - keep data in memory longer

            // Don't refetch on window focus by default for dashboard data
            refetchOnWindowFocus: false,

            // Don't refetch on reconnect for static data
            refetchOnReconnect: false,

            // Don't refetch on mount if data is still fresh
            refetchOnMount: false,

            // Retry configuration to prevent excessive retries
            retry: (failureCount, error: any) => {
              // Don't retry 4xx errors (client errors)
              if (
                error?.response?.status >= 400 &&
                error?.response?.status < 500
              ) {
                return false;
              }
              // Retry only once for server errors to reduce load
              return failureCount < 1;
            },

            // Longer retry delay to reduce server load
            retryDelay: (attemptIndex) =>
              Math.min(2000 * 2 ** attemptIndex, 60000), // Start at 2s, max 60s
          },
        },
      })
  );
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
