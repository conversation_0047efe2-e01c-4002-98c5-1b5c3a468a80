"use client";

import SortingVisualizer from "@/components/visualizations/SortingVisualizer";
import {
  ArrowUpDown,
  BarChart3,
  Binary,
  Code2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Layers,
  RotateCw,
  Shuffle,
} from "lucide-react";
import { useEffect, useState } from "react";

// Floating particles interface
interface Particle {
  id: number;
  x: number;
  y: number;
  delay: number;
  duration: number;
}

// Floating icons interface
interface FloatingIcon {
  id: number;
  icon: any;
  x: number;
  y: number;
  delay: number;
  size: number;
}

// Animated sorting bars interface
interface SortingBar {
  id: number;
  height: number;
  x: number;
  delay: number;
}

// Background array interface
interface BackgroundArray {
  id: number;
  elements: number[];
  x: number;
  y: number;
  rotation: number;
  state: "unsorted" | "sorting" | "sorted";
  delay: number;
}

const page = () => {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [floatingIcons, setFloatingIcons] = useState<FloatingIcon[]>([]);
  const [sortingBars, setSortingBars] = useState<SortingBar[]>([]);
  const [backgroundArrays, setBackgroundArrays] = useState<BackgroundArray[]>(
    []
  );

  // Array of sorting/programming related icons
  const iconSet = [
    ArrowUpDown,
    BarChart3,
    Binary,
    Code2,
    GitCompare,
    Layers,
    RotateCw,
    Shuffle,
  ];

  useEffect(() => {
    // Initialize floating particles
    setParticles(
      Array.from({ length: 20 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 15,
        duration: 12 + Math.random() * 8,
      }))
    );

    // Initialize floating icons
    setFloatingIcons(
      Array.from({ length: 8 }, (_, i) => ({
        id: i,
        icon: iconSet[Math.floor(Math.random() * iconSet.length)],
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 10,
        size: 16 + Math.random() * 8, // Random size between 16-24px
      }))
    );

    // Initialize animated sorting bars
    setSortingBars(
      Array.from({ length: 12 }, (_, i) => ({
        id: i,
        height: 20 + Math.random() * 60,
        x: i * 8 + 5,
        delay: i * 0.3,
      }))
    );

    // Initialize background arrays
    const arrayStates: ("unsorted" | "sorting" | "sorted")[] = [
      "unsorted",
      "sorting",
      "sorted",
    ];
    setBackgroundArrays(
      Array.from({ length: 6 }, (_, i) => ({
        id: i,
        elements: Array.from(
          { length: 5 + Math.floor(Math.random() * 3) },
          () => Math.floor(Math.random() * 99) + 1
        ),
        x: 10 + Math.random() * 80,
        y: 10 + Math.random() * 80,
        rotation: Math.random() * 30 - 15, // -15 to 15 degrees
        state: arrayStates[Math.floor(Math.random() * arrayStates.length)],
        delay: Math.random() * 5,
      }))
    );
  }, []);

  const getArrayStateColor = (state: string) => {
    switch (state) {
      case "unsorted":
        return "text-blue-300/25";
      case "sorting":
        return "text-blue-400/30";
      case "sorted":
        return "text-blue-500/35";
      default:
        return "text-blue-300/25";
    }
  };

  return (
    <main className="min-h-screen bg-black/[0.96] antialiased bg-grid-white/[0.02] relative overflow-hidden mt-16">
      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move"
        style={{
          backgroundSize: "30px 30px",
        }}
      />

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/30 via-transparent to-blue-900/25" />

      {/* Background Arrays */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {backgroundArrays.map((bgArray) => (
          <div
            key={bgArray.id}
            className={`absolute font-mono text-sm ${getArrayStateColor(
              bgArray.state
            )} animate-pulse select-none`}
            style={{
              left: `${bgArray.x}%`,
              top: `${bgArray.y}%`,
              transform: `rotate(${bgArray.rotation}deg)`,
              animationDelay: `${bgArray.delay}s`,
              animationDuration: "4s",
            }}
          >
            <div className="flex items-center gap-1">
              <span className="text-blue-400/30">[</span>
              {bgArray.elements.map((element, index) => (
                <span key={index} className="flex items-center">
                  <span
                    className={
                      bgArray.state === "sorting" && index === 0
                        ? "text-blue-500/50"
                        : ""
                    }
                  >
                    {element}
                  </span>
                  {index < bgArray.elements.length - 1 && (
                    <span className="text-blue-400/20 mx-1">,</span>
                  )}
                </span>
              ))}
              <span className="text-blue-400/30">]</span>
            </div>
            {/* Array state indicator */}
            <div className="text-xs mt-1 text-blue-400/20 capitalize">
              {bgArray.state}
            </div>
          </div>
        ))}
      </div>
      {/* test */}
      {/* 
      
      */}

      {/* Array operation indicators */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="absolute top-20 left-20 text-blue-400/25 font-mono text-xs animate-bounce"
          style={{ animationDelay: "0s", animationDuration: "3s" }}
        >
          arr[i] &lt; arr[j]
        </div>
        <div
          className="absolute top-1/3 right-24 text-blue-500/25 font-mono text-xs animate-bounce"
          style={{ animationDelay: "1s", animationDuration: "3.5s" }}
        >
          swap(arr[i], arr[j])
        </div>
        <div
          className="absolute bottom-1/3 left-32 text-blue-400/25 font-mono text-xs animate-bounce"
          style={{ animationDelay: "2s", animationDuration: "4s" }}
        >
          for i in range(n)
        </div>
        <div
          className="absolute bottom-20 right-20 text-blue-500/25 font-mono text-xs animate-bounce"
          style={{ animationDelay: "0.5s", animationDuration: "3.2s" }}
        >
          sorted: true
        </div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-1 h-1 bg-blue-400/30 rounded-full"
            style={{
              left: `${particle.x}%`,
              top: `${particle.y}%`,
              animation: `particleFloat ${particle.duration}s ease-in-out infinite`,
              animationDelay: `${particle.delay}s`,
            }}
          />
        ))}
      </div>

      {/* Floating algorithm icons */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {floatingIcons.map((item) => {
          const IconComponent = item.icon;
          return (
            <div
              key={item.id}
              className="absolute text-blue-300/20 animate-bounce"
              style={{
                left: `${item.x}%`,
                top: `${item.y}%`,
                animationDelay: `${item.delay}s`,
                animationDuration: "4s",
              }}
            >
              <IconComponent size={item.size} />
            </div>
          );
        })}
      </div>

      {/* Animated sorting bars in background */}
      <div className="absolute top-20 left-10 pointer-events-none opacity-10">
        <div className="flex items-end gap-1 h-20">
          {sortingBars.map((bar) => (
            <div
              key={bar.id}
              className="bg-gradient-to-t from-blue-600 via-blue-500 to-blue-400 w-2 rounded-t animate-pulse"
              style={{
                height: `${bar.height}px`,
                animationDelay: `${bar.delay}s`,
                animationDuration: "3s",
              }}
            />
          ))}
        </div>
      </div>

      {/* Another set of sorting bars in different corner */}
      <div className="absolute bottom-20 right-10 pointer-events-none opacity-10">
        <div className="flex items-end gap-1 h-16">
          {sortingBars.slice(0, 8).map((bar) => (
            <div
              key={`right-${bar.id}`}
              className="bg-gradient-to-t from-blue-700 via-blue-600 to-blue-500 w-2 rounded-t animate-pulse"
              style={{
                height: `${bar.height * 0.8}px`,
                animationDelay: `${bar.delay + 1}s`,
                animationDuration: "2.5s",
              }}
            />
          ))}
        </div>
      </div>

      {/* Geometric sorting arrows */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Sorting direction indicators */}
        <div className="absolute top-1/4 left-16 w-8 h-0.5 bg-gradient-to-r from-blue-400/30 to-blue-500/30 animate-pulse transform rotate-12" />
        <div
          className="absolute top-1/4 left-20 w-0 h-0 border-l-2 border-l-blue-500/30 border-t border-b border-t-transparent border-b-transparent animate-pulse"
          style={{ animationDelay: "0.5s" }}
        />

        <div
          className="absolute top-3/4 right-16 w-8 h-0.5 bg-gradient-to-l from-blue-400/30 to-blue-600/30 animate-pulse transform -rotate-12"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute top-3/4 right-20 w-0 h-0 border-r-2 border-r-blue-600/30 border-t border-b border-t-transparent border-b-transparent animate-pulse"
          style={{ animationDelay: "1.5s" }}
        />
      </div>

      {/* Comparison symbols */}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="absolute top-60 left-24 text-blue-400/20 font-mono text-lg animate-pulse"
          style={{ animationDelay: "0s" }}
        >
          &lt;
        </div>
        <div
          className="absolute top-40 right-24 text-blue-500/20 font-mono text-lg animate-pulse"
          style={{ animationDelay: "1s" }}
        >
          &gt;
        </div>
        <div
          className="absolute bottom-60 left-48 text-blue-400/20 font-mono text-lg animate-pulse"
          style={{ animationDelay: "2s" }}
        >
          ≤
        </div>
        <div
          className="absolute bottom-40 right-48 text-blue-600/20 font-mono text-lg animate-pulse"
          style={{ animationDelay: "1.5s" }}
        >
          ≥
        </div>
      </div>

      {/* Corner accent blurs with blue colors only */}
      <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-600/5 rounded-full blur-3xl" />
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-blue-400/3 rounded-full blur-3xl" />

      {/* Geometric lines representing array elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Horizontal lines representing arrays */}
        <div className="absolute top-1/3 left-0 w-1/4 h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent animate-pulse" />
        <div
          className="absolute top-2/3 right-0 w-1/3 h-px bg-gradient-to-l from-transparent via-blue-600/20 to-transparent animate-pulse"
          style={{ animationDelay: "1s" }}
        />

        {/* Vertical lines representing comparisons */}
        <div
          className="absolute left-1/5 top-0 w-px h-1/3 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent animate-pulse"
          style={{ animationDelay: "0.5s" }}
        />
        <div
          className="absolute right-1/5 bottom-0 w-px h-1/4 bg-gradient-to-t from-transparent via-blue-600/20 to-transparent animate-pulse"
          style={{ animationDelay: "1.5s" }}
        />
      </div>

      {/* Sorting Algorithms Visualization Section */}
      <div className="relative py-20 z-10">
        {/* Custom glowing background */}
        <div
          aria-hidden="true"
          className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-[400px] w-[400px] rounded-full bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 opacity-25 blur-3xl -z-10"
        />

        {/* Content */}
        <div className="relative text-center mx-auto px-4">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Visualize Sorting Algorithms
          </h1>
          <p className="text-lg text-gray-300 mb-12 max-w-3xl mx-auto">
            Watch how sorting algorithms work step by step with interactive
            animations and clear comparisons.
          </p>

          {/* Sorting Visualizer Container */}
          <div className="w-[95%] mx-auto">
            <SortingVisualizer />
          </div>
        </div>
      </div>
    </main>
  );
};

export default page;
