# 🚀 Complete Prisma Client Setup Guide for TraceStack

## 📋 What We've Accomplished

### ✅ Files Created/Modified:
1. **`src/app/api/database/route.ts`** - Complete CRUD API endpoints
2. **`src/lib/prisma.ts`** - Prisma client utility with BigInt handling
3. **`test-database-api.js`** - Test script for API endpoints

## 🔧 Step-by-Step Explanation

### Step 1: Understanding Your Existing Setup
Your project already had:
- ✅ Prisma packages installed (`@prisma/client`, `prisma`)
- ✅ Schema defined in `prisma/schema.prisma`
- ✅ Generated client in `src/generated/prisma`
- ✅ Database route file (empty)

### Step 2: Prisma Client Instance Management
**Why we use a global instance:**
```typescript
// In src/lib/prisma.ts
declare global {
  var prisma: PrismaClient | undefined
}

const prisma = globalThis.prisma || new PrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.prisma = prisma
}
```

**Explanation:**
- In development, Next.js hot reloading can create multiple Prisma instances
- This pattern ensures we reuse the same instance across hot reloads
- In production, each deployment gets a fresh instance

### Step 3: API Route Structure
Our `/api/database/route.ts` implements full CRUD operations:

#### 🔍 GET Endpoints
```typescript
// Get specific user
GET /api/database?handle=username&chatId=chat123

// Get all users with pagination
GET /api/database?page=1&limit=10
```

#### ➕ POST Endpoint
```typescript
// Create new user
POST /api/database
Content-Type: application/json
{
  "handle": "username",
  "chat_id": "chat123",
  "rating": 1500,
  "rank": "Expert"
}
```

#### ✏️ PUT Endpoint
```typescript
// Update existing user
PUT /api/database
Content-Type: application/json
{
  "handle": "username",
  "chat_id": "chat123",
  "rating": 1600
}
```

#### 🗑️ DELETE Endpoint
```typescript
// Delete user
DELETE /api/database?handle=username&chatId=chat123
```

### Step 4: BigInt Handling
**Problem:** PostgreSQL BigInt fields can't be directly serialized to JSON
**Solution:** Custom serialization helper

```typescript
export function serializeBigInt(obj: any): any {
  return JSON.parse(JSON.stringify(obj, (key, value) =>
    typeof value === 'bigint' ? Number(value) : value
  ))
}
```

## 🧪 Testing Your Setup

### Method 1: Using the Test Script
```bash
# Make sure your dev server is running
npm run dev

# In another terminal, run the test
node test-database-api.js
```

### Method 2: Using curl commands
```bash
# Create a user
curl -X POST http://localhost:3000/api/database \
  -H "Content-Type: application/json" \
  -d '{"handle":"testuser","chat_id":"chat123","rating":1500}'

# Get the user
curl "http://localhost:3000/api/database?handle=testuser&chatId=chat123"

# Update the user
curl -X PUT http://localhost:3000/api/database \
  -H "Content-Type: application/json" \
  -d '{"handle":"testuser","chat_id":"chat123","rating":1600}'

# Delete the user
curl -X DELETE "http://localhost:3000/api/database?handle=testuser&chatId=chat123"
```

### Method 3: Using a REST client (Postman, Insomnia, etc.)
Import the endpoints and test them manually.

## 🔒 Security Considerations

### Current Implementation:
- ✅ Input validation for required fields
- ✅ Error handling with appropriate HTTP status codes
- ✅ SQL injection protection (Prisma handles this)
- ✅ Type safety with TypeScript

### Recommended Additions:
- 🔄 Rate limiting
- 🔐 Authentication middleware
- 📝 Request logging
- 🛡️ Input sanitization

## 🚀 Next Steps

### 1. Add Authentication
```typescript
// Example middleware
export async function middleware(request: NextRequest) {
  const token = request.headers.get('authorization')
  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }
  // Verify token...
}
```

### 2. Add Validation
```bash
pnpm add zod
```

```typescript
import { z } from 'zod'

const createUserSchema = z.object({
  handle: z.string().min(1),
  chat_id: z.string().min(1),
  rating: z.number().optional(),
  // ... other fields
})
```

### 3. Add Error Logging
```bash
pnpm add winston
```

### 4. Add Database Migrations
```bash
# Generate migration
npx prisma migrate dev --name add_new_field

# Apply migrations in production
npx prisma migrate deploy
```

## 🔧 Common Issues & Solutions

### Issue 1: "Can't reach database server"
**Solution:** Check your `DATABASE_URL` in `.env`

### Issue 2: "BigInt serialization error"
**Solution:** Use our `serializeBigInt` helper function

### Issue 3: "Multiple Prisma instances"
**Solution:** Use the global instance pattern we implemented

### Issue 4: "Schema out of sync"
**Solution:** Run `npx prisma generate` to regenerate the client

## 📚 Key Prisma Concepts

### 1. **Client Generation**
- Run `npx prisma generate` after schema changes
- Client is generated to `src/generated/prisma`

### 2. **Database Operations**
```typescript
// Create
await prisma.users.create({ data: {...} })

// Read
await prisma.users.findMany()
await prisma.users.findFirst({ where: {...} })

// Update
await prisma.users.update({ where: {...}, data: {...} })

// Delete
await prisma.users.delete({ where: {...} })
```

### 3. **Transactions**
```typescript
await prisma.$transaction([
  prisma.users.create({...}),
  prisma.users.update({...})
])
```

## 🎯 Summary

You now have a fully functional Prisma-powered database API with:
- ✅ Complete CRUD operations
- ✅ Proper error handling
- ✅ BigInt serialization
- ✅ Pagination support
- ✅ TypeScript safety
- ✅ Development-friendly instance management

Your API is ready to use at `/api/database` with all HTTP methods supported!
