"use client";
import {
  <PERSON><PERSON>ronRight,
  FileCode2,
  Link,
  SendHorizonal,
  Sparkles,
  Target,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";

const sharedColor = {
  color: "from-blue-950 to-cyan-900", // super deep gradient
  bgColor: "bg-blue-950/40", // very dark background
  borderColor: "border-blue-900/30", // subtle border
  hoverBorder: "hover:border-cyan-800", // more contrast on hover
  glowColor: "shadow-[0_0_40px_rgba(8,145,178,0.5)]", // using a cyan glow
};

const steps = [
  {
    icon: <Link className="w-6 h-6" />,
    title: "Enter Problem Link",
    desc: "Paste the URL to the programming problem you're working on to provide context for the analysis.",
    ...sharedColor,
    accent: <Sparkles className="w-4 h-4" />,
  },
  {
    icon: <FileCode2 className="w-6 h-6" />,
    title: "Paste Your Code",
    desc: "Add your code solution into the editor. We support C++, Java, and Python.",
    ...sharedColor,
    accent: <Zap className="w-4 h-4" />,
  },
  {
    icon: <SendHorizonal className="w-6 h-6" />,
    title: "Submit & Analyze",
    desc: "Click the submit button to have your code traced and analyzed for bugs, performance issues, and logical errors.",
    ...sharedColor,
    accent: <Target className="w-4 h-4" />,
  },
];

export function HowItWorks() {
  const [activeStep, setActiveStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % steps.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative mt-24 max-w-4xl mx-auto px-6 pb-20">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-10 right-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "1s" }}
        />
        <div
          className="absolute top-1/2 left-1/2 w-64 h-64 bg-emerald-500/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "2s" }}
        />
      </div>

      {/* Enhanced title */}
      <div className="text-center mb-16 relative">
        <div className="inline-flex items-center gap-3 mb-4">
          <div className="w-12 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent" />
          <Sparkles className="w-6 h-6 animate-pulse" />
          <div className="w-12 h-px bg-gradient-to-r from-transparent via-cyan-400 to-transparent" />
        </div>
        <h2 className="text-5xl font-bold mb-4">How It Works</h2>
        <p className="text-gray-400 text-lg max-w-2xl mx-auto">
          Transform your code debugging experience in three simple steps
        </p>
      </div>

      {/* Enhanced steps */}
      <div className="space-y-6">
        {steps.map((step, index) => (
          <div
            key={index}
            className={`group relative transition-all duration-700 transform ${
              isVisible
                ? "translate-x-0 opacity-100"
                : "translate-x-20 opacity-0"
            }`}
            style={{ transitionDelay: `${index * 150}ms` }}
            onMouseEnter={() => setActiveStep(index)}
          >
            {/* Connection line */}
            {index < steps.length - 1 && (
              <div className="absolute left-8 top-16 w-px h-12 bg-gradient-to-b from-cyan-500/50 to-transparent z-0" />
            )}

            {/* Step number */}
            <div className="absolute -left-4 top-4 w-8 h-8 rounded-full bg-gradient-to-r from-gray-800 to-gray-700 border border-gray-600 flex items-center justify-center text-sm font-bold text-white z-10">
              {index + 1}
            </div>

            {/* Main card */}
            <div
              className={`relative ml-8 p-4 rounded-2xl border backdrop-blur-sm transition-all duration-500 cursor-pointer ${
                step.borderColor
              } ${step.hoverBorder} ${
                activeStep === index
                  ? `${step.bgColor} ${step.glowColor} scale-[1.02] border-opacity-100`
                  : "bg-white/5 border-white/10 hover:bg-white/10"
              }`}
            >
              {/* Animated gradient border */}
              <div
                className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${step.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500 -z-10`}
              />

              <div className="flex items-start gap-4">
                {/* Icon container */}
                <div
                  className={`relative flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${step.color} p-3 shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-3`}
                >
                  <div className="text-white">{step.icon}</div>

                  {/* Floating accent */}
                  <div
                    className={`absolute -top-2 -right-2 p-1 rounded-full bg-white/20 backdrop-blur-sm transition-all duration-300 ${
                      activeStep === index
                        ? "opacity-100 scale-100"
                        : "opacity-0 scale-75"
                    }`}
                  >
                    <div className="text-white">{step.accent}</div>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-bold text-white transition-colors duration-300 group-hover:text-cyan-300">
                      {step.title}
                    </h3>
                    <ChevronRight
                      className={`w-5 h-5 text-gray-400 transition-all duration-300 ${
                        activeStep === index
                          ? "translate-x-2 text-cyan-400"
                          : ""
                      }`}
                    />
                  </div>
                  <p className="text-gray-300 text-base leading-relaxed group-hover:text-gray-200 transition-colors duration-300">
                    {step.desc}
                  </p>
                </div>
              </div>

              {/* Progress indicator */}
              <div
                className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${
                  step.color
                } transition-all duration-500 rounded-b-2xl ${
                  activeStep === index ? "w-full opacity-100" : "w-0 opacity-0"
                }`}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Floating particles */}
      {[...Array(8)].map((_, i) => (
        <div
          key={i}
          className="absolute w-1 h-1 bg-cyan-400/60 rounded-full animate-pulse"
          style={{
            left: `${20 + i * 10}%`,
            top: `${30 + i * 8}%`,
            animationDelay: `${i * 0.5}s`,
            animationDuration: `${2 + i * 0.3}s`,
          }}
        />
      ))}
    </div>
  );
}
