"use client";

import {
  lowerBoundBinarySearch,
  standardBinarySearch,
  upperBoundBinarySearch,
} from "@/lib/algorithms/binarySearch";
import {
  BinarySearchAlgorithm,
  BinarySearchElement,
  BinarySearchGenerator,
} from "@/lib/types";
import { useCallback, useEffect, useRef, useState } from "react";

// Utility function to create a delay
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Map of algorithm names to their generator functions
const ALGORITHMS: Record<
  BinarySearchAlgorithm,
  (arr: BinarySearchElement[], target: number) => BinarySearchGenerator
> = {
  standard: standardBinarySearch,
  lowerBound: lowerBoundBinarySearch,
  upperBound: upperBoundBinarySearch,
};

export const useBinarySearch = () => {
  // State management
  const [array, setArray] = useState<BinarySearchElement[]>([]);
  const [target, setTarget] = useState<number>(0);
  const [isSearching, setIsSearching] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [selectedAlgorithm, setSelectedAlgorithm] =
    useState<BinarySearchAlgorithm>("standard");
  const [speed, setSpeed] = useState(1);
  const [inputValue, setInputValue] = useState("1,3,5,7,9,11,13,15,17,19");
  const [targetValue, setTargetValue] = useState("7");
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<{
    found: boolean;
    index: number;
    message: string;
  } | null>(null);

  // Refs for controlling the search process
  const isSearchingRef = useRef(false);
  const isPausedRef = useRef(false);
  const speedRef = useRef(speed);

  // Update refs when state changes
  useEffect(() => {
    isSearchingRef.current = isSearching;
  }, [isSearching]);

  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Parse input string to array of numbers
  const parseInput = useCallback((input: string): number[] => {
    try {
      const numbers = input
        .split(",")
        .map((str) => str.trim())
        .filter((str) => str !== "")
        .map((str) => {
          const num = parseInt(str, 10);
          if (isNaN(num)) throw new Error(`Invalid number: ${str}`);
          return num;
        });

      if (numbers.length === 0) throw new Error("No valid numbers found");
      if (numbers.length > 15) {
        throw new Error("Maximum 15 numbers allowed for binary search.");
      }

      // Check if array is sorted
      for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] < numbers[i - 1]) {
          throw new Error("Array must be sorted for binary search.");
        }
      }

      return numbers;
    } catch (error) {
      throw new Error(
        `Invalid input: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }, []);

  // Parse target value
  const parseTarget = useCallback((targetStr: string): number => {
    const target = parseInt(targetStr.trim(), 10);
    if (isNaN(target)) {
      throw new Error("Target must be a valid number");
    }
    return target;
  }, []);

  // Convert numbers array to BinarySearchElement array
  const createArrayElements = useCallback(
    (numbers: number[]): BinarySearchElement[] => {
      return numbers.map((value, index) => ({
        value,
        id: `element-${index}-${value}-${Date.now()}`,
        index,
        isLeft: false,
        isRight: false,
        isMid: false,
        isTarget: false,
        isComparing: false,
        isInRange: false,
        isFound: false,
        message: "",
      }));
    },
    []
  );

  const startVisualization = useCallback(async () => {
    if (isSearchingRef.current) return;

    try {
      setError(null); // Clear previous errors
      const numbers = parseInput(inputValue);
      const targetNum = parseTarget(targetValue);
      
      if (numbers.length === 0) {
        throw new Error("Please enter at least one number");
      }

      const initialElements = createArrayElements(numbers);
      setArray(initialElements);
      setTarget(targetNum);
      setIsComplete(false);
      setIsSearching(true);
      setResult(null);

      await sleep(250 / speedRef.current);

      // Run the selected algorithm
      const algorithmGenerator = ALGORITHMS[selectedAlgorithm](
        initialElements,
        targetNum
      );

      for (const step of algorithmGenerator) {
        if (!isSearchingRef.current) return;

        while (isPausedRef.current && isSearchingRef.current) {
          await sleep(100);
        }
        if (!isSearchingRef.current) return;

        setArray([...step]);
        await sleep(800 / speedRef.current);
      }

      if (isSearchingRef.current) {
        // Determine the result based on the algorithm
        let resultMessage = "";
        let foundIndex = -1;
        let found = false;

        if (selectedAlgorithm === "standard") {
          const foundElement = initialElements.find((el) => el.isFound);
          if (foundElement) {
            found = true;
            foundIndex = foundElement.index;
            resultMessage = `Target ${targetNum} found at index ${foundIndex}`;
          } else {
            resultMessage = `Target ${targetNum} not found in array`;
          }
        } else if (selectedAlgorithm === "lowerBound") {
          const boundElement = initialElements.find((el) => el.isFound);
          if (boundElement) {
            foundIndex = boundElement.index;
            resultMessage = `Lower bound for ${targetNum} is at index ${foundIndex}`;
          } else {
            foundIndex = numbers.length;
            resultMessage = `Lower bound for ${targetNum} is at index ${foundIndex} (end of array)`;
          }
          found = true;
        } else if (selectedAlgorithm === "upperBound") {
          const boundElement = initialElements.find((el) => el.isFound);
          if (boundElement) {
            foundIndex = boundElement.index;
            resultMessage = `Upper bound for ${targetNum} is at index ${foundIndex}`;
          } else {
            foundIndex = numbers.length;
            resultMessage = `Upper bound for ${targetNum} is at index ${foundIndex} (end of array)`;
          }
          found = true;
        }

        setResult({
          found,
          index: foundIndex,
          message: resultMessage,
        });

        setIsSearching(false);
        setIsComplete(true);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unexpected error occurred";
      setError(errorMessage);
      setIsSearching(false);
      setIsComplete(false);
    }
  }, [
    inputValue,
    targetValue,
    selectedAlgorithm,
    parseInput,
    parseTarget,
    createArrayElements,
  ]);

  const pauseVisualization = useCallback(() => {
    setIsPaused((prev) => !prev);
  }, []);

  const stopVisualization = useCallback(() => {
    setIsSearching(false);
    setIsPaused(false);
    setIsComplete(false);
    setResult(null);
  }, []);

  const resetVisualization = useCallback(() => {
    setIsSearching(false);
    setIsPaused(false);
    setIsComplete(false);
    setArray([]);
    setResult(null);
    setError(null);
  }, []);

  return {
    // State
    array,
    target,
    isSearching,
    isPaused,
    isComplete,
    selectedAlgorithm,
    speed,
    inputValue,
    targetValue,
    error,
    result,

    // Actions
    setInputValue,
    setTargetValue,
    setSelectedAlgorithm,
    setSpeed,
    startVisualization,
    pauseVisualization,
    stopVisualization,
    resetVisualization,
    setError,
  };
};
