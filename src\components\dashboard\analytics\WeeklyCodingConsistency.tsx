"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useAllSubmissionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import { useAuth } from "@/lib/auth-context";
import type { CodeforcesSubmission } from "@/lib/codeforces";
import { useQuery } from "@tanstack/react-query";
import { Activity, Award, Calendar, Target, TrendingUp } from "lucide-react";
import { useMemo } from "react";
import {
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

// Types for weekly data
interface WeeklyData {
  week: string;
  weekStart: Date;
  weekEnd: Date;
  totalSubmissions: number;
  acceptedSubmissions: number;
  acceptanceRate: number;
}

interface StreakData {
  currentStreak: number;
  longestStreak: number;
  activeWeeks: number;
  totalWeeks: number;
  drySpells: number;
}

interface PerformanceData {
  avgAcceptedPerWeek: number;
  mostProductiveWeek: {
    week: string;
    accepted: number;
  };
  activityRate: number;
}

interface UserProfile {
  handle: string;
  rating?: number;
  rank?: string;
}

const WeeklyCodingConsistency = () => {
  const { user, loading: authLoading } = useAuth();

  // Memoize user email to prevent unnecessary query key changes
  const userEmail = useMemo(() => user?.email, [user?.email]);

  // Fetch user profile data from our API
  const { data: userProfile, isLoading: profileLoading } =
    useQuery<UserProfile>({
      queryKey: ["userProfile", userEmail],
      queryFn: async () => {
        const response = await fetch("/api/userInfo");
        if (!response.ok) {
          throw new Error("Failed to fetch user profile");
        }
        return response.json();
      },
      enabled: !!userEmail && !authLoading,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
    });

  // Get Codeforces handle from user profile
  const codeforcesHandle = userProfile?.handle || "";

  // Fetch all submissions (including failed ones) for the last 6 months
  const { data: allSubmissionsData, isLoading: submissionsLoading } =
    useAllSubmissionsClient({
      handle: codeforcesHandle,
      enabled: !!codeforcesHandle.trim(),
    });

  // Process submissions into weekly data
  const { weeklyData, streakData, performanceData } = useMemo(() => {
    if (!allSubmissionsData?.result?.submissions) {
      return {
        weeklyData: [],
        streakData: {
          currentStreak: 0,
          longestStreak: 0,
          activeWeeks: 0,
          totalWeeks: 0,
          drySpells: 0,
        },
        performanceData: {
          avgAcceptedPerWeek: 0,
          mostProductiveWeek: { week: "", accepted: 0 },
          activityRate: 0,
        },
      };
    }

    const submissions = allSubmissionsData.result.submissions;

    // Get date 6 months ago
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Filter submissions from last 6 months
    const recentSubmissions = submissions.filter(
      (submission: CodeforcesSubmission) => {
        const submissionDate = new Date(submission.creationTimeSeconds * 1000);
        return submissionDate >= sixMonthsAgo;
      }
    );

    // Group submissions by week
    const weeklyMap = new Map<string, WeeklyData>();

    recentSubmissions.forEach((submission: CodeforcesSubmission) => {
      const submissionDate = new Date(submission.creationTimeSeconds * 1000);

      // Get the start of the week (Sunday)
      const weekStart = new Date(submissionDate);
      weekStart.setDate(submissionDate.getDate() - submissionDate.getDay());
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);

      const weekKey = weekStart.toISOString().split("T")[0];

      if (!weeklyMap.has(weekKey)) {
        weeklyMap.set(weekKey, {
          week: weekKey,
          weekStart,
          weekEnd,
          totalSubmissions: 0,
          acceptedSubmissions: 0,
          acceptanceRate: 0,
        });
      }

      const weekData = weeklyMap.get(weekKey)!;
      weekData.totalSubmissions++;

      if (submission.verdict === "OK") {
        weekData.acceptedSubmissions++;
      }
    });

    // Calculate acceptance rates and sort by date
    const processedWeeklyData = Array.from(weeklyMap.values())
      .map((week) => ({
        ...week,
        acceptanceRate:
          week.totalSubmissions > 0
            ? Math.round(
                (week.acceptedSubmissions / week.totalSubmissions) * 100
              )
            : 0,
      }))
      .sort((a, b) => a.weekStart.getTime() - b.weekStart.getTime());

    // Calculate streak data
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    let activeWeeks = 0;
    let drySpells = 0;

    // Check from most recent week backwards for current streak
    for (let i = processedWeeklyData.length - 1; i >= 0; i--) {
      if (processedWeeklyData[i].acceptedSubmissions > 0) {
        if (i === processedWeeklyData.length - 1) {
          currentStreak++;
        } else if (processedWeeklyData[i + 1].acceptedSubmissions > 0) {
          currentStreak++;
        } else {
          break;
        }
      } else {
        break;
      }
    }

    // Calculate longest streak and other metrics
    processedWeeklyData.forEach((week, index) => {
      if (week.acceptedSubmissions > 0) {
        tempStreak++;
        activeWeeks++;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        if (tempStreak > 0) {
          drySpells++;
        }
        tempStreak = 0;
      }
    });

    // Calculate performance data
    const totalAccepted = processedWeeklyData.reduce(
      (sum, week) => sum + week.acceptedSubmissions,
      0
    );
    const avgAcceptedPerWeek =
      processedWeeklyData.length > 0
        ? Math.round(totalAccepted / processedWeeklyData.length)
        : 0;

    const mostProductiveWeek = processedWeeklyData.reduce(
      (max, week) =>
        week.acceptedSubmissions > max.accepted
          ? { week: week.week, accepted: week.acceptedSubmissions }
          : max,
      { week: "", accepted: 0 }
    );

    const activityRate =
      processedWeeklyData.length > 0
        ? Math.round((activeWeeks / processedWeeklyData.length) * 100)
        : 0;

    return {
      weeklyData: processedWeeklyData,
      streakData: {
        currentStreak,
        longestStreak,
        activeWeeks,
        totalWeeks: processedWeeklyData.length,
        drySpells,
      },
      performanceData: {
        avgAcceptedPerWeek,
        mostProductiveWeek,
        activityRate,
      },
    };
  }, [allSubmissionsData]);

  // Loading state
  if (authLoading || profileLoading || submissionsLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <Activity className="w-6 h-6 text-green-400" />
            Weekly Coding Consistency
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-400">Loading consistency data...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No handle state
  if (!codeforcesHandle) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
            <Activity className="w-6 h-6 text-green-400" />
            Weekly Coding Consistency
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-400">
              Please verify your Codeforces handle to view consistency data
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Format week labels for display
  const formatWeekLabel = (weekStart: Date) => {
    const month = weekStart.toLocaleDateString("en-US", { month: "short" });
    const day = weekStart.getDate();
    return `${month} ${day}`;
  };

  // Function to render different consistency goal cards based on user's patterns
  const renderConsistencyGoalsCard = () => {
    const { currentStreak, longestStreak, activeWeeks, totalWeeks } =
      streakData;
    const { activityRate } = performanceData;

    // Determine user's consistency pattern
    const isHighPerformer = activityRate >= 80 && currentStreak >= 4;
    const hasActiveStreak = currentStreak >= 2;
    const hasBrokenStreak = longestStreak >= 3 && currentStreak === 0;
    const isNewBeginner = totalWeeks <= 4 && activeWeeks <= 2;
    const isInconsistent = activityRate < 50 && longestStreak <= 2;

    if (isHighPerformer) {
      return (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-yellow-400" />
            Elite Consistency
          </h4>
          <div className="space-y-3">
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-3">
              <div className="text-yellow-400 font-medium mb-1">
                🏆 Outstanding Performance!
              </div>
              <div className="text-sm text-gray-300">
                You're maintaining elite-level consistency with {activityRate}%
                activity rate.
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-yellow-400">
                  Maintain your excellence
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">
                  Reach {longestStreak + 2}-week streak
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">
                  Mentor others in consistency
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (hasActiveStreak) {
      return (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-green-400" />
            Active Streak
          </h4>
          <div className="space-y-3">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3">
              <div className="text-green-400 font-medium mb-1">
                🔥 Keep the momentum!
              </div>
              <div className="text-sm text-gray-300">
                {currentStreak >= 4
                  ? `Amazing ${currentStreak}-week streak! You're on fire!`
                  : `Great ${currentStreak}-week streak! ${
                      4 - currentStreak
                    } more weeks to reach a month.`}
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">
                  Extend to {currentStreak + 2} weeks
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">
                  Beat your {longestStreak}-week record
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Reach 8-week milestone</span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (hasBrokenStreak) {
      return (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-400" />
            Comeback Time
          </h4>
          <div className="space-y-3">
            <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-3">
              <div className="text-orange-400 font-medium mb-1">
                💪 Time for a comeback!
              </div>
              <div className="text-sm text-gray-300">
                You had a great {longestStreak}-week streak before. Let's
                rebuild it!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Start a new 2-week streak</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">
                  Reach {Math.floor(longestStreak / 2)} weeks consistently
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">
                  Surpass your {longestStreak}-week record
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (isNewBeginner) {
      return (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-400" />
            Getting Started
          </h4>
          <div className="space-y-3">
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3">
              <div className="text-blue-400 font-medium mb-1">
                🌱 Welcome to your coding journey!
              </div>
              <div className="text-sm text-gray-300">
                Every expert was once a beginner. Start building your
                consistency habit!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Solve 1 problem this week</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Build a 2-week streak</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">
                  Practice 3 weeks in a row
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    if (isInconsistent) {
      return (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-red-400" />
            Build Consistency
          </h4>
          <div className="space-y-3">
            <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
              <div className="text-red-400 font-medium mb-1">
                📈 Focus on consistency!
              </div>
              <div className="text-sm text-gray-300">
                Small, regular practice beats sporadic intense sessions. Let's
                build a habit!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Practice 2 weeks in a row</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Reach 50%+ activity rate</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Maintain 4-week streak</span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Default case - moderate consistency
    return (
      <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
          <Target className="w-5 h-5 text-purple-400" />
          Consistency Goals
        </h4>
        <div className="space-y-3">
          <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3">
            <div className="text-purple-400 font-medium mb-1">
              🎯 Steady progress!
            </div>
            <div className="text-sm text-gray-300">
              You're making steady progress. Let's push for more consistency!
            </div>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <span className="text-gray-300">• Target:</span>
              <span className="text-blue-400">Solve problems every week</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-300">• Goal:</span>
              <span className="text-green-400">
                Maintain {Math.max(activityRate + 10, 70)}%+ activity rate
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-gray-300">• Challenge:</span>
              <span className="text-purple-400">
                Beat your {longestStreak}-week record
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Prepare chart data with formatted labels
  const chartData = weeklyData.map((week) => ({
    ...week,
    weekLabel: formatWeekLabel(week.weekStart),
  }));

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
          <Activity className="w-6 h-6 text-green-400" />
          Consistency Index - Weekly practice tracking
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-green-400 mb-1">
              {streakData.currentStreak}
            </div>
            <div className="text-sm text-gray-300">Current Streak</div>
          </div>

          <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-blue-400 mb-1">
              {streakData.longestStreak}
            </div>
            <div className="text-sm text-gray-300">Longest Streak</div>
          </div>

          <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-purple-400 mb-1">
              {streakData.activeWeeks}/{streakData.totalWeeks}
            </div>
            <div className="text-sm text-gray-300">Active Weeks</div>
          </div>

          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-1">🔥</div>
            <div className="text-sm text-gray-300">Streak</div>
          </div>
        </div>

        {/* Weekly Coding Consistency Chart */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white text-center">
            Weekly Coding Consistency (Last 6 Months)
          </h3>

          {/* Legend */}
          <div className="flex justify-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-400"></div>
              <span className="text-sm text-gray-300">Accepted Solutions</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-400"></div>
              <span className="text-sm text-gray-300">Total Submissions</span>
            </div>
          </div>

          {/* Chart */}
          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
              >
                <XAxis
                  dataKey="weekLabel"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: "#9CA3AF" }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: "#9CA3AF" }}
                  label={{
                    value: "Number of Problems",
                    angle: -90,
                    position: "insideLeft",
                    style: { textAnchor: "middle", fill: "#9CA3AF" },
                  }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "rgba(15, 23, 42, 0.95)",
                    border: "1px solid rgba(148, 163, 184, 0.3)",
                    borderRadius: "8px",
                    color: "#fff",
                  }}
                  formatter={(value: number, name: string) => [
                    value,
                    name === "acceptedSubmissions"
                      ? "Accepted Solutions"
                      : "Total Submissions",
                  ]}
                  labelFormatter={(label) => `Week of ${label}`}
                />
                <Line
                  type="monotone"
                  dataKey="totalSubmissions"
                  stroke="#60A5FA"
                  strokeWidth={2}
                  dot={{ fill: "#60A5FA", strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: "#60A5FA", strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="acceptedSubmissions"
                  stroke="#34D399"
                  strokeWidth={2}
                  dot={{ fill: "#34D399", strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: "#34D399", strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Analysis Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Streak Analysis */}
          <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              Streak Analysis
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-300">Current streak:</span>
                <span className="text-green-400 font-semibold">
                  {streakData.currentStreak} weeks
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Longest streak:</span>
                <span className="text-blue-400 font-semibold">
                  {streakData.longestStreak} weeks
                </span>
              </div>
            </div>
          </div>

          {/* Dynamic Consistency Goals Card */}
          {renderConsistencyGoalsCard()}

          {/* Performance */}
          <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
            <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Award className="w-5 h-5 text-yellow-400" />
              Performance
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-300">Avg ACs per active week:</span>
                <span className="text-green-400 font-semibold">
                  {performanceData.avgAcceptedPerWeek}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Most productive week:</span>
                <span className="text-blue-400 font-semibold">
                  {performanceData.mostProductiveWeek.accepted > 0
                    ? `${performanceData.mostProductiveWeek.accepted} ACs`
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Activity rate:</span>
                <span className="text-purple-400 font-semibold">
                  {performanceData.activityRate}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Most Productive Week Highlight */}
        {performanceData.mostProductiveWeek.accepted > 0 && (
          <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-5 h-5 text-yellow-400" />
              <span className="text-lg font-semibold text-white">
                Most Productive Week:
              </span>
            </div>
            <div className="text-gray-300">
              Week of{" "}
              {formatWeekLabel(
                new Date(performanceData.mostProductiveWeek.week)
              )}{" "}
              - You solved{" "}
              <span className="text-yellow-400 font-bold">
                {performanceData.mostProductiveWeek.accepted}
              </span>{" "}
              problems! That's incredible productivity! 🚀
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default WeeklyCodingConsistency;
