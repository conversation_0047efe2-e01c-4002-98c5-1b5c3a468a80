import type { CodeforcesRatingChange } from "@/lib/codeforces";
import { fetchCodeforcesRating } from "@/lib/codeforces-client";
import { useQuery } from "@tanstack/react-query";

// ============================================================================
// CLIENT-SIDE CODEFORCES RATING REACT QUERY HOOK
// ============================================================================
// This hook fetches Codeforces rating history directly from the Codeforces API
// on the client side, avoiding backend API calls and distributing requests
// across different user IPs to prevent rate limiting.

// Extended rating change interface with computed fields
export interface RatingChangeWithExtras extends CodeforcesRatingChange {
  ratingDelta: number; // Rating change (+/- points)
  date: string; // Human-readable date in YYYY-MM-DD format
}

// Response structure matching the backend API format for compatibility
interface RatingResponse {
  status: string;
  result: {
    handle: string; // Username
    ratingHistory: RatingChangeWithExtras[]; // Complete rating history with extra fields
    currentRating: number; // Most recent rating
    maxRating: number; // Highest rating ever achieved
    minRating: number; // Lowest rating ever achieved
    totalContests: number; // Number of rated contests
    ratingRange: number; // Difference between max and min rating
  };
}

// Parameters for the rating hook
interface UseCodeforcesRatingClientParams {
  handle: string; // Codeforces username
  enabled?: boolean; // Whether to enable the query (default: true)
}

/**
 * React Query hook for fetching Codeforces rating history
 * Makes direct API calls to Codeforces from the client side
 * @param handle - Codeforces username
 * @param enabled - Whether to enable the query (default: true)
 * @returns React Query result with rating history data
 */
export const useCodeforcesRatingClient = ({
  handle,
  enabled = true,
}: UseCodeforcesRatingClientParams) => {
  return useQuery({
    // Unique query key for caching - includes handle to cache per user
    queryKey: ["codeforces-rating-client", handle],

    // Function that actually fetches the data from Codeforces API
    queryFn: async (): Promise<RatingResponse> => {
      // Validate input before making API call
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      try {
        // Call Codeforces user.rating API directly from client
        const response = await fetchCodeforcesRating(handle.trim());

        // Check if the API call failed
        if (response.status === "FAILED") {
          throw new Error(response.comment || "Failed to fetch rating history");
        }

        // Check if user has no rating history (never participated in rated contests)
        if (!response.result) {
          throw new Error("No rating history found");
        }

        // Sort rating history chronologically (oldest contest first)
        // This ensures the graph shows proper progression from left to right
        const sortedRatingHistory = response.result.sort(
          (a, b) => a.ratingUpdateTimeSeconds - b.ratingUpdateTimeSeconds
        );

        // Calculate key statistics for the rating graph
        // Current rating is the rating after the most recent contest
        const currentRating =
          sortedRatingHistory.length > 0
            ? sortedRatingHistory[sortedRatingHistory.length - 1].newRating
            : 0;

        // Find maximum and minimum ratings achieved
        const maxRating = Math.max(
          ...sortedRatingHistory.map((change) => change.newRating)
        );
        const minRating = Math.min(
          ...sortedRatingHistory.map((change) => change.newRating)
        );

        // Process rating changes to add computed fields
        const ratingChanges: RatingChangeWithExtras[] = sortedRatingHistory.map(
          (change) => ({
            ...change,
            // Calculate rating delta (change in rating)
            ratingDelta: change.newRating - change.oldRating,
            // Convert timestamp to human-readable date
            date: new Date(change.ratingUpdateTimeSeconds * 1000)
              .toISOString()
              .split("T")[0],
          })
        );

        // Return the processed rating data with statistics in backend-compatible format
        return {
          status: "OK",
          result: {
            handle: handle.trim(), // Username
            ratingHistory: ratingChanges, // Array of all rating changes with extra fields
            currentRating: currentRating, // Most recent rating
            maxRating: maxRating, // Highest rating ever achieved
            minRating: minRating, // Lowest rating ever achieved
            totalContests: sortedRatingHistory.length, // Number of rated contests participated
            ratingRange: maxRating - minRating, // Difference between max and min rating
          },
        };
      } catch (error: any) {
        console.error("Error fetching Codeforces rating history:", error);
        throw error;
      }
    },

    // Only run the query if enabled and handle is provided
    enabled: enabled && !!handle.trim(),

    // Cache configuration - DISABLED for sheetscope page
    staleTime: 5 * 60 * 1000, // Always consider data stale - no caching
    gcTime: 10 * 60 * 1000, // Don't keep in cache after component unmounts
    refetchOnMount: true, // Always refetch when component mounts
    refetchOnWindowFocus: false, // Refetch when window gains focus
    refetchOnReconnect: true, // Refetch when network reconnects

    // Smart retry logic
    retry: (failureCount, error: any) => {
      // Don't retry on user not found or no rating history errors
      if (error.message?.includes("No rating history found")) {
        return false;
      }

      // Don't retry on API errors that indicate permanent failures
      if (error.message?.includes("FAILED")) {
        return false;
      }

      // Retry up to 3 times for network issues
      return failureCount < 3;
    },

    // Exponential backoff for retries (1s, 2s, 4s, max 30s)
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
