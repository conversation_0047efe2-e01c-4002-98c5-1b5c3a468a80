"use client";

import { bubbleSort } from "@/lib/algorithms/bubbleSort";
import { insertionSort } from "@/lib/algorithms/insertionSort";
import { mergeSort } from "@/lib/algorithms/mergeSort";
import { selectionSort } from "@/lib/algorithms/selectionSort";
import {
  ArrayElement,
  MergeTreeState,
  SortingAlgorithm,
  SortingGenerator,
} from "@/lib/types";
import { useCallback, useEffect, useRef, useState } from "react";

// Utility function to create a delay
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Map of algorithm names to their generator functions
const ALGORITHMS: Record<
  SortingAlgorithm,
  (arr: ArrayElement[]) => SortingGenerator
> = {
  bubble: bubbleSort,
  selection: selectionSort,
  insertion: insertionSort,
  merge: (arr) => (function* () {})(), // Placeholder for regular array visualization
};

export const useSorting = () => {
  const [array, setArray] = useState<ArrayElement[]>([]);
  const [isSorting, setIsSorting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [selectedAlgorithm, setSelectedAlgorithm] =
    useState<SortingAlgorithm>("bubble");
  const [speed, setSpeed] = useState(1);
  const [inputValue, setInputValue] = useState("5, 1, 4, 2, 8");
  const [treeState, setTreeState] = useState<MergeTreeState | null>(null);
  const [error, setError] = useState<string | null>(null);

  const isSortingRef = useRef(isSorting);
  const speedRef = useRef(speed);
  const isPausedRef = useRef(isPaused);

  useEffect(() => {
    isSortingRef.current = isSorting;
  }, [isSorting]);

  useEffect(() => {
    isPausedRef.current = isPaused;
  }, [isPaused]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Parse input string to array of numbers
  const parseInput = useCallback(
    (input: string, algorithm: SortingAlgorithm): number[] => {
      try {
        const numbers = input
          .split(",")
          .map((str) => str.trim())
          .filter((str) => str !== "")
          .map((str) => {
            const num = parseInt(str, 10);
            if (isNaN(num)) throw new Error(`Invalid number: ${str}`);
            return num;
          });

        if (numbers.length === 0) throw new Error("No valid numbers found");

        if (algorithm === "merge") {
          if (numbers.length > 6) {
            throw new Error(
              "For Merge Sort, please enter a maximum of 6 numbers."
            );
          }
        } else {
          if (numbers.length > 10) {
            throw new Error("Maximum 10 numbers allowed for this algorithm.");
          }
        }

        return numbers;
      } catch (error) {
        throw new Error(
          `Invalid input: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      }
    },
    []
  );

  // Convert numbers array to ArrayElement array
  const createArrayElements = useCallback(
    (numbers: number[]): ArrayElement[] => {
      return numbers.map((value, index) => ({
        value,
        id: `element-${index}-${value}-${Date.now()}`,
        isComparing: false,
        isSwapping: false,
        isSorted: false,
        isMinimum: false,
        isCurrent: false,
        isPivot: false,
      }));
    },
    []
  );

  const startVisualization = useCallback(async () => {
    if (isSortingRef.current) return;

    try {
      setError(null); // Clear previous errors
      const numbers = parseInput(inputValue, selectedAlgorithm);
      if (numbers.length === 0) {
        throw new Error("Please enter at least one number");
      }

      const initialElements = createArrayElements(numbers);
      setArray(initialElements);
      setIsComplete(false);
      setIsSorting(true);
      setTreeState(null);

      await sleep(250 / speedRef.current);

      if (selectedAlgorithm === "merge") {
        // Handle merge sort with tree visualization
        const mergeSortGenerator = mergeSort(initialElements);

        for (const treeStep of mergeSortGenerator) {
          if (!isSortingRef.current) return;

          while (isPausedRef.current && isSortingRef.current) {
            await sleep(100);
          }
          if (!isSortingRef.current) return;

          setTreeState(treeStep);
          await sleep(800 / speedRef.current); // Slower for tree visualization
        }
      } else {
        // Handle other algorithms with array visualization
        const algorithmGenerator =
          ALGORITHMS[selectedAlgorithm](initialElements);

        for (const step of algorithmGenerator) {
          if (!isSortingRef.current) return;

          while (isPausedRef.current && isSortingRef.current) {
            await sleep(100);
          }
          if (!isSortingRef.current) return;

          setArray([...step]);
          await sleep(500 / speedRef.current);
        }
      }

      if (isSortingRef.current) {
        setIsSorting(false);
        setIsComplete(true);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unexpected error occurred";
      setError(errorMessage);
      setIsSorting(false);
      setIsComplete(false);
    }
  }, [inputValue, selectedAlgorithm, parseInput, createArrayElements]);

  const pauseVisualization = useCallback(() => {
    setIsPaused((prev) => !prev);
  }, []);

  const stopVisualization = useCallback(() => {
    setIsSorting(false);
    setIsPaused(false);
    setIsComplete(false);
    setArray([]);
    setTreeState(null);
    setError(null);
  }, []);

  const resetVisualization = useCallback(() => {
    stopVisualization();
    setInputValue("5, 1, 4, 2, 8");
  }, [stopVisualization]);

  // Effect to stop visualization if isSorting becomes false
  useEffect(() => {
    if (!isSorting) {
      isSortingRef.current = false;
    }
  }, [isSorting]);

  return {
    array,
    isSorting,
    isPaused,
    isComplete,
    selectedAlgorithm,
    speed,
    inputValue,
    treeState,
    error,
    setInputValue,
    setSelectedAlgorithm,
    setSpeed,
    startVisualization,
    pauseVisualization,
    stopVisualization,
    resetVisualization,
    setError,
  };
};
