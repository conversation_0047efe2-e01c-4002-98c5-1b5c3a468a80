"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useCodeforcesRatingClient } from "@/hooks/useCodeforcesRatingClient";
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Minus,
  TrendingDown,
  TrendingUp,
  Trophy,
} from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";

// Types for contest data
interface ContestData {
  contestId: number;
  contestName: string;
  handle: string;
  rank: number;
  ratingUpdateTimeSeconds: number;
  oldRating: number;
  newRating: number;
}

interface ContestCardProps {
  contest: ContestData;
  index: number;
}

// Individual contest card component
const ContestCard = ({ contest, index }: ContestCardProps) => {
  const ratingChange = contest.newRating - contest.oldRating;
  const isPositive = ratingChange > 0;
  const isNegative = ratingChange < 0;

  // Format date
  const contestDate = new Date(
    contest.ratingUpdateTimeSeconds * 1000
  ).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  // Get rating color based on new rating
  const getRatingColor = (rating: number): string => {
    if (rating >= 3000) return "#8B0000"; // Legendary Grandmaster
    if (rating >= 2600) return "#ff0000"; // International Grandmaster
    if (rating >= 2400) return "#ff0000"; // Grandmaster
    if (rating >= 2300) return "#ff8c00"; // International Master
    if (rating >= 2100) return "#ff8c00"; // Master
    if (rating >= 1900) return "#aa00aa"; // Candidate Master
    if (rating >= 1600) return "#0000ff"; // Expert
    if (rating >= 1400) return "#00aaaa"; // Specialist
    if (rating >= 1200) return "#008000"; // Pupil
    return "#808080"; // Newbie
  };

  return (
    <div
      className="flex-shrink-0 w-72 group cursor-pointer"
      style={{ animationDelay: `${index * 100}ms` }}
    >
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300 hover:-translate-y-1 h-full">
        <CardHeader className="pb-3">
          {/* Contest Type Badge */}
          <div className="flex items-center justify-between mb-2">
            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400 border border-blue-500/30">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-1.5"></span>
              CF
            </div>
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {contestDate}
            </div>
          </div>

          {/* Contest Name */}
          <CardTitle className="text-sm font-semibold text-white leading-tight line-clamp-2 min-h-[2.5rem]">
            {contest.contestName}
          </CardTitle>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="space-y-3">
            {/* Rank */}
            <div className="flex items-center justify-between p-2 bg-slate-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Trophy className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-300">Rank</span>
              </div>
              <span className="text-sm font-bold text-yellow-400">
                #{contest.rank.toLocaleString()}
              </span>
            </div>

            {/* Rating Change */}
            <div className="flex items-center justify-between p-2 bg-slate-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                {isPositive ? (
                  <TrendingUp className="w-4 h-4 text-green-400" />
                ) : isNegative ? (
                  <TrendingDown className="w-4 h-4 text-red-400" />
                ) : (
                  <Minus className="w-4 h-4 text-gray-400" />
                )}
                <span className="text-sm text-gray-300">Rating</span>
              </div>
              <div className="text-right">
                <div
                  className="text-sm font-bold"
                  style={{ color: getRatingColor(contest.newRating) }}
                >
                  {contest.newRating}
                </div>
                <div
                  className={`text-xs font-medium ${
                    isPositive
                      ? "text-green-400"
                      : isNegative
                      ? "text-red-400"
                      : "text-gray-400"
                  }`}
                >
                  {isPositive ? "+" : ""}
                  {ratingChange}
                </div>
              </div>
            </div>
          </div>

          {/* Hover Effect Overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none" />
        </CardContent>
      </Card>
    </div>
  );
};

// Main Recent Contests component
interface RecentContestsProps {
  handle: string;
}

const RecentContests = ({ handle }: RecentContestsProps) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Fetch rating data using existing hook
  const {
    data: ratingData,
    isLoading,
    error,
  } = useCodeforcesRatingClient({
    handle,
    enabled: !!handle.trim(),
  });

  // Process rating data to get recent contests
  const recentContests = useMemo(() => {
    if (!ratingData?.result?.ratingHistory) return [];

    // Get the 10 most recent contests
    return ratingData.result.ratingHistory
      .slice(-10)
      .reverse()
      .map((contest) => ({
        contestId: contest.contestId,
        contestName: contest.contestName,
        handle: contest.handle,
        rank: contest.rank,
        ratingUpdateTimeSeconds: contest.ratingUpdateTimeSeconds,
        oldRating: contest.oldRating,
        newRating: contest.newRating,
      }));
  }, [ratingData]);

  // Check scroll position and update indicators
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  // Update scroll indicators when contests change
  useEffect(() => {
    checkScrollPosition();
  }, [recentContests]);

  if (isLoading) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Recent Contests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !recentContests.length) {
    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            Recent Contests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-gray-400">No contest data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-white flex items-center gap-2">
          <Trophy className="w-5 h-5 text-yellow-400" />
          Recent Contests
          <span className="text-sm text-gray-400 font-normal ml-2">
            (recent {recentContests.length} contests)
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Horizontal scrolling container */}
        <div className="relative">
          {/* Left scroll button */}
          {canScrollLeft && (
            <button
              onClick={scrollLeft}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 bg-slate-800/90 hover:bg-slate-700/90 border border-slate-600/50 rounded-full p-2 transition-all duration-200 hover:scale-110 shadow-lg"
            >
              <ChevronLeft className="w-4 h-4 text-white" />
            </button>
          )}

          {/* Right scroll button */}
          {canScrollRight && (
            <button
              onClick={scrollRight}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 bg-slate-800/90 hover:bg-slate-700/90 border border-slate-600/50 rounded-full p-2 transition-all duration-200 hover:scale-110 shadow-lg"
            >
              <ChevronRight className="w-4 h-4 text-white" />
            </button>
          )}

          <div
            ref={scrollContainerRef}
            className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide snap-x snap-mandatory"
            onScroll={checkScrollPosition}
          >
            {recentContests.map((contest, index) => (
              <div key={contest.contestId} className="snap-start">
                <ContestCard contest={contest} index={index} />
              </div>
            ))}
          </div>

          {/* Gradient indicators */}
          {canScrollLeft && (
            <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-slate-900/60 to-transparent pointer-events-none" />
          )}
          {canScrollRight && (
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-slate-900/60 to-transparent pointer-events-none" />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentContests;
