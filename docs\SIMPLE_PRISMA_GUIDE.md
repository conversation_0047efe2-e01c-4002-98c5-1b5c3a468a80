# 🚀 Simple Prisma Client Setup - No Complexity!

## 📁 Basic File Structure

### 1. **Prisma Client Utility** (`src/lib/prisma.ts`)
```typescript
import { PrismaClient } from '../generated/prisma'

// Global instance to prevent multiple connections in development
declare global {
  var prisma: PrismaClient | undefined
}

// Create a single Prisma client instance
const prisma = globalThis.prisma || new PrismaClient()

if (process.env.NODE_ENV === 'development') {
  globalThis.prisma = prisma
}

export default prisma
```

**That's it!** This is all you need for a Prisma client.

### 2. **Simple API Route** (`src/app/api/database/route.ts`)
```typescript
import { NextRequest, NextResponse } from "next/server"
import prisma from "../../../lib/prisma"

// GET - Fetch users
export async function GET(request: NextRequest) {
  try {
    const users = await prisma.users.findMany()
    return NextResponse.json({ users })
  } catch (error) {
    return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
  }
}

// POST - Create user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const newUser = await prisma.users.create({
      data: {
        handle: body.handle,
        chat_id: body.chat_id,
        rating: body.rating,
        rank: body.rank
      }
    })
    
    return NextResponse.json({ user: newUser }, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: "Failed to create user" }, { status: 500 })
  }
}
```

## 🔧 Basic Prisma Operations

### **Create (INSERT)**
```typescript
const newUser = await prisma.users.create({
  data: {
    handle: "john_doe",
    chat_id: "chat123",
    rating: 1500
  }
})
```

### **Read (SELECT)**
```typescript
// Get all users
const users = await prisma.users.findMany()

// Get one user
const user = await prisma.users.findFirst({
  where: { handle: "john_doe" }
})

// Get user by unique field
const user = await prisma.users.findUnique({
  where: { id: 1 }
})
```

### **Update**
```typescript
const updatedUser = await prisma.users.update({
  where: { id: 1 },
  data: { rating: 1600 }
})
```

### **Delete**
```typescript
const deletedUser = await prisma.users.delete({
  where: { id: 1 }
})
```

## 🧪 Simple Test

### **Test with curl:**
```bash
# Create user
curl -X POST http://localhost:3000/api/database \
  -H "Content-Type: application/json" \
  -d '{"handle":"testuser","chat_id":"chat123","rating":1500}'

# Get all users
curl http://localhost:3000/api/database
```

### **Test with JavaScript:**
```javascript
// Create user
const response = await fetch('/api/database', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    handle: 'testuser',
    chat_id: 'chat123',
    rating: 1500
  })
})

const result = await response.json()
console.log(result)
```

## 🎯 Key Points

### **Why the global pattern?**
- Next.js development mode has hot reloading
- Without this pattern, you'd create multiple database connections
- This ensures one connection per development session

### **What's the minimum you need?**
1. Import PrismaClient
2. Create instance
3. Export it
4. Use it in your routes

### **Common patterns:**
```typescript
// Simple query
const users = await prisma.users.findMany()

// With conditions
const activeUsers = await prisma.users.findMany({
  where: { Inactive: false }
})

// With pagination
const users = await prisma.users.findMany({
  skip: 10,
  take: 5
})

// With sorting
const users = await prisma.users.findMany({
  orderBy: { rating: 'desc' }
})
```

## 🚀 That's It!

This is all you need for a basic Prisma setup:
- ✅ One utility file (`src/lib/prisma.ts`)
- ✅ Import and use in your routes
- ✅ Standard CRUD operations
- ✅ No complex serialization needed

**Simple, clean, and effective!** 🎉
