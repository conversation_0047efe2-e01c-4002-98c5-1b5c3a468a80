import { Prisma } from '../../generated/prisma';

/**
 * Enhanced type definitions for database operations
 * Provides better type safety and developer experience
 */

// Re-export commonly used Prisma types for convenience
export type {
  Users,
  Sheet,
  Prisma,
} from '../../generated/prisma';

/**
 * Database operation result types
 */
export type DatabaseResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: {
    code: string;
    message: string;
    userMessage: string;
    isRetryable: boolean;
  };
};

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
  cursor?: string;
}

/**
 * Pagination result
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
}

/**
 * Enhanced Users type with computed fields
 */
export interface UserWithSheets extends Prisma.UsersGetPayload<{
  include: { sheets: true };
}> {
  sheetCount: number;
  isActive: boolean;
}

/**
 * User creation input type
 */
export type CreateUserInput = Prisma.UsersCreateInput;

/**
 * User update input type
 */
export type UpdateUserInput = Prisma.UsersUpdateInput;

/**
 * User query filters
 */
export interface UserFilters {
  email?: string;
  handle?: string;
  isActive?: boolean;
  hasSheets?: boolean;
  ratingRange?: {
    min?: number;
    max?: number;
  };
  createdAfter?: Date;
  createdBefore?: Date;
}

/**
 * Sheet creation input type
 */
export type CreateSheetInput = Prisma.SheetCreateInput;

/**
 * Sheet update input type
 */
export type UpdateSheetInput = Prisma.SheetUpdateInput;

/**
 * Sheet query filters
 */
export interface SheetFilters {
  userId?: number;
  isPublic?: boolean;
  createdAfter?: Date;
  createdBefore?: Date;
  titleContains?: string;
}

/**
 * Database transaction type
 */
export type DatabaseTransaction = Prisma.TransactionClient;

/**
 * Query options for database operations
 */
export interface QueryOptions {
  include?: any;
  select?: any;
  orderBy?: any;
  where?: any;
  pagination?: PaginationParams;
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult {
  count: number;
  success: boolean;
  errors?: Array<{
    index: number;
    error: string;
  }>;
}

/**
 * Database metrics interface
 */
export interface DatabaseMetrics {
  totalUsers: number;
  totalSheets: number;
  activeUsers: number;
  averageRating: number;
  topRatedUsers: Array<{
    id: number;
    handle: string;
    rating: number;
  }>;
  recentActivity: {
    newUsersToday: number;
    newSheetsToday: number;
    lastActivityAt: Date;
  };
}

/**
 * Search parameters for full-text search
 */
export interface SearchParams {
  query: string;
  filters?: {
    type?: 'users' | 'sheets' | 'all';
    dateRange?: {
      start: Date;
      end: Date;
    };
  };
  pagination?: PaginationParams;
}

/**
 * Search result item
 */
export interface SearchResultItem {
  id: string;
  type: 'user' | 'sheet';
  title: string;
  description?: string;
  url: string;
  relevanceScore: number;
  metadata?: Record<string, any>;
}

/**
 * Audit log entry
 */
export interface AuditLogEntry {
  id: string;
  userId?: number;
  action: string;
  entityType: string;
  entityId: string;
  changes?: Record<string, any>;
  metadata?: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Database configuration interface
 */
export interface DatabaseConfig {
  url: string;
  maxConnections: number;
  connectionTimeout: number;
  poolTimeout: number;
  logLevel: 'query' | 'info' | 'warn' | 'error';
  enableMetrics: boolean;
  enableAuditLog: boolean;
}

/**
 * Repository pattern interfaces
 */
export interface BaseRepository<T, CreateInput, UpdateInput> {
  findById(id: number): Promise<T | null>;
  findMany(filters?: any, options?: QueryOptions): Promise<T[]>;
  create(data: CreateInput): Promise<T>;
  update(id: number, data: UpdateInput): Promise<T>;
  delete(id: number): Promise<boolean>;
  count(filters?: any): Promise<number>;
}

/**
 * User repository interface
 */
export interface UserRepository extends BaseRepository<UserWithSheets, CreateUserInput, UpdateUserInput> {
  findByEmail(email: string): Promise<UserWithSheets | null>;
  findByHandle(handle: string): Promise<UserWithSheets | null>;
  findActiveUsers(pagination?: PaginationParams): Promise<PaginatedResult<UserWithSheets>>;
  updateRating(id: number, rating: number): Promise<UserWithSheets>;
  getTopRatedUsers(limit?: number): Promise<UserWithSheets[]>;
}

/**
 * Sheet repository interface
 */
export interface SheetRepository extends BaseRepository<Prisma.SheetGetPayload<{ include: { user: true } }>, CreateSheetInput, UpdateSheetInput> {
  findByUserId(userId: number, pagination?: PaginationParams): Promise<PaginatedResult<Prisma.SheetGetPayload<{ include: { user: true } }>>>;
  findPublicSheets(pagination?: PaginationParams): Promise<PaginatedResult<Prisma.SheetGetPayload<{ include: { user: true } }>>>;
  searchSheets(query: string, pagination?: PaginationParams): Promise<PaginatedResult<Prisma.SheetGetPayload<{ include: { user: true } }>>>;
}

/**
 * Service layer result types
 */
export type ServiceResult<T> = Promise<DatabaseResult<T>>;

/**
 * Common database errors
 */
export const DATABASE_ERRORS = {
  NOT_FOUND: 'RECORD_NOT_FOUND',
  DUPLICATE: 'DUPLICATE_RECORD',
  VALIDATION: 'VALIDATION_ERROR',
  CONNECTION: 'CONNECTION_ERROR',
  TIMEOUT: 'OPERATION_TIMEOUT',
  CONSTRAINT: 'CONSTRAINT_VIOLATION',
  PERMISSION: 'PERMISSION_DENIED',
} as const;

/**
 * Type guard utilities
 */
export const isValidUser = (user: any): user is UserWithSheets => {
  return user && typeof user.id === 'number' && typeof user.email === 'string';
};

export const isValidSheet = (sheet: any): sheet is Prisma.SheetGetPayload<{}> => {
  return sheet && typeof sheet.id === 'number' && typeof sheet.userId === 'number';
};

/**
 * Utility type for making certain fields optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Utility type for making certain fields required
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;
