import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import { type NextRequest } from "next/server";

//this will get the sheetcount of the user
export async function GET(request: NextRequest) {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  try {
    // Optimized: Single query with aggregation instead of two separate queries
    const userWithSheetCount = await prisma.users.findFirst({
      where: {
        email: email,
      },
      select: {
        id: true,
        _count: {
          select: {
            sheets: true,
          },
        },
      },
    });

    if (!userWithSheetCount) {
      return new Response("User not found", { status: 404 });
    }

    const sheetCount = userWithSheetCount._count.sheets;

    return new Response(JSON.stringify({ sheetCount }), {
      headers: {
        "Content-Type": "application/json",
        // No aggressive caching for dynamic user data
        "Cache-Control": "private, no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("Database error: Failed to fetch sheet count", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

//this is the create a sheet api
export async function POST(request: NextRequest) {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  const body = await request.json();
  const sheetName = body.sheetName;
  const problems = body.problems;

  //   console.log(sheetName, email, problems);

  if (!sheetName || !problems || !email) {
    return new Response("Bad Request", { status: 400 });
  }

  // Add hasSentAndCompleted field to each problem with default value false
  const problemsWithStatus = problems.map((problem: any) => ({
    ...problem,
    hasSentAndCompleted: false,
  }));

  try {
    // Optimized: Single query with count aggregation instead of separate queries
    const userWithSheetInfo = await prisma.users.findFirst({
      where: {
        email: email,
      },
      select: {
        id: true,
        maxSheetSlots: true,
        _count: {
          select: {
            sheets: true,
          },
        },
      },
    });

    if (!userWithSheetInfo) {
      return new Response("User not found", { status: 404 });
    }

    const currentSheetCount = userWithSheetInfo._count.sheets;
    const maxSheetSlots = userWithSheetInfo.maxSheetSlots || 1;

    if (currentSheetCount >= maxSheetSlots) {
      return new Response("Sheet limit reached", { status: 400 });
    }

    const newSheet = await prisma.sheet.create({
      data: {
        name: sheetName,
        problems: problemsWithStatus,
        userId: userWithSheetInfo.id,
      },
    });

    return new Response(JSON.stringify({ newSheet }), {
      headers: {
        "Content-Type": "application/json",
        //No aggressive caching for dynamic operations
        "Cache-Control": "private, no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("Database error: Failed to create sheet", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
