import {
  GraphData,
  GraphLink,
  GraphNode,
} from "../components/visualizations/D3GraphVisualizer";

// Types for different edge list input formats
export type EdgeListInput = string | string[][] | number[][];

export interface ParseResult {
  success: boolean;
  data?: GraphData;
  error?: string;
  warnings?: string[];
}

/**
 * Color palette for automatic node coloring based on groups
 */
const NODE_COLORS = [
  "#69b3a2",
  "#ff6b6b",
  "#4ecdc4",
  "#45b7d1",
  "#96ceb4",
  "#feca57",
  "#ff9ff3",
  "#54a0ff",
  "#5f27cd",
  "#00d2d3",
  "#ff9f43",
  "#10ac84",
];

/**
 * Parses various edge list formats and converts them to GraphData
 *
 * Supported formats:
 * 1. String format: "0 1\n1 2\n2 3" or "A B\nB C\nC D"
 * 2. String array format: [["A", "B"], ["B", "C"]]
 * 3. Number array format: [[0, 1], [1, 2]]
 *
 * @param input - The edge list input in various formats
 * @returns ParseResult containing success status, data, and any errors/warnings
 */
export function parseEdgeList(input: EdgeListInput): ParseResult {
  const warnings: string[] = [];

  try {
    let edges: Array<[string, string]> = [];

    // Handle different input formats
    if (typeof input === "string") {
      edges = parseStringEdgeList(input);
    } else if (Array.isArray(input)) {
      if (input.length === 0) {
        return {
          success: false,
          error: "Empty edge list provided",
        };
      }

      // Check if it's string[][] or number[][]
      if (typeof input[0][0] === "string") {
        edges = input as Array<[string, string]>;
      } else if (typeof input[0][0] === "number") {
        edges = (input as number[][]).map(([source, target]) => [
          String(source),
          String(target),
        ]);
      } else {
        return {
          success: false,
          error: "Invalid edge format. Expected string or number pairs.",
        };
      }
    } else {
      return {
        success: false,
        error:
          "Invalid input format. Expected string, string[][], or number[][].",
      };
    }

    // Validate edges
    const validationResult = validateEdges(edges);
    if (!validationResult.success) {
      return validationResult;
    }

    if (validationResult.warnings) {
      warnings.push(...validationResult.warnings);
    }

    // Generate graph data
    const graphData = generateGraphData(edges);

    return {
      success: true,
      data: graphData,
      warnings: warnings.length > 0 ? warnings : undefined,
    };
  } catch (error) {
    return {
      success: false,
      error: `Parsing failed: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
    };
  }
}

/**
 * Parses string format edge lists
 * Handles various delimiters and formats
 */
function parseStringEdgeList(input: string): Array<[string, string]> {
  const lines = input.trim().split("\n");
  const edges: Array<[string, string]> = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue; // Skip empty lines

    // Split by whitespace, comma, or tab
    const parts = line.split(/[\s,\t]+/).filter((part) => part.length > 0);

    if (parts.length !== 2) {
      throw new Error(
        `Invalid edge format on line ${
          i + 1
        }: "${line}". Expected exactly 2 nodes per edge.`
      );
    }

    edges.push([parts[0], parts[1]]);
  }

  return edges;
}

/**
 * Validates edge list for common issues
 */
function validateEdges(edges: Array<[string, string]>): ParseResult {
  const warnings: string[] = [];

  if (edges.length === 0) {
    return {
      success: false,
      error: "No valid edges found in input",
    };
  }

  // Check for self-loops
  const selfLoops = edges.filter(([source, target]) => source === target);
  if (selfLoops.length > 0) {
    warnings.push(
      `Found ${selfLoops.length} self-loop(s). These will be included in the visualization.`
    );
  }

  // Check for duplicate edges
  const edgeSet = new Set<string>();
  const duplicates: Array<[string, string]> = [];

  edges.forEach(([source, target]) => {
    const edgeKey = `${source}-${target}`;
    const reverseKey = `${target}-${source}`;

    if (edgeSet.has(edgeKey) || edgeSet.has(reverseKey)) {
      duplicates.push([source, target]);
    } else {
      edgeSet.add(edgeKey);
    }
  });

  if (duplicates.length > 0) {
    warnings.push(
      `Found ${duplicates.length} duplicate edge(s). All will be included in the visualization.`
    );
  }

  // Check for very large graphs
  const uniqueNodes = new Set<string>();
  edges.forEach(([source, target]) => {
    uniqueNodes.add(source);
    uniqueNodes.add(target);
  });

  if (uniqueNodes.size > 100) {
    warnings.push(
      `Large graph detected (${uniqueNodes.size} nodes). Performance may be affected.`
    );
  }

  if (edges.length > 500) {
    warnings.push(
      `Many edges detected (${edges.length} edges). Consider reducing complexity for better performance.`
    );
  }

  return {
    success: true,
    warnings: warnings.length > 0 ? warnings : undefined,
  };
}

/**
 * Generates GraphData from validated edges
 * Creates nodes with automatic grouping and coloring
 */
function generateGraphData(edges: Array<[string, string]>): GraphData {
  const nodeMap = new Map<string, GraphNode>();
  const links: GraphLink[] = [];

  // Create nodes from edges
  edges.forEach(([source, target]) => {
    // Add source node
    if (!nodeMap.has(source)) {
      nodeMap.set(source, {
        id: source,
        label: source,
        group: 0, // Will be updated with community detection if needed
        radius: 20,
        color: NODE_COLORS[nodeMap.size % NODE_COLORS.length],
      });
    }

    // Add target node
    if (!nodeMap.has(target)) {
      nodeMap.set(target, {
        id: target,
        label: target,
        group: 0,
        radius: 20,
        color: NODE_COLORS[nodeMap.size % NODE_COLORS.length],
      });
    }
  });

  // Create links
  edges.forEach(([source, target], index) => {
    links.push({
      id: `link-${index}`,
      source: source,
      target: target,
      value: 1,
      color: "#999",
    });
  });

  // Calculate node degrees for sizing
  const nodeDegrees = new Map<string, number>();
  links.forEach((link) => {
    const sourceId =
      typeof link.source === "string" ? link.source : link.source.id;
    const targetId =
      typeof link.target === "string" ? link.target : link.target.id;

    nodeDegrees.set(sourceId, (nodeDegrees.get(sourceId) || 0) + 1);
    nodeDegrees.set(targetId, (nodeDegrees.get(targetId) || 0) + 1);
  });

  // Update node sizes based on degree
  const nodes = Array.from(nodeMap.values()).map((node) => ({
    ...node,
    radius: Math.max(
      15,
      Math.min(40, 15 + (nodeDegrees.get(node.id) || 0) * 3)
    ),
  }));

  return { nodes, links };
}

/**
 * Utility function to generate sample edge lists for testing
 */
export function generateSampleEdgeList(
  type: "simple" | "complex" | "tree" | "cycle"
): string {
  switch (type) {
    case "simple":
      return "A B\nB C\nC D\nD A";

    case "complex":
      return "0 1\n1 2\n2 3\n3 0\n1 3\n0 2\n4 1\n4 2\n5 3\n5 4";

    case "tree":
      return "root child1\nroot child2\nchild1 grandchild1\nchild1 grandchild2\nchild2 grandchild3";

    case "cycle":
      return "1 2\n2 3\n3 4\n4 5\n5 1";

    default:
      return "A B\nB C";
  }
}
