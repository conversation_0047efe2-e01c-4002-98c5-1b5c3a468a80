import type { CodeforcesRatingChange } from "@/lib/codeforces";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useEffect, useState } from "react";

// ============================================================================
// CODEFORCES RATING REACT QUERY HOOK
// ============================================================================
// This hook fetches and caches Codeforces rating history using React Query
// Provides automatic caching, error handling, and background updates

// Extended rating change interface with computed fields
interface RatingChangeWithExtras extends CodeforcesRatingChange {
  ratingDelta: number; // Rating change (+/- points)
  date: string; // Human-readable date in YYYY-MM-DD format
}

// Response structure from our rating API endpoint
interface RatingResponse {
  status: string;
  result: {
    handle: string; // Username
    ratingHistory: RatingChangeWithExtras[]; // Complete rating history with extra fields
    currentRating: number; // Most recent rating
    maxRating: number; // Highest rating ever achieved
    minRating: number; // Lowest rating ever achieved
    totalContests: number; // Number of rated contests
    ratingRange: number; // Difference between max and min rating
  };
}

// Parameters for the rating hook
interface UseCodeforcesRatingParams {
  handle: string; // Codeforces username
  enabled?: boolean; // Whether to enable the query (default: true)
}

// Main hook for fetching Codeforces rating history
export const useCodeforcesRating = ({
  handle,
  enabled = true,
}: UseCodeforcesRatingParams) => {
  return useQuery({
    // Unique query key for caching - includes handle to cache per user
    queryKey: ["codeforces-rating", handle],

    // Function that actually fetches the data
    queryFn: async (): Promise<RatingResponse> => {
      // Validate input before making API call
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      // Make HTTP request to our rating API endpoint
      const response = await axios.get<RatingResponse>(
        `/api/codeforces/rating?handle=${encodeURIComponent(handle)}`
      );

      // Check if our API returned an error
      if (response.data.status !== "OK") {
        throw new Error("Failed to fetch rating history");
      }

      return response.data;
    },
    // Only run the query if enabled and handle is provided
    enabled: enabled && !!handle.trim(),

    // Cache configuration - rating changes less frequently than submissions
    staleTime: 10 * 60 * 1000, // Consider data fresh for 10 minutes
    gcTime: 30 * 60 * 1000, // Keep in cache for 30 minutes after component unmounts

    // Smart retry logic
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx) - these are usually permanent
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      // Retry up to 3 times for server errors (5xx) or network issues
      return failureCount < 3;
    },

    // Exponential backoff for retries (1s, 2s, 4s, max 30s)
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// ============================================================================
// DEBOUNCE HOOK FOR SMOOTH SLIDER INTERACTIONS
// ============================================================================
// Custom hook that debounces a value to prevent excessive API calls
// Useful for search inputs, sliders, and other frequently changing values

export const useDebounce = <T>(value: T, delay: number): T => {
  // State to store the debounced value
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set up a timer to update the debounced value after the specified delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Clean up the timer if the value changes before the delay completes
    // This ensures only the latest value is used after the delay
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // Re-run effect when value or delay changes

  return debouncedValue;
};

// Export TypeScript interfaces for use in other components
export type { RatingChangeWithExtras, RatingResponse };
