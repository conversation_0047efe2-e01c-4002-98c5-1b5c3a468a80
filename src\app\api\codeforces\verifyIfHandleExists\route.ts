import prisma from "@/lib/prisma";
import { createClient } from "@/lib/supabase/server";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  //Checking if the user is logged in
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const email = user.email;

  if (!email) {
    return new Response("Bad Request", { status: 400 });
  }

  const searchParams = request.nextUrl.searchParams;
  const handle = searchParams.get("handle");
  if (!handle) {
    return new Response("Bad Request", { status: 400 });
  }

  // Check if handle exists in the database
  try {
    // Optimized: Use findUnique when possible for better performance
    const existingHandle = await prisma.users.findFirst({
      where: {
        handle: handle,
      },
      select: {
        id: true,
        handle: true,
        email: true,
        rating: true,
        rank: true,
      },
    });

    if (existingHandle) {
      // Handle exists - check if it has an email
      if (existingHandle.email) {
        // Handle exists with email - block completely
        return new Response(
          JSON.stringify({
            exists: true,
            hasEmail: true,
            email: existingHandle.email,
            isOwnHandle: existingHandle.email === email,
            message:
              existingHandle.email === email
                ? "This handle is already verified on your account."
                : "This handle is already taken. Each Codeforces handle can only be verified once.",
            canProceed: false,
          }),
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
              "Cache-Control":
                "public, max-age=600, s-maxage=600, stale-while-revalidate=300",
            },
          }
        );
      } else {
        // Handle exists but no email - allow verification (will update existing record)
        // Contest participation validation is handled on the frontend
        return new Response(
          JSON.stringify({
            exists: true,
            hasEmail: false,
            email: null,
            isOwnHandle: false,
            message:
              "Handle exists but not claimed. Available for verification.",
            canProceed: true,
          }),
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
              "Cache-Control":
                "public, max-age=600, s-maxage=600, stale-while-revalidate=300",
            },
          }
        );
      }
    } else {
      // Handle doesn't exist - allow verification (contest participation validation handled on frontend)
      return new Response(
        JSON.stringify({
          exists: false,
          hasEmail: false,
          email: null,
          isOwnHandle: false,
          message: "Handle is available for verification",
          canProceed: true,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Cache-Control":
              "public, max-age=600, s-maxage=600, stale-while-revalidate=300",
          },
        }
      );
    }
  } catch (error) {
    console.error("Database error: Failed to fetch handle info", error);
    return new Response(
      JSON.stringify({
        error: "Internal server error",
        exists: false,
        hasEmail: false,
        email: null,
        isOwnHandle: false,
        canProceed: false,
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
