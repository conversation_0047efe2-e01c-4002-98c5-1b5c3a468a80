"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAllSolvedQuestionsClient } from "@/hooks/useCodeforcesSubmissionsClient";
import {
  createContributionGrid,
  getAvailableYears,
  getMonthLabels,
  getRatingBasedColor,
  processSubmissionsToContributionsForYear,
} from "@/lib/contribution-utils";
import {
  Activity,
  Calendar,
  ChevronLeft,
  ChevronRight,
  Target,
  TrendingUp,
} from "lucide-react";
import React from "react";

interface ContributionGraphProps {
  handle: string;
}

const ContributionGraph: React.FC<ContributionGraphProps> = React.memo(
  ({ handle }) => {
    // State for year selection
    const [selectedYear, setSelectedYear] = React.useState<number>(
      new Date().getFullYear()
    );

    // Memoize the trimmed handle to ensure stable query key
    const stableHandle = React.useMemo(() => handle?.trim() || "", [handle]);

    // Fetch all solved submissions for the user with optimized configuration
    const {
      data: submissionsData,
      isLoading,
      error,
    } = useAllSolvedQuestionsClient({
      handle: stableHandle,
      enabled: !!stableHandle,
    });

    // Get available years from submissions
    const availableYears = React.useMemo(() => {
      if (!submissionsData?.result?.submissions) return [];
      return getAvailableYears(submissionsData.result.submissions);
    }, [submissionsData?.result?.submissions]);

    // Update selected year to the latest available year when data loads
    React.useEffect(() => {
      if (availableYears.length > 0) {
        const currentYear = new Date().getFullYear();
        // If current year is available and it's not January, use current year
        // Otherwise, use the previous year for more complete data
        const defaultYear =
          availableYears.includes(currentYear) && new Date().getMonth() > 0
            ? currentYear
            : availableYears.find((year) => year < currentYear) ||
              availableYears[0];

        if (!availableYears.includes(selectedYear)) {
          setSelectedYear(defaultYear);
        }
      }
    }, [availableYears, selectedYear]);

    // Process contribution data for the selected year
    const contributionData = React.useMemo(() => {
      if (!submissionsData?.result?.submissions) return null;
      return processSubmissionsToContributionsForYear(
        submissionsData.result.submissions,
        selectedYear
      );
    }, [submissionsData?.result?.submissions, selectedYear]);

    // Create GitHub-style grid layout
    const contributionGrid = React.useMemo(() => {
      if (!contributionData) return [];
      return createContributionGrid(contributionData.days);
    }, [contributionData]);

    // Get month labels for the grid
    const monthLabels = React.useMemo(() => {
      if (!contributionData) return [];
      return getMonthLabels(contributionData.days);
    }, [contributionData]);

    // Navigation functions - memoize to prevent unnecessary re-renders
    const goToPreviousYear = React.useCallback(() => {
      const currentIndex = availableYears.indexOf(selectedYear);
      if (currentIndex < availableYears.length - 1) {
        setSelectedYear(availableYears[currentIndex + 1]);
      }
    }, [availableYears, selectedYear]);

    const goToNextYear = React.useCallback(() => {
      const currentIndex = availableYears.indexOf(selectedYear);
      if (currentIndex > 0) {
        setSelectedYear(availableYears[currentIndex - 1]);
      }
    }, [availableYears, selectedYear]);

    if (isLoading) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Contribution Graph
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
              <span className="ml-3 text-gray-400">
                Loading contribution data...
              </span>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (error || !submissionsData?.result?.submissions) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Contribution Graph
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-400 py-8">
              <p>Unable to load contribution data</p>
              <p className="text-sm mt-2">Please check the Codeforces handle</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    if (!contributionData) {
      return (
        <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
          <CardHeader>
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Contribution Graph
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center text-gray-400 py-8">
              <p>No contribution data available for {selectedYear}</p>
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-white flex items-center gap-2">
              <Activity className="w-6 h-6 text-green-400" />
              Daily Top-Rated Problems Solved
            </CardTitle>

            {/* Year Navigation */}
            <div className="flex items-center gap-2 pt-10">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPreviousYear}
                disabled={
                  availableYears.indexOf(selectedYear) >=
                  availableYears.length - 1
                }
                className="h-8 w-8 p-0 bg-slate-800 border-slate-600 hover:bg-slate-700"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <span className="text-white font-medium min-w-[60px] text-center">
                {selectedYear}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={goToNextYear}
                disabled={availableYears.indexOf(selectedYear) <= 0}
                className="h-8 w-8 p-0 bg-slate-800 border-slate-600 hover:bg-slate-700"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-6 text-sm text-gray-300">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-blue-400" />
              <span>Total: {contributionData.totalSolved} problems</span>
            </div>
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-400" />
              <span>Current streak: {contributionData.currentStreak} days</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-yellow-400" />
              <span>Best streak: {contributionData.maxStreak} days</span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pb-6">
          <div className="relative min-w-0">
            {/* Month labels */}
            <div className="relative mb-2 ml-12">
              <div className="flex">
                {monthLabels.map((label, index) => (
                  <div
                    key={index}
                    className="text-xs text-gray-400 text-left"
                    style={{
                      width: `${label.width * 16}px`, // 16px = 12px cell + 4px gap
                      minWidth: "28px",
                    }}
                  >
                    {label.month}
                  </div>
                ))}
              </div>
            </div>

            {/* Day labels */}
            <div className="flex mb-2">
              <div className="flex flex-col text-xs text-gray-400 mr-2 justify-between h-[112px]">
                <span>Sun</span>
                <span>Mon</span>
                <span>Tue</span>
                <span>Wed</span>
                <span>Thu</span>
                <span>Fri</span>
                <span>Sat</span>
              </div>

              {/* Contribution grid */}
              <div className="overflow-x-auto flex-1 w-full">
                <div
                  className="flex gap-1 min-w-fit pb-2"
                  style={{ minWidth: "800px" }}
                >
                  {contributionGrid.map((week, weekIndex) => (
                    <div key={weekIndex} className="flex flex-col gap-1">
                      {week.map((day, dayIndex) => (
                        <div
                          key={day.date || `empty-${weekIndex}-${dayIndex}`}
                          className="w-3 h-3 rounded-sm border border-gray-700/30 transition-all hover:border-gray-500"
                          style={{
                            backgroundColor: day.date
                              ? getRatingBasedColor(day.maxRating, day.level)
                              : "rgb(22, 27, 34)",
                            boxShadow:
                              day.level > 0 && day.date
                                ? `0 0 4px ${getRatingBasedColor(
                                    day.maxRating,
                                    day.level
                                  )}60`
                                : "none",
                          }}
                          title={
                            day.date
                              ? `${day.date}: ${day.count} problem${
                                  day.count !== 1 ? "s" : ""
                                } solved${
                                  day.maxRating > 0
                                    ? ` (max rating: ${day.maxRating})`
                                    : ""
                                }`
                              : ""
                          }
                        />
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-400">
                Colors represent highest rated problem solved each day
              </div>
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <span>Less</span>
                <div className="flex gap-1">
                  {[0, 1, 2, 3, 4].map((level) => (
                    <div
                      key={level}
                      className="w-3 h-3 rounded-sm border border-gray-700/30"
                      style={{
                        backgroundColor: getRatingBasedColor(1200, level),
                      }}
                    />
                  ))}
                </div>
                <span>More</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// Add display name for debugging
ContributionGraph.displayName = "ContributionGraph";

export default ContributionGraph;
