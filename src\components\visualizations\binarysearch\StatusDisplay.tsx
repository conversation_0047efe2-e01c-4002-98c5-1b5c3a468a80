"use client";

import { BinarySearchAlgorithm } from "@/lib/types";

interface StatusDisplayProps {
  isSearching: boolean;
  isComplete: boolean;
  result: {
    found: boolean;
    index: number;
    message: string;
  } | null;
  target: number;
  selectedAlgorithm: BinarySearchAlgorithm;
}

const StatusDisplay = ({ 
  isSearching, 
  isComplete, 
  result, 
  target, 
  selectedAlgorithm 
}: StatusDisplayProps) => {
  const getAlgorithmIcon = () => {
    switch (selectedAlgorithm) {
      case "standard":
        return "🎯";
      case "lowerBound":
        return "⬇️";
      case "upperBound":
        return "⬆️";
      default:
        return "🔍";
    }
  };

  const getAlgorithmName = () => {
    switch (selectedAlgorithm) {
      case "standard":
        return "Standard Binary Search";
      case "lowerBound":
        return "Lower Bound Search";
      case "upperBound":
        return "Upper Bound Search";
      default:
        return "Binary Search";
    }
  };

  return (
    <>
      {/* Current Algorithm Display */}
      <div className="text-center">
        <div className="inline-flex items-center px-4 py-2 bg-blue-900/30 border border-blue-700/30 rounded-lg">
          <span className="text-lg mr-2">{getAlgorithmIcon()}</span>
          <span className="text-blue-300 font-medium">
            {getAlgorithmName()} for target: {target}
          </span>
        </div>
      </div>

      {/* Searching Status */}
      {isSearching && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-amber-900/50 border border-amber-700/50 rounded-lg">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-400 mr-2"></div>
            <span className="text-amber-300">Searching in progress...</span>
          </div>
        </div>
      )}

      {/* Completion Status */}
      {isComplete && result && (
        <div className="text-center">
          <div className={`inline-flex items-center px-4 py-2 rounded-lg ${
            result.found 
              ? "bg-green-900/50 border border-green-700/50" 
              : selectedAlgorithm === "standard" 
                ? "bg-red-900/50 border border-red-700/50"
                : "bg-green-900/50 border border-green-700/50"
          }`}>
            {result.found || selectedAlgorithm !== "standard" ? (
              <svg
                className="w-4 h-4 text-green-400 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            ) : (
              <svg
                className="w-4 h-4 text-red-400 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            <span className={
              result.found || selectedAlgorithm !== "standard"
                ? "text-green-300" 
                : "text-red-300"
            }>
              {result.message}
            </span>
          </div>
        </div>
      )}

      {/* Algorithm Explanation */}
      {!isSearching && !isComplete && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 bg-gray-900/30 border border-gray-700/30 rounded-lg">
            <span className="text-gray-400 text-sm">
              {selectedAlgorithm === "standard" && 
                "Click Start to search for the exact target value"}
              {selectedAlgorithm === "lowerBound" && 
                "Click Start to find the first position ≥ target"}
              {selectedAlgorithm === "upperBound" && 
                "Click Start to find the first position > target"}
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default StatusDisplay;
