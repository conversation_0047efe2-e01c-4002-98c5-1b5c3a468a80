@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Hide scrollbars globally while maintaining scroll functionality */
  html {
    overflow: auto; /* Allow scrolling */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  body {
    overflow: auto; /* Allow scrolling */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* Hide scrollbars for webkit browsers (Chrome, Safari, Edge) */
  html::-webkit-scrollbar,
  body::-webkit-scrollbar,
  *::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbars for Firefox on all elements */
  * {
    scrollbar-width: none;
  }

  /* Ensure content containers can still scroll without visible scrollbars */
  .scrollable-content {
    overflow: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .scrollable-content::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  /* Visible scrollbar for specific content areas like problems display */
  .scrollable-with-bar {
    overflow-y: auto;
    scrollbar-width: thin; /* Firefox */
    scrollbar-color: #4b5563 #1f2937; /* Firefox - thumb and track colors */
  }

  .scrollable-with-bar::-webkit-scrollbar {
    width: 8px; /* Chrome, Safari, Edge */
  }

  .scrollable-with-bar::-webkit-scrollbar-track {
    background: #1f2937; /* Dark track */
    border-radius: 4px;
  }

  .scrollable-with-bar::-webkit-scrollbar-thumb {
    background: #4b5563; /* Thumb color */
    border-radius: 4px;
    border: 1px solid #374151;
  }

  .scrollable-with-bar::-webkit-scrollbar-thumb:hover {
    background: #6b7280; /* Lighter on hover */
  }
}

@layer utilities {
  @keyframes breathing {
    0%,
    100% {
      transform: scale(1);
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    }
    50% {
      transform: scale(1.03);
    }
  }

  @keyframes animated-gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes fadeInUp {
    0% {
      opacity: 0;
      transform: translateY(30px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-breathing {
    animation: breathing 3s ease-in-out infinite;
  }

  .animate-gradient {
    background-size: 200% auto;
    animation: animated-gradient 3s ease-in-out infinite;
  }

  .animate-fade-in-up {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
  }

  /* Grid movement animation for visualizer pages */
  @keyframes gridMove {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(30px, 30px);
    }
  }

  .animate-grid-move {
    animation: gridMove 25s linear infinite;
  }

  /* Particle floating animation */
  @keyframes particleFloat {
    0%,
    100% {
      transform: translateY(0px) translateX(0px) scale(1);
      opacity: 0.3;
    }
    25% {
      transform: translateY(-20px) translateX(10px) scale(1.1);
      opacity: 0.6;
    }
    50% {
      transform: translateY(0px) translateX(-10px) scale(0.9);
      opacity: 0.8;
    }
    75% {
      transform: translateY(20px) translateX(5px) scale(1.05);
      opacity: 0.4;
    }
  }

  .animate-particle-float {
    animation: particleFloat 6s ease-in-out infinite;
  }

  /* Sorting pulse animation */
  @keyframes sortingPulse {
    0%,
    100% {
      transform: scaleY(1);
      opacity: 0.6;
    }
    50% {
      transform: scaleY(1.2);
      opacity: 0.9;
    }
  }

  .animate-sorting-pulse {
    animation: sortingPulse 3s ease-in-out infinite;
  }

  /* Fade in animation for tree visualizer */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 1s ease-out;
  }

  /* Hero background specific animations */
  @keyframes gridMoveHero {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(20px, 20px);
    }
  }

  .animate-grid-move-hero {
    animation: gridMoveHero 20s linear infinite;
  }

  @keyframes particleFloatHero {
    0% {
      transform: translateY(100vh) translateX(0px);
      opacity: 0;
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      transform: translateY(-100px) translateX(30px);
      opacity: 0;
    }
  }

  .animate-particle-float-hero {
    animation: particleFloatHero 15s linear infinite;
  }

  /* Gradient animation for special headings */
  @keyframes gradientX {
    0%,
    100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient-x {
    animation: gradientX 4s ease-in-out infinite;
  }

  .bg-300\% {
    background-size: 300% 300%;
  }

  /* Float animations for binary search visualization */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) translateX(0px);
      opacity: 0.4;
    }
    25% {
      transform: translateY(-15px) translateX(8px);
      opacity: 0.7;
    }
    50% {
      transform: translateY(0px) translateX(-8px);
      opacity: 0.9;
    }
    75% {
      transform: translateY(15px) translateX(4px);
      opacity: 0.6;
    }
  }

  @keyframes floatSlow {
    0%,
    100% {
      transform: translateY(0px) translateX(0px) rotate(0deg);
      opacity: 0.3;
    }
    33% {
      transform: translateY(-20px) translateX(10px) rotate(2deg);
      opacity: 0.6;
    }
    66% {
      transform: translateY(10px) translateX(-10px) rotate(-2deg);
      opacity: 0.8;
    }
  }

  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: floatSlow 12s ease-in-out infinite;
  }

  /* Utility class for hiding scrollbars */
  .scrollbar-hide {
    overflow: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  /* Smooth scroll snapping */
  .snap-x {
    scroll-snap-type: x mandatory;
  }

  .snap-start {
    scroll-snap-align: start;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Slow blinking animation for info button */
  @keyframes slowBlink {
    0%,
    50% {
      opacity: 1;
    }
    51%,
    100% {
      opacity: 0.3;
    }
  }

  .animate-slow-blink {
    animation: slowBlink 2s ease-in-out infinite;
  }
}
