"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Target } from "lucide-react";

const ConsistencyCardDemo = () => {
  // Demo scenarios
  const scenarios = [
    {
      title: "Elite Consistency",
      description: "High performer with 80%+ activity rate and 4+ week streak",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-yellow-400" />
            Elite Consistency
          </h4>
          <div className="space-y-3">
            <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-lg p-3">
              <div className="text-yellow-400 font-medium mb-1">
                🏆 Outstanding Performance!
              </div>
              <div className="text-sm text-gray-300">
                You're maintaining elite-level consistency with 85% activity rate.
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-yellow-400">Maintain your excellence</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Reach 12-week streak</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Mentor others in consistency</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Active Streak",
      description: "User with current 2+ week streak",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-green-400" />
            Active Streak
          </h4>
          <div className="space-y-3">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3">
              <div className="text-green-400 font-medium mb-1">
                🔥 Keep the momentum!
              </div>
              <div className="text-sm text-gray-300">
                Great 3-week streak! 1 more weeks to reach a month.
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Extend to 5 weeks</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Beat your 8-week record</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Reach 8-week milestone</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Comeback Time",
      description: "User had a good streak but it's broken",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-orange-400" />
            Comeback Time
          </h4>
          <div className="space-y-3">
            <div className="bg-orange-500/20 border border-orange-500/30 rounded-lg p-3">
              <div className="text-orange-400 font-medium mb-1">
                💪 Time for a comeback!
              </div>
              <div className="text-sm text-gray-300">
                You had a great 6-week streak before. Let's rebuild it!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Start a new 2-week streak</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Reach 3 weeks consistently</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Surpass your 6-week record</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Getting Started",
      description: "New beginner with minimal activity",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-400" />
            Getting Started
          </h4>
          <div className="space-y-3">
            <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-3">
              <div className="text-blue-400 font-medium mb-1">
                🌱 Welcome to your coding journey!
              </div>
              <div className="text-sm text-gray-300">
                Every expert was once a beginner. Start building your consistency habit!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Solve 1 problem this week</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Build a 2-week streak</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Practice 3 weeks in a row</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Build Consistency",
      description: "Inconsistent user with sporadic activity",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-red-400" />
            Build Consistency
          </h4>
          <div className="space-y-3">
            <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3">
              <div className="text-red-400 font-medium mb-1">
                📈 Focus on consistency!
              </div>
              <div className="text-sm text-gray-300">
                Small, regular practice beats sporadic intense sessions. Let's build a habit!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Practice 2 weeks in a row</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Reach 50%+ activity rate</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Maintain 4-week streak</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "Steady Progress",
      description: "Default case for moderate consistency",
      card: (
        <div className="bg-slate-800/40 border border-slate-600/30 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <Target className="w-5 h-5 text-purple-400" />
            Consistency Goals
          </h4>
          <div className="space-y-3">
            <div className="bg-purple-500/20 border border-purple-500/30 rounded-lg p-3">
              <div className="text-purple-400 font-medium mb-1">
                🎯 Steady progress!
              </div>
              <div className="text-sm text-gray-300">
                You're making steady progress. Let's push for more consistency!
              </div>
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Target:</span>
                <span className="text-blue-400">Solve problems every week</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Goal:</span>
                <span className="text-green-400">Maintain 75%+ activity rate</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-300">• Challenge:</span>
                <span className="text-purple-400">Beat your 5-week record</span>
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <Card className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 shadow-xl">
      <CardHeader>
        <CardTitle className="text-xl font-semibold text-white">
          Dynamic Consistency Cards Demo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-8">
        {scenarios.map((scenario, index) => (
          <div key={index} className="space-y-3">
            <div className="border-b border-slate-600/30 pb-2">
              <h3 className="text-lg font-semibold text-white">{scenario.title}</h3>
              <p className="text-sm text-gray-400">{scenario.description}</p>
            </div>
            {scenario.card}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ConsistencyCardDemo;
