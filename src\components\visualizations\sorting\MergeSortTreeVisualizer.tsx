"use client";

import { MergeTreeNode, MergeTreeState } from "@/lib/types";
import { AnimatePresence, motion } from "framer-motion";
import { useMemo } from "react";
import ReactFlow, {
  Background,
  BackgroundVariant,
  Controls,
  Edge,
  Handle,
  MarkerType,
  MiniMap,
  Node,
  Position,
} from "reactflow";
import "reactflow/dist/style.css";

interface MergeSortTreeVisualizerProps {
  treeState: MergeTreeState | null;
  isSorting: boolean;
}

interface CustomNodeData {
  treeNode: MergeTreeNode;
}

interface CustomNodeProps {
  data: CustomNodeData;
}

const CustomTreeNode = ({ data }: CustomNodeProps) => {
  const { treeNode: node } = data;
  const getStateColor = (state: string, isActive?: boolean) => {
    if (isActive) return "#14b8a6"; // teal-500 for active

    switch (state) {
      case "dividing":
        return "#f59e0b"; // amber-500
      case "divided":
        return "#6366f1"; // indigo-500
      case "merging":
        return "#8b5cf6"; // violet-500
      case "merged":
        return "#10b981"; // emerald-500
      case "complete":
        return "#10b981"; // emerald-500
      default:
        return "#3b82f6"; // blue-500
    }
  };

  const getStateLabel = (state: string) => {
    switch (state) {
      case "dividing":
        return "Dividing";
      case "divided":
        return "Divided";
      case "merging":
        return "Merging";
      case "merged":
        return "Merged";
      case "complete":
        return "Complete";
      default:
        return "";
    }
  };

  return (
    <div className="min-w-[120px] max-w-[280px] group cursor-pointer">
      {/* React Flow Handles for connections */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        className="w-2 h-2 !bg-blue-500 !border-2 !border-white opacity-0"
        style={{ top: -4 }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        className="w-2 h-2 !bg-blue-500 !border-2 !border-white opacity-0"
        style={{ bottom: -4 }}
      />

      {/* Main node container */}
      <div className="relative">
        {/* Array values */}
        <div
          className={`
          bg-gray-900/80 backdrop-blur-sm border rounded-lg p-2.5 shadow-lg transition-all duration-300
          ${
            node.isActive
              ? "border-teal-500/80 shadow-teal-500/20 ring-2 ring-teal-500/40"
              : node.isBeingCompared
              ? "border-amber-500/80 shadow-amber-500/30 ring-2 ring-amber-400/50 bg-amber-900/20"
              : node.state === "merging"
              ? "border-violet-500/70 shadow-violet-500/20"
              : "border-gray-700/60 hover:border-blue-500/40"
          }
        `}
        >
          <div className="flex flex-wrap justify-center items-start gap-1.5 mb-2">
            {node.array.map((element) => (
              <div key={element.id} className="relative pt-6">
                <div
                  className={`
                    px-2.5 py-1.5 rounded-md text-base font-semibold text-white shadow-md
                    ${
                      element.isComparing
                        ? "bg-gradient-to-br from-red-500 to-red-700 ring-2 ring-red-300 shadow-red-500/50"
                        : element.isSorted
                        ? "bg-gradient-to-br from-emerald-500 to-green-600"
                        : "bg-gradient-to-br from-blue-500 to-indigo-600"
                    }
                  `}
                >
                  {element.value}
                </div>
                <AnimatePresence>
                  {element.isComparing && (
                    <motion.div
                      className="absolute top-0 left-1/2 -translate-x-1/2 text-xs text-red-300 font-semibold bg-black/50 px-2 py-0.5 rounded-full"
                      initial={{ opacity: 0, y: 5, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -5, scale: 0.9 }}
                      transition={{ duration: 0.2, ease: "easeInOut" }}
                    >
                      Compare
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>

          <AnimatePresence>
            {node.comparisonText && (
              <motion.div
                className="text-center my-2 text-red-300 font-mono text-sm"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.2 }}
              >
                {node.comparisonText}
              </motion.div>
            )}
          </AnimatePresence>

          {/* State indicator */}
          <div className="text-center">
            <span
              className={`text-xs font-semibold uppercase tracking-wider ${
                node.isActive
                  ? "text-teal-300"
                  : node.isBeingCompared
                  ? "text-amber-300"
                  : node.state === "merging"
                  ? "text-violet-300"
                  : "text-gray-400"
              }`}
            >
              {node.isBeingCompared ? "COMPARING" : getStateLabel(node.state)}
            </span>
            {(node.state === "merging" || node.isBeingCompared) && (
              <div className="flex justify-center mt-1.5">
                <div className="flex space-x-1.5">
                  <div
                    className={`w-1.5 h-1.5 rounded-full animate-pulse ${
                      node.isBeingCompared ? "bg-amber-400" : "bg-violet-400"
                    }`}
                  ></div>
                  <div
                    className={`w-1.5 h-1.5 rounded-full animate-pulse ${
                      node.isBeingCompared ? "bg-amber-400" : "bg-violet-400"
                    }`}
                    style={{ animationDelay: "0.15s" }}
                  ></div>
                  <div
                    className={`w-1.5 h-1.5 rounded-full animate-pulse ${
                      node.isBeingCompared ? "bg-amber-400" : "bg-violet-400"
                    }`}
                    style={{ animationDelay: "0.3s" }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Active indicator glow */}
        {node.isActive && (
          <div className="absolute -inset-1.5 bg-teal-500/20 rounded-lg blur-md" />
        )}

        {/* Comparison indicator glow */}
        {node.isBeingCompared && !node.isActive && (
          <div className="absolute -inset-1.5 bg-amber-500/25 rounded-lg blur-md animate-pulse" />
        )}
      </div>
    </div>
  );
};

// Custom node types for React Flow - memoized outside component
const nodeTypes = {
  treeNode: CustomTreeNode,
};

const MergeSortTreeVisualizer = ({
  treeState,
  isSorting,
}: MergeSortTreeVisualizerProps) => {
  // Convert tree state to React Flow nodes and edges
  const { nodes, edges } = useMemo(() => {
    if (!treeState) {
      return { nodes: [], edges: [] };
    }

    const treeNodes = Array.from(treeState.nodes.values());
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];

    // Create React Flow nodes with better spacing
    treeNodes.forEach((treeNode) => {
      const x = treeNode.x || 50;
      const y = treeNode.y || treeNode.depth * 200 + 100;

      flowNodes.push({
        id: treeNode.id,
        type: "treeNode",
        position: {
          x: (x / 100) * 1000 - 150, // Increased width for better spacing
          y: y,
        },
        data: { treeNode },
        sourcePosition: Position.Bottom,
        targetPosition: Position.Top,
      });

      // Create edges for connections with better visibility
      if (treeNode.leftChildId) {
        const leftChild = treeState.nodes.get(treeNode.leftChildId);
        const isActiveConnection =
          treeNode.state === "merging" || treeNode.isActive;
        const isComparisonConnection = leftChild?.isBeingCompared;

        const edgeColor = isActiveConnection
          ? "#10b981"
          : isComparisonConnection
          ? "#f59e0b"
          : "#6366f1";

        flowEdges.push({
          id: `${treeNode.id}-${treeNode.leftChildId}`,
          source: treeNode.id,
          target: treeNode.leftChildId,
          sourceHandle: "bottom",
          targetHandle: "top",
          type: "straight",
          animated: isActiveConnection || isComparisonConnection,
          style: {
            stroke: edgeColor,
            strokeWidth: isActiveConnection || isComparisonConnection ? 3 : 2,
            strokeOpacity: 1,
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: edgeColor,
            width: 12,
            height: 12,
          },
          label: "L",
          labelStyle: {
            fill: edgeColor,
            fontWeight: 600,
            fontSize: 12,
          },
          labelBgStyle: {
            fill: "#1f2937",
            fillOpacity: 0.8,
          },
        });
      }

      if (treeNode.rightChildId) {
        const rightChild = treeState.nodes.get(treeNode.rightChildId);
        const isActiveConnection =
          treeNode.state === "merging" || treeNode.isActive;
        const isComparisonConnection = rightChild?.isBeingCompared;

        const edgeColor = isActiveConnection
          ? "#10b981"
          : isComparisonConnection
          ? "#f59e0b"
          : "#6366f1";

        flowEdges.push({
          id: `${treeNode.id}-${treeNode.rightChildId}`,
          source: treeNode.id,
          target: treeNode.rightChildId,
          sourceHandle: "bottom",
          targetHandle: "top",
          type: "straight",
          animated: isActiveConnection || isComparisonConnection,
          style: {
            stroke: edgeColor,
            strokeWidth: isActiveConnection || isComparisonConnection ? 3 : 2,
            strokeOpacity: 1,
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: edgeColor,
            width: 12,
            height: 12,
          },
          label: "R",
          labelStyle: {
            fill: edgeColor,
            fontWeight: 600,
            fontSize: 12,
          },
          labelBgStyle: {
            fill: "#1f2937",
            fillOpacity: 0.8,
          },
        });
      }
    });

    return { nodes: flowNodes, edges: flowEdges };
  }, [treeState]);

  if (!treeState) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-400">
        <p>No tree data available</p>
      </div>
    );
  }

  const containerHeight = Math.max(500, (treeState.maxDepth + 1) * 140 + 120);

  return (
    <div className="mt-8 w-full">
      <h3 className="text-xl font-semibold text-white mb-4 text-center">
        Merge Sort Tree Visualization
      </h3>

      {/* Tree container */}
      <div
        className="relative w-full bg-gradient-to-br from-blue-950/30 via-gray-900/20 to-blue-900/25 rounded-xl border border-gray-700/30 overflow-hidden shadow-2xl"
        style={{ height: `${containerHeight}px` }}
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{
            padding: 0.15,
            includeHiddenNodes: false,
            minZoom: 0.5,
            maxZoom: 1.2,
          }}
          proOptions={{ hideAttribution: true }}
          defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
          minZoom={0.4}
          maxZoom={1.5}
          nodesDraggable={false}
          nodesConnectable={false}
          elementsSelectable={false}
          zoomOnScroll={true}
          zoomOnPinch={true}
          panOnScroll={false}
          className="bg-transparent"
        >
          <Background
            color="#6366f1"
            gap={20}
            size={1}
            variant={BackgroundVariant.Dots}
            className="opacity-20"
          />
          <Controls
            className="bg-gray-800/80 border-gray-600/50"
            showZoom={true}
            showFitView={true}
            showInteractive={false}
          />
          <MiniMap
            className="bg-gray-800/80 border border-gray-600/50"
            nodeColor="#3b82f6"
            maskColor="rgba(0, 0, 0, 0.2)"
          />
        </ReactFlow>

        {/* Progress indicator */}
        {isSorting && (
          <div className="absolute top-4 right-4 bg-blue-900/80 backdrop-blur-sm rounded-lg px-3 py-2 z-10">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
              <span className="text-blue-300 text-sm">
                Step {treeState.currentStep}
              </span>
            </div>
          </div>
        )}

        {/* Completion indicator */}
        {treeState.isComplete && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute top-4 right-4 bg-emerald-900/80 backdrop-blur-sm rounded-lg px-3 py-2 z-10"
          >
            <div className="flex items-center gap-2">
              <svg
                className="w-4 h-4 text-emerald-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-emerald-300 text-sm">Complete</span>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MergeSortTreeVisualizer;
