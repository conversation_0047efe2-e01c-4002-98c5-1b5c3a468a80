import { ArrayElement } from "../types";

export function* insertionSort(
  array: ArrayElement[]
): Generator<ArrayElement[], void, void> {
  const arr = [...array];
  const n = arr.length;

  yield [...arr];

  for (let i = 1; i < n; i++) {
    let j = i;

    // Highlight the key element we are positioning as the pivot
    arr[j].isPivot = true;
    yield [...arr];

    // Swap the element to the left until it's in the correct sorted position.
    while (j > 0 && arr[j - 1].value > arr[j].value) {
      // Highlight elements being compared
      arr[j - 1].isComparing = true;
      yield [...arr];

      arr[j - 1].isComparing = false;

      // Highlight for swapping
      arr[j].isSwapping = true;
      arr[j - 1].isSwapping = true;
      yield [...arr];

      // The swap
      [arr[j], arr[j - 1]] = [arr[j - 1], arr[j]];

      // Update element states post-swap
      arr[j].isSwapping = false;
      arr[j - 1].isSwapping = false;
      arr[j].isPivot = false; // Old position is no longer pivot
      arr[j - 1].isPivot = true; // New position is pivot

      yield [...arr];

      j--;
    }

    // Element is now in its sorted position. Remove the pivot highlight.
    arr[j].isPivot = false;
    yield [...arr];
  }

  // Final cleanup, everything is sorted
  for (let k = 0; k < n; k++) {
    arr[k].isSorted = true;
    arr[k].isCurrent = false;
    arr[k].isComparing = false;
    arr[k].isSwapping = false;
    arr[k].isPivot = false;
  }
  yield [...arr];
}
