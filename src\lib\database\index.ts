/**
 * Database utilities index
 * Centralized exports for all database-related utilities
 */

// Core Prisma client
export { default as prisma } from "../prisma";

// Error handling utilities
export {
  getUserFriendlyMessage,
  handlePrismaError,
  isRetryableError,
  PRISMA_ERROR_CODES,
  safeDbOperation,
  withRetry,
  type DatabaseError,
} from "./errors";

// Health check utilities
export {
  checkDatabaseConnection,
  getConnectionPoolStatus,
  maintenanceUtils,
  performHealthCheck,
  runDatabaseDiagnostics,
  shutdownDatabase,
  validateSchemaIntegrity,
  warmupDatabase,
  type ConnectionPoolStatus,
  type DatabaseHealthCheck,
} from "./health";

// Configuration utilities
export {
  DATABASE_CONFIGS,
  generateOptimizedDatabaseUrl,
  getCurrentEnvironmentConfig,
  runConfigurationDiagnostics,
  supabaseHelpers,
  validateDatabaseUrl,
  validateEnvironmentVariables,
  type DatabaseEnvironmentConfig,
} from "./config";

// Import functions for internal use in dbUtils
import { runConfigurationDiagnostics } from "./config";
import { handlePrismaError, safeDbOperation, withRetry } from "./errors";
import {
  checkDatabaseConnection,
  getConnectionPoolStatus,
  performHealthCheck,
  runDatabaseDiagnostics,
  shutdownDatabase,
  validateSchemaIntegrity,
  warmupDatabase,
} from "./health";

// Type definitions
export {
  DATABASE_ERRORS,
  isValidSheet,
  isValidUser,
  type AuditLogEntry,
  type BaseRepository,
  type BulkOperationResult,
  type CreateSheetInput,
  type CreateUserInput,
  type DatabaseConfig,
  type DatabaseMetrics,
  type DatabaseResult,
  type DatabaseTransaction,
  type PaginatedResult,
  type PaginationParams,
  type PartialBy,
  type Prisma,
  type QueryOptions,
  type RequiredBy,
  type SearchParams,
  type SearchResultItem,
  type ServiceResult,
  type Sheet,
  type SheetFilters,
  type SheetRepository,
  type UpdateSheetInput,
  type UpdateUserInput,
  type UserFilters,
  type UserRepository,
  // Re-export Prisma types
  type Users,
  type UserWithSheets,
} from "./types";

/**
 * Database utility functions for common operations
 */
export const dbUtils = {
  /**
   * Initialize database connection and perform health check
   */
  initialize: async () => {
    console.log("🚀 Initializing database...");

    // Validate configuration
    const configDiagnostics = runConfigurationDiagnostics();
    if (!configDiagnostics.validation.isValid) {
      console.error(
        "❌ Database configuration validation failed:",
        configDiagnostics.validation.errors
      );
      throw new Error("Invalid database configuration");
    }

    // Warm up connection
    const warmupSuccess = await warmupDatabase();
    if (!warmupSuccess) {
      throw new Error("Database warmup failed");
    }

    // Perform health check
    const healthCheck = await performHealthCheck();
    if (healthCheck.status === "unhealthy") {
      throw new Error(`Database health check failed: ${healthCheck.error}`);
    }

    console.log("✅ Database initialized successfully");
    return {
      status: healthCheck.status,
      responseTime: healthCheck.responseTime,
      config: configDiagnostics.currentConfig,
    };
  },

  /**
   * Gracefully shutdown database connections
   */
  shutdown: async () => {
    await shutdownDatabase();
  },

  /**
   * Get database status and metrics
   */
  getStatus: async () => {
    const healthCheck = await performHealthCheck();
    const poolStatus = await getConnectionPoolStatus();
    const schemaIntegrity = await validateSchemaIntegrity();

    return {
      health: healthCheck,
      connectionPool: poolStatus,
      schemaIntegrity,
      timestamp: new Date(),
    };
  },

  /**
   * Run comprehensive database diagnostics
   */
  diagnose: async () => {
    const configDiagnostics = runConfigurationDiagnostics();
    const dbDiagnostics = await runDatabaseDiagnostics();

    return {
      configuration: configDiagnostics,
      database: dbDiagnostics,
      summary: {
        configurationValid: configDiagnostics.validation.isValid,
        databaseHealthy: dbDiagnostics.health.status === "healthy",
        schemaIntegrity: dbDiagnostics.schemaIntegrity,
        overallStatus:
          configDiagnostics.validation.isValid &&
          dbDiagnostics.health.status === "healthy" &&
          dbDiagnostics.schemaIntegrity
            ? "healthy"
            : "issues_detected",
      },
    };
  },
};

/**
 * Database middleware for Next.js API routes
 */
export const withDatabase = (handler: any) => {
  return async (req: any, res: any) => {
    try {
      // Ensure database is warmed up
      await warmupDatabase();

      // Execute the handler
      return await handler(req, res);
    } catch (error) {
      console.error("Database middleware error:", error);

      // Handle database-specific errors
      const dbError = handlePrismaError(error);

      return res.status(500).json({
        error: "Database operation failed",
        message: dbError.userMessage,
        code: dbError.code,
        retryable: dbError.isRetryable,
      });
    }
  };
};

/**
 * Database hooks for React components (if needed)
 */
export const dbHooks = {
  /**
   * Hook for database health monitoring
   */
  useHealthCheck: () => {
    // This would be implemented as a React hook if needed
    // For now, it's a placeholder
    return {
      isHealthy: true,
      isLoading: false,
      error: null,
      refetch: async () => await performHealthCheck(),
    };
  },
};

/**
 * Export commonly used Prisma utilities
 */
export const prismaUtils = {
  /**
   * Create a safe database operation wrapper
   */
  safe: safeDbOperation,

  /**
   * Retry wrapper for operations
   */
  retry: withRetry,

  /**
   * Error handler
   */
  handleError: handlePrismaError,

  /**
   * Connection check
   */
  checkConnection: checkDatabaseConnection,
};
