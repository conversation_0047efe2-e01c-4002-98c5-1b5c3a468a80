"use client";

import { SortingAlgorithm } from "@/lib/types";

interface ControlsProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  selectedAlgorithm: SortingAlgorithm;
  onAlgorithmChange: (algorithm: SortingAlgorithm) => void;
  speed: number;
  onSpeedChange: (speed: number) => void;
  onStart: () => void;
  onPause: () => void;
  onStop: () => void;
  onReset: () => void;
  isSorting: boolean;
  isPaused: boolean;
  error: string | null;
}

const Controls = ({
  inputValue,
  onInputChange,
  selectedAlgorithm,
  onAlgorithmChange,
  speed,
  onSpeedChange,
  onStart,
  onPause,
  onStop,
  onReset,
  isSorting,
  isPaused,
  error,
}: ControlsProps) => {
  return (
    <div className="flex flex-col lg:flex-row gap-4 items-center justify-center">
      <div className="flex-1 max-w-md w-full">
        <label
          htmlFor="numbers-input"
          className="block text-sm font-medium text-gray-300 mb-2"
        >
          Enter Numbers (comma-separated)
        </label>
        <input
          id="numbers-input"
          type="text"
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          disabled={isSorting}
          placeholder="e.g., 5, 1, 4, 2, 8"
          className={`w-full px-4 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${
            error
              ? "border-red-500 ring-red-500"
              : "border-gray-600 focus:ring-blue-500"
          }`}
        />
        {error && (
          <p className="mt-2 text-sm text-red-400 font-medium">{error}</p>
        )}
      </div>

      <div className="w-full max-w-md lg:w-auto lg:flex-shrink-0">
        <label
          htmlFor="algorithm-select"
          className="block text-sm font-medium text-gray-300 mb-2"
        >
          Algorithm
        </label>
        <select
          id="algorithm-select"
          value={selectedAlgorithm}
          onChange={(e) =>
            onAlgorithmChange(e.target.value as SortingAlgorithm)
          }
          disabled={isSorting}
          className="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <option value="bubble">Bubble Sort</option>
          <option value="selection">Selection Sort</option>
          <option value="insertion">Insertion Sort</option>
          <option value="merge">Merge Sort (Tree View)</option>
        </select>
      </div>

      <div className="w-full max-w-md lg:w-auto lg:flex-shrink-0">
        <label
          htmlFor="speed-control"
          className="block text-sm font-medium text-gray-300 mb-2"
        >
          Speed
        </label>
        <select
          id="speed-control"
          value={speed}
          onChange={(e) => onSpeedChange(Number(e.target.value))}
          className="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value={0.5}>0.5x (Slow)</option>
          <option value={1}>1x (Normal)</option>
          <option value={2}>2x (Fast)</option>
          <option value={4}>4x (Very Fast)</option>
        </select>
      </div>

      <div className="w-full max-w-md lg:w-auto lg:flex-shrink-0">
        <label className="block text-sm font-medium text-transparent mb-2">
          Actions
        </label>
        <div className="flex flex-wrap gap-2 justify-center">
          <button
            onClick={onStart}
            disabled={isSorting}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900"
          >
            {isSorting && !isPaused ? (
              <span className="flex items-center">
                Sorting
                <span className="ml-1">
                  <span
                    className="inline-block animate-bounce"
                    style={{ animationDelay: "0ms" }}
                  >
                    .
                  </span>
                  <span
                    className="inline-block animate-bounce"
                    style={{ animationDelay: "150ms" }}
                  >
                    .
                  </span>
                  <span
                    className="inline-block animate-bounce"
                    style={{ animationDelay: "300ms" }}
                  >
                    .
                  </span>
                </span>
              </span>
            ) : (
              "Visualize"
            )}
          </button>

          <button
            onClick={onPause}
            disabled={!isSorting}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:ring-offset-gray-900"
          >
            {isPaused ? "Resume" : "Pause"}
          </button>

          <button
            onClick={onStop}
            disabled={!isSorting}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900"
          >
            Stop
          </button>

          <button
            onClick={onReset}
            disabled={isSorting}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-gray-900"
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );
};

export default Controls;
