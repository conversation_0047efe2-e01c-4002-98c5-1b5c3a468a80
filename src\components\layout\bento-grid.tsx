import {
  Activity,
  BarChart3,
  FileText,
  Network,
  Sparkles,
  TrendingUp,
} from "lucide-react";

import { BentoCard, BentoGrid } from "@/components/magicui/bento-grid";

const features = [
  {
    Icon: TrendingUp,
    name: "Codeforces Profile Tracking",
    description:
      "Track your Codeforces submissions, rating history, and contest performance with real-time sync.",
    href: "/dashboard",
    cta: "View Dashboard",
    background: (
      <div className="absolute inset-0 overflow-hidden">
        {/* Background gradient layers */}
        <div className="absolute -right-20 -top-20 w-72 h-72 bg-gradient-to-br from-blue-500/20 via-cyan-500/15 to-teal-500/20 blur-3xl rounded-full animate-pulse" />
        <div
          className="absolute -left-16 -bottom-16 w-56 h-56 bg-gradient-to-tr from-green-500/15 to-blue-500/15 blur-3xl rounded-full animate-pulse"
          style={{ animationDelay: "1s" }}
        />

        {/* Corner decorative elements */}
        <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400/60 rounded-full animate-ping" />
        <div
          className="absolute top-8 right-8 w-1.5 h-1.5 bg-cyan-400/60 rounded-full animate-ping"
          style={{ animationDelay: "0.5s" }}
        />
        <div className="absolute bottom-4 left-4 w-2 h-2 bg-teal-400/60 rounded-full animate-pulse" />

        {/* Edge animated lines */}
        <div className="absolute bottom-0 right-0 w-20 h-0.5 bg-gradient-to-l from-blue-400/40 to-transparent animate-pulse" />
        <div
          className="absolute top-0 left-0 w-16 h-0.5 bg-gradient-to-r from-cyan-400/40 to-transparent animate-pulse"
          style={{ animationDelay: "0.3s" }}
        />

        {/* Content overlay protection */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/5" />
      </div>
    ),
    className:
      "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3 group hover:scale-[1.01] transition-all duration-300 relative",
  },
  {
    Icon: BarChart3,
    name: "Interactive Rating Graphs",
    description:
      "Visualize your rating progression with zoom, date filtering, and interactive start/end point selection.",
    href: "/dashboard",
    cta: "View Analytics",
    background: (
      <div className="absolute inset-0 overflow-hidden">
        {/* Rotating background layers */}
        <div
          className="absolute -right-16 -top-16 w-64 h-64 bg-gradient-to-br from-blue-600/25 via-green-500/20 to-emerald-400/25 blur-3xl rounded-full animate-spin"
          style={{ animationDuration: "20s" }}
        />
        <div
          className="absolute -left-12 -bottom-12 w-48 h-48 bg-gradient-to-tr from-cyan-500/20 to-blue-500/20 blur-3xl rounded-full animate-spin"
          style={{ animationDuration: "15s", animationDirection: "reverse" }}
        />

        {/* Corner performance indicators */}
        <div className="absolute top-6 right-6 flex space-x-1">
          <div className="w-1 h-3 bg-green-400/60 rounded-full animate-bounce" />
          <div
            className="w-1 h-4 bg-blue-400/60 rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}
          />
          <div
            className="w-1 h-5 bg-cyan-400/60 rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}
          />
        </div>

        {/* Edge complexity notations */}
        <div className="absolute top-4 left-4 text-xs text-green-400/70 font-mono animate-pulse">
          O(1)
        </div>
        <div
          className="absolute bottom-4 right-4 text-xs text-blue-400/70 font-mono animate-pulse"
          style={{ animationDelay: "0.5s" }}
        >
          O(n)
        </div>

        {/* Clock hands animation in corner */}
        <div className="absolute top-8 right-12 w-4 h-4 ">
          <div className="absolute top-2 left-2 w-0.5 h-2 bg-green-400/60 origin-bottom animate-pulse transform rotate-45" />
          <div
            className="absolute top-2 left-2 w-0.5 h-1.5 bg-blue-400/60 origin-bottom animate-pulse transform rotate-90"
            style={{ animationDelay: "0.5s" }}
          />
        </div>

        {/* Content protection overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/5" />
      </div>
    ),
    className:
      "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3 group hover:scale-[1.01] transition-all duration-300 relative",
  },
  {
    Icon: FileText,
    name: "Custom Problem Sheets",
    description:
      "Create and manage personalized coding sheets with up to 400 problems, filtered by tags and difficulty.",
    href: "/dashboard",
    cta: "Create Sheet",
    background: (
      <div className="absolute inset-0 overflow-hidden">
        {/* Matrix background */}
        <div className="absolute -right-12 -top-12 w-56 h-56 bg-gradient-to-br from-purple-600/25 via-violet-500/20 to-indigo-500/25 blur-3xl rounded-full animate-pulse" />
        <div
          className="absolute -left-8 -bottom-8 w-40 h-40 bg-gradient-to-tr from-pink-500/15 to-purple-500/20 blur-3xl rounded-full animate-pulse"
          style={{ animationDelay: "1s" }}
        />

        {/* Corner code elements */}
        <div className="absolute top-3 left-3 text-xs text-purple-400/60 font-mono animate-bounce">
          {"{"}
        </div>
        <div
          className="absolute top-3 right-3 text-xs text-violet-400/60 font-mono animate-bounce"
          style={{ animationDelay: "0.3s" }}
        >
          {"}"}
        </div>
        <div
          className="absolute bottom-3 left-3 text-xs text-indigo-400/60 font-mono animate-bounce"
          style={{ animationDelay: "0.6s" }}
        >
          []
        </div>
        <div
          className="absolute bottom-3 right-3 text-xs text-pink-400/60 font-mono animate-bounce"
          style={{ animationDelay: "0.9s" }}
        >
          {"()"}
        </div>

        {/* Edge progress bars */}
        <div className="absolute bottom-2 left-6 right-6 flex space-x-1">
          <div className="flex-1 h-0.5 bg-purple-400/40 rounded-full animate-pulse" />
          <div
            className="flex-1 h-0.5 bg-violet-400/40 rounded-full animate-pulse"
            style={{ animationDelay: "0.2s" }}
          />
          <div
            className="flex-1 h-0.5 bg-indigo-400/40 rounded-full animate-pulse"
            style={{ animationDelay: "0.4s" }}
          />
        </div>

        {/* Sparkle in corner */}
        <Sparkles className="absolute top-4 right-4 w-3 h-3 text-purple-400/60 animate-ping" />

        {/* Content protection */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/5" />
      </div>
    ),
    className:
      "lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4 group hover:scale-[1.01] transition-all duration-300 relative",
  },
  {
    Icon: Activity,
    name: "Performance Analytics",
    description:
      "Get detailed insights with Productivity Index Score and Weekly Coding Consistency tracking.",
    href: "/dashboard",
    cta: "View Analytics",
    background: (
      <div className="absolute inset-0 overflow-hidden">
        {/* Analytics background */}
        <div className="absolute -right-16 -top-16 w-64 h-64 bg-gradient-to-br from-purple-500/25 via-pink-500/20 to-red-500/25 blur-3xl rounded-full animate-pulse" />
        <div
          className="absolute -left-8 -bottom-8 w-44 h-44 bg-gradient-to-tr from-indigo-500/15 to-purple-500/20 blur-3xl rounded-full animate-pulse"
          style={{ animationDelay: "0.7s" }}
        />

        {/* Corner analytics indicators */}
        <div className="absolute top-4 left-4 w-8 h-4 border border-purple-400/40 rounded-sm animate-pulse" />
        <div
          className="absolute top-4 right-4 w-8 h-4 border border-pink-400/40 rounded-sm animate-pulse"
          style={{ animationDelay: "0.3s" }}
        />

        {/* Activity bars */}
        <div className="absolute top-6 left-6 flex space-x-1">
          <div className="w-1 h-3 bg-purple-400/60 rounded-full animate-bounce" />
          <div
            className="w-1 h-4 bg-pink-400/60 rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}
          />
          <div
            className="w-1 h-2 bg-red-400/60 rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}
          />
        </div>

        {/* Corner labels */}
        <div className="absolute bottom-3 left-3 text-xs text-purple-400/70 font-mono">
          Score
        </div>
        <div className="absolute bottom-3 right-3 text-xs text-pink-400/70 font-mono">
          Trend
        </div>

        {/* Content protection */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/5" />
      </div>
    ),
    className:
      "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2 group hover:scale-[1.01] transition-all duration-300 relative",
  },
  {
    Icon: Network,
    name: "Data Structure Visualizers",
    description:
      "Interactive visualizations for binary trees, graphs, and other data structures to enhance learning.",
    href: "/visualize",
    cta: "Explore Visualizers",
    background: (
      <div className="absolute inset-0 overflow-hidden">
        {/* Network background */}
        <div
          className="absolute -right-16 -top-16 w-64 h-64 bg-gradient-to-br from-cyan-500/25 via-blue-500/20 to-teal-500/25 blur-3xl rounded-full animate-spin"
          style={{ animationDuration: "25s" }}
        />
        <div
          className="absolute -left-12 -bottom-12 w-48 h-48 bg-gradient-to-tr from-emerald-500/15 to-cyan-500/20 blur-3xl rounded-full animate-spin"
          style={{ animationDuration: "20s", animationDirection: "reverse" }}
        />

        {/* Corner network nodes */}
        <div className="absolute top-4 left-4 w-2 h-2 bg-cyan-400/70 rounded-full animate-pulse" />
        <div
          className="absolute top-6 right-6 w-2 h-2 bg-blue-400/70 rounded-full animate-pulse"
          style={{ animationDelay: "0.2s" }}
        />
        <div
          className="absolute bottom-6 left-6 w-2 h-2 bg-teal-400/70 rounded-full animate-pulse"
          style={{ animationDelay: "0.4s" }}
        />
        <div
          className="absolute bottom-4 right-4 w-2 h-2 bg-emerald-400/70 rounded-full animate-pulse"
          style={{ animationDelay: "0.6s" }}
        />

        {/* Connection lines in corners */}
        <div className="absolute top-5 left-6 w-8 h-px bg-gradient-to-r from-cyan-400/50 to-blue-400/50 animate-pulse transform rotate-12" />
        <div
          className="absolute bottom-6 left-8 w-6 h-px bg-gradient-to-r from-teal-400/50 to-emerald-400/50 animate-pulse transform -rotate-12"
          style={{ animationDelay: "0.3s" }}
        />

        {/* Tiny floating data points */}
        <div className="absolute top-8 left-8 w-1 h-1 bg-cyan-300/60 rounded-full animate-ping" />
        <div
          className="absolute bottom-8 right-8 w-1 h-1 bg-blue-300/60 rounded-full animate-ping"
          style={{ animationDelay: "0.5s" }}
        />

        {/* Traversal indicator */}
        <div className="absolute top-6 right-8 w-1.5 h-1.5 bg-yellow-400/70 rounded-full animate-ping" />

        {/* Content protection */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/5" />
      </div>
    ),
    className:
      "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4 group hover:scale-[1.01] transition-all duration-300 relative",
  },
];

export function BentoDemo() {
  return (
    <div className="relative">
      {/* Subtle global ambient particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute top-1/4 left-1/4 w-1 h-1 bg-blue-400/20 rounded-full animate-ping"
          style={{ animationDelay: "0s", animationDuration: "4s" }}
        />
        <div
          className="absolute top-3/4 right-1/4 w-0.5 h-0.5 bg-purple-400/20 rounded-full animate-ping"
          style={{ animationDelay: "2s", animationDuration: "3s" }}
        />
        <div
          className="absolute bottom-1/4 left-3/4 w-1 h-1 bg-green-400/20 rounded-full animate-ping"
          style={{ animationDelay: "1s", animationDuration: "5s" }}
        />
      </div>

      <BentoGrid className="lg:grid-rows-3 gap-4">
        {features.map((feature) => (
          <BentoCard
            key={feature.name}
            {...feature}
            className={`${feature.className} transform transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/20 hover:-translate-y-1 group relative overflow-hidden backdrop-blur-sm border border-white/10`}
          />
        ))}
      </BentoGrid>

      {/* Very subtle ambient lighting */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/3 via-transparent to-purple-500/3 pointer-events-none rounded-3xl" />
    </div>
  );
}
