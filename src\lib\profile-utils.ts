// ============================================================================
// PROFILE UTILITY FUNCTIONS
// ============================================================================
// Utility functions for formatting and displaying user profile data

/**
 * Get color for Codeforces rating based on official color scheme
 */
export const getRatingColor = (rating?: number): string => {
  if (!rating) return "#808080"; // Unrated (gray)
  if (rating >= 3000) return "#FF0000"; // Legendary Grandmaster (red)
  if (rating >= 2600) return "#FF8C00"; // International Grandmaster (orange)
  if (rating >= 2400) return "#FF0000"; // Grandmaster (red)
  if (rating >= 2300) return "#FF8C00"; // International Master (orange)
  if (rating >= 2100) return "#FF8C00"; // Master (orange)
  if (rating >= 1900) return "#AA00AA"; // Candidate Master (violet)
  if (rating >= 1600) return "#0000FF"; // Expert (blue)
  if (rating >= 1400) return "#03A89E"; // Specialist (cyan)
  if (rating >= 1200) return "#008000"; // Pupil (green)
  return "#808080"; // Newbie (gray)
};

/**
 * Get rank title color for display
 */
export const getRankColor = (rank?: string): string => {
  if (!rank) return "#808080";

  const lowerRank = rank.toLowerCase();
  if (lowerRank.includes("legendary")) return "#FF0000";
  if (lowerRank.includes("international grandmaster")) return "#FF8C00";
  if (lowerRank.includes("grandmaster")) return "#FF0000";
  if (lowerRank.includes("international master")) return "#FF8C00";
  if (lowerRank.includes("master")) return "#FF8C00";
  if (lowerRank.includes("candidate master")) return "#AA00AA";
  if (lowerRank.includes("expert")) return "#0000FF";
  if (lowerRank.includes("specialist")) return "#03A89E";
  if (lowerRank.includes("pupil")) return "#008000";
  return "#808080"; // Newbie or unrated
};

/**
 * Format date for display (e.g., "Member since March 2023")
 */
export const formatMemberSince = (date: string | Date): string => {
  if (!date) return "Unknown";

  const memberDate = new Date(date);

  // Check if the date is valid
  if (isNaN(memberDate.getTime())) {
    return "Unknown";
  }

  return memberDate.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
  });
};

/**
 * Format last online time for display
 */
export const formatLastOnline = (timestamp: number): string => {
  const lastOnline = new Date(timestamp * 1000);
  const now = new Date();
  const diffMs = now.getTime() - lastOnline.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor(diffMs / (1000 * 60));

  if (diffDays > 30) {
    return lastOnline.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } else if (diffDays > 0) {
    return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;
  } else if (diffHours > 0) {
    return `${diffHours} hour${diffHours > 1 ? "s" : ""} ago`;
  } else if (diffMinutes > 0) {
    return `${diffMinutes} minute${diffMinutes > 1 ? "s" : ""} ago`;
  } else {
    return "Just now";
  }
};

/**
 * Format registration time for display
 */
export const formatRegistrationDate = (timestamp: number): string => {
  const regDate = new Date(timestamp * 1000);
  return regDate.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

/**
 * Get achievement badge based on rating or other criteria
 */
export const getAchievementBadge = (rating?: number, maxRating?: number) => {
  const currentRating = rating || 0;
  const peak = maxRating || currentRating;

  if (peak >= 3000)
    return { text: "Legendary", color: "from-red-500 to-red-600" };
  if (peak >= 2600)
    return { text: "International GM", color: "from-orange-500 to-red-500" };
  if (peak >= 2400)
    return { text: "Grandmaster", color: "from-red-500 to-red-600" };
  if (peak >= 2300)
    return {
      text: "International Master",
      color: "from-orange-500 to-yellow-500",
    };
  if (peak >= 2100)
    return { text: "Master", color: "from-yellow-500 to-yellow-600" };
  if (peak >= 1900)
    return { text: "Candidate Master", color: "from-purple-500 to-pink-500" };
  if (peak >= 1600)
    return { text: "Expert", color: "from-blue-500 to-blue-600" };
  if (peak >= 1400)
    return { text: "Specialist", color: "from-cyan-500 to-cyan-600" };
  if (peak >= 1200)
    return { text: "Pupil", color: "from-green-500 to-green-600" };
  return { text: "Newbie", color: "from-gray-500 to-gray-600" };
};

/**
 * Calculate progress percentage between rating ranges
 */
export const getRatingProgress = (
  rating?: number
): { percentage: number; nextRank: string; pointsNeeded: number } => {
  if (!rating) return { percentage: 0, nextRank: "Pupil", pointsNeeded: 1200 };

  const ranges = [
    { min: 0, max: 1199, name: "Newbie" },
    { min: 1200, max: 1399, name: "Pupil" },
    { min: 1400, max: 1599, name: "Specialist" },
    { min: 1600, max: 1899, name: "Expert" },
    { min: 1900, max: 2099, name: "Candidate Master" },
    { min: 2100, max: 2299, name: "Master" },
    { min: 2300, max: 2399, name: "International Master" },
    { min: 2400, max: 2599, name: "Grandmaster" },
    { min: 2600, max: 2999, name: "International Grandmaster" },
    { min: 3000, max: Infinity, name: "Legendary Grandmaster" },
  ];

  for (let i = 0; i < ranges.length; i++) {
    const range = ranges[i];
    if (rating >= range.min && rating < range.max) {
      const progress = ((rating - range.min) / (range.max - range.min)) * 100;
      const nextRank = i < ranges.length - 1 ? ranges[i + 1].name : "Max Rank";
      const pointsNeeded = range.max - rating;
      return { percentage: progress, nextRank, pointsNeeded };
    }
  }

  // If rating is above all ranges
  return { percentage: 100, nextRank: "Max Rank", pointsNeeded: 0 };
};

/**
 * Format large numbers with appropriate suffixes
 */
export const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
};

/**
 * Get contribution color based on value
 */
export const getContributionColor = (contribution: number): string => {
  if (contribution > 0) return "#00AA00"; // Green for positive
  if (contribution < 0) return "#FF0000"; // Red for negative
  return "#808080"; // Gray for zero
};
