"use client";

import { TwoPartModal } from "@/components/ui/reusable-modal";
import { useAuth } from "@/lib/auth-context";
import { createClient } from "@/lib/supabase/client";
import { User } from "@supabase/supabase-js";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Calendar,
  Clock,
  Code,
  Loader2,
  LogOut,
  Mail,
  Settings,
  Shield,
  UserCheck,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const ProfilePage = () => {
  const { user, loading } = useAuth();
  const [userDetails, setUserDetails] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();
  const supabase = createClient();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!loading && !user) {
      router.push("/login");
    } else if (user) {
      setUserDetails(user);
    }
  }, [user, loading, router]);

  const { data: isCodeforcesHandleVerified, isLoading: isVerificationLoading } =
    useQuery({
      queryKey: ["isVerified", user?.email],
      queryFn: async () => {
        if (!user?.email) {
          throw new Error("User email not found");
        }
        const response = await fetch(`/api/codeforces/verifyHandle`);
        // console.log(response);
        if (!response.ok) {
          return false;
        }
        return true;
      },
      enabled: !!user?.email,
      staleTime: 0, // No caching - always fetch fresh data
      gcTime: 5 * 60 * 1000, // Don't keep in cache
      refetchOnMount: true, // Always refetch on mount
      refetchOnWindowFocus: false, // Refetch when window gains focus
      retry: 1,
      retryDelay: 1000,
    });

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/");
  };

  // Modal handlers
  const handleModalComplete = (_handle: string) => {
    // console.log("Modal completed with handle:", handle);
    // Invalidate the verification query to update UI immediately
    queryClient.invalidateQueries({ queryKey: ["isVerified", user?.email] });
  };

  const handleVerificationSuccess = (_handle: string) => {
    // console.log("Verification successful for:", handle);
    // Invalidate the verification query to update UI immediately
    queryClient.invalidateQueries({ queryKey: ["isVerified", user?.email] });
  };

  const handleVerificationFailed = (_handle: string, _reason: string) => {
    // console.log("Verification failed for:", handle, "Reason:", reason);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user || !userDetails) {
    return null;
  }

  return (
    <div className="min-h-screen relative bg-black/[0.96] antialiased bg-grid-white/[0.02] overflow-hidden">
      {/* Beautiful gradient background matching home page style */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[600px] w-[600px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-30 blur-3xl rounded-full -z-10"></div>
      <div className="absolute top-1/3 right-1/4 h-[400px] w-[400px] bg-gradient-to-br from-purple-800/40 to-cyan-800/40 opacity-20 blur-3xl rounded-full -z-10"></div>

      {/* Animated grid pattern */}
      <div
        className="absolute inset-0 bg-grid-white/[0.02] animate-grid-move-hero"
        style={{
          backgroundSize: "20px 20px",
        }}
      />

      <div className="container mx-auto p-6 max-w-6xl mt-16 relative z-10">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-3 tracking-wide">
            <span className="bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
              My
            </span>{" "}
            <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-500 bg-clip-text text-transparent">
              Profile
            </span>
          </h1>
          <p className="text-gray-400 text-lg font-light">
            Manage your account and preferences
          </p>
        </div>

        {/* Main Profile Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card - Left Column */}
          <div className="lg:col-span-1">
            <div className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 rounded-2xl p-8 shadow-xl">
              {/* Profile Avatar Section */}
              <div className="text-center mb-8">
                <div className="relative inline-block">
                  {userDetails.user_metadata?.avatar_url ? (
                    <div className="relative">
                      <Image
                        src={userDetails.user_metadata.avatar_url}
                        alt={userDetails.email ?? "User avatar"}
                        width={120}
                        height={120}
                        className="rounded-full border-4 border-blue-500/30 shadow-lg"
                      />
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-500/20"></div>
                    </div>
                  ) : (
                    <div className="w-30 h-30 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-full flex items-center justify-center border-4 border-blue-500/30 shadow-lg">
                      <span className="text-white text-4xl font-bold">
                        {userDetails.email?.charAt(0).toUpperCase() || "U"}
                      </span>
                    </div>
                  )}
                </div>

                <h2 className="text-2xl font-bold text-white mt-4 mb-2">
                  {userDetails.user_metadata?.full_name ||
                    userDetails.user_metadata?.name ||
                    "MyCPTrainer User"}
                </h2>

                <p className="text-gray-400 text-sm">{userDetails.email}</p>

                {/* Status Badge */}
                <div className="mt-4">
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-500/30">
                    <UserCheck className="w-3 h-3 mr-1" />
                    Active Account
                  </span>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-3">
                <button
                  onClick={() => router.push("/dashboard")}
                  className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-blue-600/80 to-cyan-600/80 hover:from-blue-600 hover:to-cyan-600 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25"
                >
                  <Settings className="w-4 h-4" />
                  Dashboard
                </button>

                <button
                  onClick={handleSignOut}
                  className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-red-600/80 to-red-700/80 hover:from-red-600 hover:to-red-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-red-500/25"
                >
                  <LogOut className="w-4 h-4" />
                  Sign Out
                </button>
              </div>
            </div>
          </div>

          {/* Account Details - Right Columns */}
          <div className="lg:col-span-2 space-y-6">
            {/* Account Information */}
            <div className="relative bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-900/40 backdrop-blur-lg border border-slate-600/30 rounded-2xl p-8 shadow-xl">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-400" />
                Account Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Mail className="w-5 h-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-400">Email Address</p>
                      <p className="text-white font-medium">
                        {userDetails.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Calendar className="w-5 h-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-400">Member Since</p>
                      <p className="text-white font-medium">
                        {new Date(userDetails.created_at).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-400">Last Sign In</p>
                      <p className="text-white font-medium">
                        {userDetails.last_sign_in_at
                          ? new Date(
                              userDetails.last_sign_in_at
                            ).toLocaleDateString("en-US", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            })
                          : "Never"}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <UserCheck className="w-5 h-5 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-sm text-gray-400">Email Verified</p>
                      <p className="text-white font-medium">
                        {userDetails.email_confirmed_at ? (
                          <span className="text-green-400">✓ Verified</span>
                        ) : (
                          <span className="text-yellow-400">⚠ Pending</span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Verify Codeforces Profile */}
            <div className="relative bg-gradient-to-br from-slate-900/40 via-slate-800/30 to-slate-900/40 backdrop-blur-lg border border-slate-600/30 rounded-2xl p-8 shadow-xl">
              <h3 className="text-xl font-bold text-white mb-6">
                Verify Codeforces Profile
              </h3>

              {isVerificationLoading ? (
                <div className="w-full flex items-center gap-4 p-4 bg-slate-800/50 rounded-xl border border-slate-600/20">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                    <Loader2 className="w-6 h-6 text-white animate-spin" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">Codeforces</p>
                    <p className="text-gray-400 text-sm">
                      Checking verification status...
                    </p>
                  </div>
                </div>
              ) : isCodeforcesHandleVerified ? (
                <div className="w-full flex items-center gap-4 p-4 bg-green-900/20 rounded-xl border border-green-500/30">
                  <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-green-700 rounded-lg flex items-center justify-center">
                    <UserCheck className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-left flex-1">
                    <p className="text-white font-medium flex items-center gap-2">
                      Codeforces
                      <span className="text-green-400 text-lg">✓</span>
                    </p>
                    <p className="text-green-400 text-sm">
                      Your codeforces profile is verified
                    </p>
                  </div>
                </div>
              ) : (
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="w-full flex items-center gap-4 p-4 bg-slate-800/50 hover:bg-slate-700/50 rounded-xl border border-slate-600/20 hover:border-purple-500/30 transition-all duration-300 group"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Code className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-left">
                    <p className="text-white font-medium">Codeforces</p>
                    <p className="text-gray-400 text-sm">
                      Verify your competitive programming profile
                    </p>
                  </div>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Two-Part Modal */}
      <TwoPartModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="CodeForces Verification"
        onComplete={handleModalComplete}
        onVerificationSuccess={handleVerificationSuccess}
        onVerificationFailed={handleVerificationFailed}
      />
    </div>
  );
};

export default ProfilePage;
