import type { CodeforcesSubmission } from "@/lib/codeforces";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

// ============================================================================
// CODEFORCES SUBMISSIONS REACT QUERY HOOK
// ============================================================================
// This hook fetches and caches Codeforces submissions using React Query
// Supports filtering by date and limiting the number of results
// Only fetches submissions with verdict "OK" (solved problems)

// Response structure from our submissions API endpoint
interface SubmissionResponse {
  status: string;
  result: {
    submissions: CodeforcesSubmission[]; // Array of solved submissions
    totalSolved: number; // Total number of solved problems (all time)
    totalSolvedOnDate: number | null; // Number solved on specific date (if date filter applied)
    totalSolvedInRange: number | null; // Number solved in date range (if range filter applied)
    handle: string; // Username
    dateFilter: string | null; // Applied date filter (YYYY-MM-DD format)
    startDate: string | null; // Start date for range filtering
    endDate: string | null; // End date for range filtering
    dateRange: {
      // Unix timestamp range for the filtered date/range
      startOfDay: number;
      endOfDay: number;
    } | null;
  };
}

// Parameters for the submissions hook
interface UseCodeforcesSubmissionsParams {
  handle: string; // Codeforces username
  count?: number; // Number of submissions to return (default: 20)
  dateFilter?: string; // Optional date filter in YYYY-MM-DD format
  enabled?: boolean; // Whether to enable the query (default: true)
}

// Main hook for fetching Codeforces submissions with optional date filtering
export const useCodeforcesSubmissions = ({
  handle,
  count = 20,
  dateFilter,
  enabled = true,
}: UseCodeforcesSubmissionsParams) => {
  return useQuery({
    // Unique query key for caching - includes all parameters that affect the result
    queryKey: ["codeforces-submissions", handle, count, dateFilter],
    queryFn: async (): Promise<SubmissionResponse> => {
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      // Build URL with parameters
      let url = `/api/codeforces/submissions?handle=${encodeURIComponent(
        handle
      )}&count=${count}`;
      if (dateFilter) {
        url += `&date=${encodeURIComponent(dateFilter)}`;
      }

      const response = await axios.get<SubmissionResponse>(url);

      if (response.data.status !== "OK") {
        throw new Error("Failed to fetch submissions");
      }

      return response.data;
    },
    enabled: enabled && !!handle.trim(),
    staleTime: 5 * 60 * 1000, // 5 minutes - submissions don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx)
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      // Retry up to 3 times for server errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};

// Additional hook for getting submissions without date filter (for comparison)
export const useAllCodeforcesSubmissions = (handle: string, count = 20) => {
  return useCodeforcesSubmissions({
    handle,
    count,
    enabled: !!handle.trim(),
  });
};

// Hook specifically for date-filtered submissions
export const useCodeforcesSubmissionsByDate = (
  handle: string,
  date: string,
  count = 20
) => {
  return useCodeforcesSubmissions({
    handle,
    count,
    dateFilter: date,
    enabled: !!handle.trim() && !!date,
  });
};

// ============================================================================
// ALL SOLVED QUESTIONS HOOK
// ============================================================================
// Hook for fetching all solved questions for a user without any constraints
// This is used to show comprehensive problem-solving statistics when a user is searched

interface UseAllSolvedQuestionsParams {
  handle: string; // Codeforces username
  enabled?: boolean; // Whether to enable the query (default: true)
}

// Hook for fetching all solved questions for a user
export const useAllSolvedQuestions = ({
  handle,
  enabled = true,
}: UseAllSolvedQuestionsParams) => {
  return useQuery({
    // Unique query key for caching - includes handle to cache per user
    queryKey: ["codeforces-all-solved", handle],

    // Function that actually fetches the data
    queryFn: async (): Promise<SubmissionResponse> => {
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }

      // Fetch ALL solved submissions without any count constraints
      // The API now uses pagination internally to fetch all submissions
      const url = `/api/codeforces/submissions?handle=${encodeURIComponent(
        handle
      )}`; // No count parameter - API will fetch ALL submissions

      const response = await axios.get<SubmissionResponse>(url);

      if (response.data.status !== "OK") {
        throw new Error("Failed to fetch all solved questions");
      }

      return response.data;
    },

    // Only run the query if enabled and handle is provided
    enabled: enabled && !!handle.trim(),

    // Cache configuration - all solved questions don't change frequently
    staleTime: 15 * 60 * 1000, // Consider data fresh for 15 minutes
    gcTime: 60 * 60 * 1000, // Keep in cache for 1 hour after component unmounts

    // Smart retry logic
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx) - these are usually permanent
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      // Retry up to 3 times for server errors (5xx) or network issues
      return failureCount < 3;
    },

    // Exponential backoff for retries (1s, 2s, 4s, max 30s)
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// ============================================================================
// DATE RANGE SUBMISSIONS HOOK
// ============================================================================
// Hook specifically for fetching submissions within a date range
// This is used when users select a range on the rating graph and want to see all problems solved in that period

interface UseCodeforcesSubmissionsByRangeParams {
  handle: string; // Codeforces username
  startDate: string; // Start date in YYYY-MM-DD format
  endDate: string; // End date in YYYY-MM-DD format
  count?: number; // Number of submissions to return (default: 1000 for range queries)
  enabled?: boolean; // Whether to enable the query (default: true)
}

export const useCodeforcesSubmissionsByRange = ({
  handle,
  startDate,
  endDate,
  count = 1000, // Higher default for range queries to get all problems in range
  enabled = true,
}: UseCodeforcesSubmissionsByRangeParams) => {
  return useQuery({
    // Unique query key for caching - includes all parameters that affect the result
    queryKey: [
      "codeforces-submissions-range",
      handle,
      startDate,
      endDate,
      count,
    ],
    queryFn: async (): Promise<SubmissionResponse> => {
      if (!handle.trim()) {
        throw new Error("Handle is required");
      }
      if (!startDate || !endDate) {
        throw new Error("Both startDate and endDate are required");
      }

      // Build URL with date range parameters
      const url = `/api/codeforces/submissions?handle=${encodeURIComponent(
        handle
      )}&count=${count}&startDate=${encodeURIComponent(
        startDate
      )}&endDate=${encodeURIComponent(endDate)}`;

      const response = await axios.get<SubmissionResponse>(url);

      if (response.data.status !== "OK") {
        throw new Error("Failed to fetch submissions");
      }

      return response.data;
    },
    enabled: enabled && !!handle.trim() && !!startDate && !!endDate,
    staleTime: 10 * 60 * 1000, // 10 minutes - range data changes less frequently
    gcTime: 20 * 60 * 1000, // 20 minutes - keep in cache longer for range queries
    retry: (failureCount, error: any) => {
      // Don't retry on client errors (4xx)
      if (error.response?.status >= 400 && error.response?.status < 500) {
        return false;
      }
      // Retry up to 3 times for server errors
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
};
