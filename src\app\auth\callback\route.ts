import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const next = searchParams.get("next") ?? "/";
  const isTab = searchParams.get("tab") === "true"; // Check if this is a tab-based auth

  if (code) {
    const supabase = await createClient();

    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      // Get the authenticated user's information
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();

      // Authentication successful - user is now logged in via Supabase
      // Database user record creation has been removed - users need to verify manually

      // If this is a tab-based authentication, redirect to a page that closes the tab
      if (isTab) {
        redirect("/auth/tab-success");
      }

      // redirect user to specified redirect URL or root of app
      redirect(next);
    }
  }

  // redirect the user to an error page with some instructions
  redirect("/error?message=Authentication failed");
}
