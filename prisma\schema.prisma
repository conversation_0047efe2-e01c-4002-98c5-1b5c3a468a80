
generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

/// This model contains row level security and requires additional setup for migrations. Visit https://pris.ly/d/row-level-security for more info.
model Users {
  id                  Int      @id @default(autoincrement())
  handle              String?  @unique
  chat_id             String?
  rating              Int?
  rank                String?
  streak              Int?     @default(0)
  easy                String?  @default("easy")
  hard                String?  @default("hard")
  isAd<PERSON>? @default(false)
  sendReminders       Boolean? @default(true)
  allotedAt           DateTime @default(dbgenerated("(now() - '06:00:00'::interval)")) @db.Timestamptz(6)
  streak_update       Boolean? @default(false)
  Inactive            Boolean? @default(false)
  missedStreak        Int?     @default(0)
  training_problem    String?  @default("NA")
  training_rating     Int      @default(0)
  training_streak     Int      @default(0)
  max_training_streak Int      @default(0)
  train_time          DateTime @default(now()) @db.Timestamptz(6)
  createdAt           DateTime @default(now()) @db.Timestamptz(6)
  updatedAt           DateTime @default(now()) @updatedAt @db.Timestamptz(6)
  email               String?  @unique
  maxSheetSlots       Int?     @default(1)
  sheets              Sheet[]
  orders              Order[]
  defaultSlot         Int?  
  Premium             Boolean? @default(false)   

  @@unique([chat_id, handle])
  @@index([email])
  @@index([handle])
  @@index([createdAt])
}

model Sheet {
  id        String   @id @default(cuid())
  name      String?  @default("My Custom Sheet")
  problems  Json
  userId    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  user      Users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

model Order{
  id                String @id @default(cuid()) 
  orderId           String @unique
  razorpayPaymentId String @unique
  user              Users @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId            Int
  email             String 
  createdAt         DateTime @default(now())

}

