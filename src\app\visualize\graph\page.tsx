"use client";

import D3GraphVisualizer, {
  GraphData,
  GraphLink,
  GraphNode,
} from "@/components/visualizations/D3GraphVisualizer";
import { AlertCircle, CheckCircle, GitFork } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import {
  generateSampleEdgeList,
  parseEdgeList,
  ParseResult,
} from "../../../utils/graphParser";

const initialEdgeListString = generateSampleEdgeList("simple");

// Interface for validation messages
interface ValidationMessage {
  type: "error" | "warning" | "info";
  message: string;
}

const GraphVisualizerPage = () => {
  // State management for the D3 graph
  const [graphData, setGraphData] = useState<GraphData>({
    nodes: [],
    links: [],
  });
  const [inputValue, setInputValue] = useState(initialEdgeListString);
  const [validationMessages, setValidationMessages] = useState<
    ValidationMessage[]
  >([]);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [selectedLink, setSelectedLink] = useState<GraphLink | null>(null);

  // State for handling SSR/client-side rendering differences
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const [isClient, setIsClient] = useState(false);

  /**
   * Parse input and update graph data
   * Handles validation and error reporting
   */
  const buildGraph = useCallback((input: string) => {
    const result: ParseResult = parseEdgeList(input);

    // Clear previous messages
    setValidationMessages([]);

    if (result.success && result.data) {
      setGraphData(result.data);

      // Add any warnings as info messages
      if (result.warnings) {
        const warningMessages: ValidationMessage[] = result.warnings.map(
          (warning) => ({
            type: "warning",
            message: warning,
          })
        );
        setValidationMessages(warningMessages);
      }

      // Add success message
      setValidationMessages((prev) => [
        ...prev,
        {
          type: "info",
          message: `Graph built successfully: ${result?.data?.nodes.length} nodes, ${result?.data?.links.length} edges`,
        },
      ]);
    } else {
      // Handle errors
      setValidationMessages([
        {
          type: "error",
          message: result.error || "Unknown error occurred",
        },
      ]);

      // Keep previous graph data on error
    }
  }, []);

  // Handle client-side mounting and window resizing
  useEffect(() => {
    setIsClient(true);

    const updateDimensions = () => {
      setDimensions({
        width:
          window.innerWidth > 768 ? window.innerWidth - 400 : window.innerWidth,
        height: window.innerHeight,
      });
    };

    updateDimensions();
    window.addEventListener("resize", updateDimensions);

    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  // Initialize graph on component mount
  useEffect(() => {
    buildGraph(initialEdgeListString);
  }, [buildGraph]);

  /**
   * Handle manual graph building from input
   */
  const handleBuildGraph = () => {
    buildGraph(inputValue);
  };

  /**
   * Load sample graphs for demonstration
   */
  const loadSampleGraph = (type: "simple" | "complex" | "tree" | "cycle") => {
    const sampleData = generateSampleEdgeList(type);
    setInputValue(sampleData);
    buildGraph(sampleData);
  };

  /**
   * Handle node selection
   */
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
    setSelectedLink(null);
  };

  /**
   * Handle link selection
   */
  const handleLinkClick = (link: GraphLink) => {
    setSelectedLink(link);
    setSelectedNode(null);
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col pt-24">
      {/* Animated Heading */}
      <div className="text-center py-8 animate-fadeIn">
        <h1 className="text-4xl md:text-5xl font-light text-white drop-shadow-[0_0_15px_rgba(29,78,216,0.6)]">
          🔗 Graph Visualizer
        </h1>
      </div>

      {/* Main Content */}
      <div className="flex flex-col flex-1 gap-4 p-4">
        <div className="flex flex-col lg:flex-row flex-1 gap-4">
          {/* Left Panel - Graph Visualization */}
          <div className="relative h-[60vh] lg:h-auto lg:flex-1 bg-black rounded-lg overflow-hidden shadow-2xl border border-gray-800">
            {isClient ? (
              <D3GraphVisualizer
                data={graphData}
                width={dimensions.width}
                height={dimensions.height}
                onNodeClick={handleNodeClick}
                onLinkClick={handleLinkClick}
                className="w-full h-full"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-black">
                <div className="text-white text-lg">
                  Loading graph visualizer...
                </div>
              </div>
            )}

            {/* Selection info overlay */}
            {(selectedNode || selectedLink) && (
              <div className="absolute top-4 left-4 bg-gray-900 bg-opacity-90 backdrop-blur-sm border border-gray-600 rounded-lg p-3">
                {selectedNode && (
                  <div>
                    <h3 className="font-semibold text-blue-400">
                      Selected Node
                    </h3>
                    <p className="text-sm">ID: {selectedNode.id}</p>
                    <p className="text-sm">Label: {selectedNode.label}</p>
                  </div>
                )}
                {selectedLink && (
                  <div>
                    <h3 className="font-semibold text-green-400">
                      Selected Link
                    </h3>
                    <p className="text-sm">
                      {typeof selectedLink.source === "string"
                        ? selectedLink.source
                        : selectedLink.source.id}{" "}
                      →{" "}
                      {typeof selectedLink.target === "string"
                        ? selectedLink.target
                        : selectedLink.target.id}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Graph Stats Panel */}
            {graphData.nodes.length > 0 && (
              <div className="absolute top-4 right-4 bg-gray-900 bg-opacity-90 backdrop-blur-sm border border-gray-600 rounded-lg p-3">
                <div className="text-xs text-gray-300">
                  <div className="font-semibold mb-1">Graph Stats</div>
                  <div>Nodes: {graphData.nodes.length}</div>
                  <div>Edges: {graphData.links.length}</div>
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Input */}
          <div className="w-full lg:w-80 flex-shrink-0 bg-zinc-900 border border-gray-700 rounded-2xl p-6 flex flex-col space-y-6">
            <h2 className="text-lg font-bold text-white text-center">
              Graph Controls
            </h2>

            <div>
              <label
                htmlFor="edge-list-input"
                className="block text-sm font-medium text-gray-300 mb-2"
              >
                Edge List (source target)
              </label>
              <textarea
                id="edge-list-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full h-48 bg-[#111] border border-gray-700 rounded-xl p-3 text-white placeholder-gray-500 font-mono text-sm focus:ring-2 focus:ring-[#1d4ed8] focus:border-transparent transition-all duration-200 resize-none"
                placeholder="e.g.&#10;0 1&#10;1 2"
              />
            </div>

            {/* Validation Messages */}
            {validationMessages.length > 0 && (
              <div className="space-y-2">
                {validationMessages.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex items-center p-2 rounded-lg text-sm ${
                      msg.type === "error"
                        ? "bg-red-900 text-red-200 border border-red-700"
                        : msg.type === "warning"
                        ? "bg-yellow-900 text-yellow-200 border border-yellow-700"
                        : "bg-blue-900 text-blue-200 border border-blue-700"
                    }`}
                  >
                    {msg.type === "error" && (
                      <AlertCircle className="mr-2 h-4 w-4" />
                    )}
                    {msg.type === "warning" && (
                      <AlertCircle className="mr-2 h-4 w-4" />
                    )}
                    {msg.type === "info" && (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    {msg.message}
                  </div>
                ))}
              </div>
            )}

            {/* Control Buttons */}
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleBuildGraph}
                className="flex items-center justify-center rounded-full bg-[#1d4ed8] px-4 py-3 font-bold text-white transition-all duration-300 transform shadow-lg hover:bg-[#2563eb] hover:scale-105 active:scale-95 hover:shadow-[0_0_15px_rgba(29,78,216,0.6)]"
              >
                <GitFork className="mr-2 h-5 w-5" />
                Build Graph
              </button>

              {/* Sample Graph Buttons */}
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => loadSampleGraph("simple")}
                  className="flex items-center justify-center rounded-full bg-green-600 px-3 py-2 text-xs font-semibold text-white shadow-md hover:bg-green-700 hover:scale-105 active:scale-95 transition-all duration-200"
                >
                  Simple
                </button>
                <button
                  onClick={() => loadSampleGraph("complex")}
                  className="flex items-center justify-center rounded-full bg-purple-600 px-3 py-2 text-xs font-semibold text-white shadow-md hover:bg-purple-700 hover:scale-105 active:scale-95 transition-all duration-200"
                >
                  Complex
                </button>
                <button
                  onClick={() => loadSampleGraph("tree")}
                  className="flex items-center justify-center rounded-full bg-orange-600 px-3 py-2 text-xs font-semibold text-white shadow-md hover:bg-orange-700 hover:scale-105 active:scale-95 transition-all duration-200"
                >
                  Tree
                </button>
                <button
                  onClick={() => loadSampleGraph("cycle")}
                  className="flex items-center justify-center rounded-full bg-pink-600 px-3 py-2 text-xs font-semibold text-white shadow-md hover:bg-pink-700 hover:scale-105 active:scale-95 transition-all duration-200"
                >
                  Cycle
                </button>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-[#111] p-4 rounded-lg border border-gray-800 text-sm">
              <h3 className="text-xs font-semibold text-gray-300 mb-2">
                Instructions:
              </h3>
              <ul className="space-y-1 text-gray-400 text-xs font-mono">
                <li>• Enter edges as "source target" pairs</li>
                <li>• One edge per line</li>
                <li>• Supports both strings and numbers</li>
                <li>• Drag nodes to reposition</li>
                <li>• Zoom and pan with mouse</li>
                <li>• Click nodes/edges to select</li>
                <li>• Hover for tooltips</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraphVisualizerPage;
