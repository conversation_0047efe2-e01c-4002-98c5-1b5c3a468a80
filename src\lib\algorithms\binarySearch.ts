import { BinarySearchElement } from "../types";

/**
 * Standard Binary Search Algorithm
 * Searches for an exact target value in a sorted array
 * Returns the index if found, -1 if not found
 */
export function* standardBinarySearch(
  array: BinarySearchElement[],
  target: number
): Generator<BinarySearchElement[], void, void> {
  const arr = [...array];
  let left = 0;
  let right = arr.length - 1;

  // Clear all previous states
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isTarget = false;
    element.isComparing = false;
    element.isInRange = false;
    element.isFound = false;
    element.message = "";
  });

  // Initial state - show the full array
  for (let i = 0; i < arr.length; i++) {
    arr[i].isInRange = true;
  }
  arr[left].isLeft = true;
  arr[right].isRight = true;
  arr[left].message = "Left";
  arr[right].message = "Right";
  yield [...arr];

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);

    // Clear previous mid marker
    arr.forEach((element) => {
      element.isMid = false;
      element.isComparing = false;
      if (!element.isLeft && !element.isRight) {
        element.message = "";
      }
    });

    // Mark current mid element
    arr[mid].isMid = true;
    arr[mid].message = "Mid";
    yield [...arr];

    // Compare mid with target
    arr[mid].isComparing = true;
    if (arr[mid].value === target) {
      // Found the target
      arr[mid].isFound = true;
      arr[mid].isTarget = true;
      arr[mid].message = "Found!";

      // Clear other markers
      arr.forEach((element, index) => {
        if (index !== mid) {
          element.isLeft = false;
          element.isRight = false;
          element.isInRange = false;
          element.message = "";
        }
      });

      yield [...arr];
      return;
    } else if (arr[mid].value < target) {
      // Target is in the right half
      arr[mid].message = "< Target";
      yield [...arr];

      // Update search range
      left = mid + 1;

      // Clear out-of-range elements
      for (let i = 0; i <= mid; i++) {
        arr[i].isInRange = false;
        arr[i].isLeft = false;
        arr[i].isMid = false;
        arr[i].isComparing = false;
        arr[i].message = "";
      }

      if (left <= right) {
        arr[left].isLeft = true;
        arr[left].message = "Left";
        for (let i = left; i <= right; i++) {
          arr[i].isInRange = true;
        }
      }

      yield [...arr];
    } else {
      // Target is in the left half
      arr[mid].message = "> Target";
      yield [...arr];

      // Update search range
      right = mid - 1;

      // Clear out-of-range elements
      for (let i = mid; i < arr.length; i++) {
        arr[i].isInRange = false;
        arr[i].isRight = false;
        arr[i].isMid = false;
        arr[i].isComparing = false;
        arr[i].message = "";
      }

      if (left <= right) {
        arr[right].isRight = true;
        arr[right].message = "Right";
        for (let i = left; i <= right; i++) {
          arr[i].isInRange = true;
        }
      }

      yield [...arr];
    }
  }

  // Target not found
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isComparing = false;
    element.isInRange = false;
    element.message = "";
  });

  yield [...arr];
}

/**
 * Lower Bound Binary Search Algorithm
 * Finds the first index where value >= target
 * Returns the insertion point for the target
 */
export function* lowerBoundBinarySearch(
  array: BinarySearchElement[],
  target: number
): Generator<BinarySearchElement[], void, void> {
  const arr = [...array];
  let left = 0;
  let right = arr.length;

  // Clear all previous states
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isTarget = false;
    element.isComparing = false;
    element.isInRange = false;
    element.isFound = false;
    element.message = "";
  });

  // Initial state - show the full array
  for (let i = 0; i < arr.length; i++) {
    arr[i].isInRange = true;
  }
  if (left < arr.length) {
    arr[left].isLeft = true;
    arr[left].message = "Left";
  }
  // Show right boundary as the last element initially (conceptually right is at arr.length)
  if (arr.length > 0) {
    arr[arr.length - 1].isRight = true;
    arr[arr.length - 1].message = "Right (end)";
  }
  yield [...arr];

  while (left < right) {
    const mid = Math.floor((left + right) / 2);

    // Clear previous mid marker
    arr.forEach((element) => {
      element.isMid = false;
      element.isComparing = false;
      if (!element.isLeft && !element.isRight) {
        element.message = "";
      }
    });

    // Mark current mid element
    if (mid < arr.length) {
      arr[mid].isMid = true;
      arr[mid].message = "Mid";
      yield [...arr];

      // Compare mid with target
      arr[mid].isComparing = true;
      if (arr[mid].value >= target) {
        // Target could be at mid or to the left
        arr[mid].message = ">= Target";
        yield [...arr];

        right = mid;

        // Update range visualization
        for (let i = right; i < arr.length; i++) {
          arr[i].isInRange = false;
          arr[i].isRight = false;
          arr[i].message = "";
        }

        // Show right boundary
        if (right > 0 && right <= arr.length) {
          if (right < arr.length) {
            arr[right].isRight = true;
            arr[right].message = "Right";
          } else if (right === arr.length && arr.length > 0) {
            arr[arr.length - 1].isRight = true;
            arr[arr.length - 1].message = "Right (end)";
          }
        }

        if (left < arr.length) {
          for (let i = left; i < Math.min(right, arr.length); i++) {
            arr[i].isInRange = true;
          }
        }
      } else {
        // Target is to the right
        arr[mid].message = "< Target";
        yield [...arr];

        left = mid + 1;

        // Update range visualization
        for (let i = 0; i <= mid; i++) {
          arr[i].isInRange = false;
          arr[i].isLeft = false;
          arr[i].message = "";
        }

        if (left < arr.length) {
          arr[left].isLeft = true;
          arr[left].message = "Left";
          for (let i = left; i < Math.min(right, arr.length); i++) {
            arr[i].isInRange = true;
          }
        }

        // Show right boundary
        if (right > 0 && right <= arr.length) {
          if (right < arr.length) {
            arr[right].isRight = true;
            arr[right].message = "Right";
          } else if (right === arr.length && arr.length > 0) {
            arr[arr.length - 1].isRight = true;
            arr[arr.length - 1].message = "Right (end)";
          }
        }
      }

      yield [...arr];
    }
  }

  // Mark the result
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isComparing = false;
    element.isInRange = false;
    element.message = "";
  });

  if (left < arr.length) {
    arr[left].isFound = true;
    arr[left].message = "Lower Bound";
  }

  yield [...arr];
}

/**
 * Upper Bound Binary Search Algorithm
 * Finds the first index where value > target
 * Returns the insertion point after all occurrences of target
 */
export function* upperBoundBinarySearch(
  array: BinarySearchElement[],
  target: number
): Generator<BinarySearchElement[], void, void> {
  const arr = [...array];
  let left = 0;
  let right = arr.length;

  // Clear all previous states
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isTarget = false;
    element.isComparing = false;
    element.isInRange = false;
    element.isFound = false;
    element.message = "";
  });

  // Initial state - show the full array
  for (let i = 0; i < arr.length; i++) {
    arr[i].isInRange = true;
  }
  if (left < arr.length) {
    arr[left].isLeft = true;
    arr[left].message = "Left";
  }
  // Show right boundary as the last element initially (conceptually right is at arr.length)
  if (arr.length > 0) {
    arr[arr.length - 1].isRight = true;
    arr[arr.length - 1].message = "Right (end)";
  }
  yield [...arr];

  while (left < right) {
    const mid = Math.floor((left + right) / 2);

    // Clear previous mid marker
    arr.forEach((element) => {
      element.isMid = false;
      element.isComparing = false;
      if (!element.isLeft && !element.isRight) {
        element.message = "";
      }
    });

    // Mark current mid element
    if (mid < arr.length) {
      arr[mid].isMid = true;
      arr[mid].message = "Mid";
      yield [...arr];

      // Compare mid with target
      arr[mid].isComparing = true;
      if (arr[mid].value > target) {
        // Target is to the left
        arr[mid].message = "> Target";
        yield [...arr];

        right = mid;

        // Update range visualization
        for (let i = right; i < arr.length; i++) {
          arr[i].isInRange = false;
          arr[i].isRight = false;
          arr[i].message = "";
        }

        // Show right boundary
        if (right > 0 && right <= arr.length) {
          if (right < arr.length) {
            arr[right].isRight = true;
            arr[right].message = "Right";
          } else if (right === arr.length && arr.length > 0) {
            arr[arr.length - 1].isRight = true;
            arr[arr.length - 1].message = "Right (end)";
          }
        }

        if (left < arr.length) {
          for (let i = left; i < Math.min(right, arr.length); i++) {
            arr[i].isInRange = true;
          }
        }
      } else {
        // Target could be at mid or to the right
        arr[mid].message = "<= Target";
        yield [...arr];

        left = mid + 1;

        // Update range visualization
        for (let i = 0; i <= mid; i++) {
          arr[i].isInRange = false;
          arr[i].isLeft = false;
          arr[i].message = "";
        }

        if (left < arr.length) {
          arr[left].isLeft = true;
          arr[left].message = "Left";
          for (let i = left; i < Math.min(right, arr.length); i++) {
            arr[i].isInRange = true;
          }
        }

        // Show right boundary
        if (right > 0 && right <= arr.length) {
          if (right < arr.length) {
            arr[right].isRight = true;
            arr[right].message = "Right";
          } else if (right === arr.length && arr.length > 0) {
            arr[arr.length - 1].isRight = true;
            arr[arr.length - 1].message = "Right (end)";
          }
        }
      }

      yield [...arr];
    }
  }

  // Mark the result
  arr.forEach((element) => {
    element.isLeft = false;
    element.isRight = false;
    element.isMid = false;
    element.isComparing = false;
    element.isInRange = false;
    element.message = "";
  });

  if (left < arr.length) {
    arr[left].isFound = true;
    arr[left].message = "Upper Bound";
  }

  yield [...arr];
}
